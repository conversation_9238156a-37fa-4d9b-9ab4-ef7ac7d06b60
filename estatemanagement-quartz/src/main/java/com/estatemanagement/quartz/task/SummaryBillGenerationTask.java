package com.estatemanagement.quartz.task;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.estatemanagement.common.utils.spring.SpringUtils;
import com.estatemanagement.system.domain.SummaryBillGenerationConfig;
import com.estatemanagement.system.service.ICommunitySummaryBillService;
import com.estatemanagement.system.service.ISummaryBillGenerationConfigService;

/**
 * 汇总账单生成定时任务
 * 
 * <AUTHOR>
 */
@Component("summaryBillGenerationTask")
public class SummaryBillGenerationTask
{
    private static final Logger log = LoggerFactory.getLogger(SummaryBillGenerationTask.class);

    /**
     * 执行汇总账单自动生成任务
     */
    public void executeAutoGeneration()
    {
        log.info("开始执行汇总账单自动生成任务");
        
        try {
            ISummaryBillGenerationConfigService configService = SpringUtils.getBean(ISummaryBillGenerationConfigService.class);
            ICommunitySummaryBillService summaryBillService = SpringUtils.getBean(ICommunitySummaryBillService.class);
            
            // 获取所有启用的自动生成配置
            SummaryBillGenerationConfig queryConfig = new SummaryBillGenerationConfig();
            queryConfig.setStatus(1); // 启用状态
            queryConfig.setAutoGenerate(1); // 自动生成开启
            
            List<SummaryBillGenerationConfig> configs = configService.selectSummaryBillGenerationConfigList(queryConfig);
            
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            
            for (SummaryBillGenerationConfig config : configs) {
                try {
                    // 检查是否到了生成时间
                    if (config.getNextGenerateTime() != null && now.before(config.getNextGenerateTime())) {
                        continue;
                    }
                    
                    log.info("为小区 {} 自动生成汇总账单", config.getCommunityName());
                    
                    // 计算汇总时间段（上次生成时间到当前时间）
                    Date startDate = config.getLastGenerateTime();
                    Date endDate = now;
                    
                    // 如果是首次生成，使用默认时间段（当前时间前一个月）
                    if (startDate == null) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(now);
                        calendar.add(Calendar.DAY_OF_MONTH, -config.getIntervalDays());
                        startDate = calendar.getTime();
                    }
                    
                    // 检查是否已存在该时间段的汇总账单
                    if (!summaryBillService.checkSummaryBillExists(config.getCommunityId(), startDate, endDate)) {
                        // 生成汇总账单
                        String taskId = summaryBillService.generateSummaryBills(config.getCommunityId(), startDate, endDate);
                        log.info("小区 {} 汇总账单生成任务已启动，任务ID: {}", config.getCommunityName(), taskId);
                    } else {
                        log.info("小区 {} 在时间段 {} 到 {} 的汇总账单已存在，跳过生成", 
                                config.getCommunityName(), sdf.format(startDate), sdf.format(endDate));
                    }
                    
                    // 更新配置信息
                    config.setLastGenerateTime(now);
                    
                    // 计算下次生成时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(now);
                    calendar.add(Calendar.DAY_OF_MONTH, config.getIntervalDays());
                    config.setNextGenerateTime(calendar.getTime());
                    
                    configService.updateSummaryBillGenerationConfig(config);
                    
                } catch (Exception e) {
                    log.error("小区 {} 自动生成汇总账单失败: {}", config.getCommunityName(), e.getMessage(), e);
                    // 继续处理其他小区
                }
            }
            
            log.info("汇总账单自动生成任务执行完成，共处理 {} 个小区配置", configs.size());
            
        } catch (Exception e) {
            log.error("执行汇总账单自动生成任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动触发指定小区的汇总账单生成
     * 
     * @param communityId 小区ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public void manualGeneration(Long communityId, Date startDate, Date endDate)
    {
        log.info("手动触发小区 {} 的汇总账单生成，时间段: {} 到 {}", communityId, startDate, endDate);
        
        try {
            ICommunitySummaryBillService summaryBillService = SpringUtils.getBean(ICommunitySummaryBillService.class);
            
            String taskId = summaryBillService.generateSummaryBills(communityId, startDate, endDate);
            
            log.info("小区 {} 手动生成汇总账单任务已启动，任务ID: {}", communityId, taskId);
            
        } catch (Exception e) {
            log.error("小区 {} 手动生成汇总账单失败: {}", communityId, e.getMessage(), e);
            throw new RuntimeException("手动生成汇总账单失败: " + e.getMessage());
        }
    }
}
