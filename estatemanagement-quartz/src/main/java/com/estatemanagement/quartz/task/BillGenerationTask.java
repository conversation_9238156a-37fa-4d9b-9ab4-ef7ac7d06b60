package com.estatemanagement.quartz.task;

import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.common.utils.spring.SpringUtils;
import com.estatemanagement.system.domain.BillGenerationConfig;
import com.estatemanagement.system.service.IBillGenerationConfigService;
import com.estatemanagement.system.service.ICommunityBillService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 账单自动生成定时任务
 * 
 * <AUTHOR>
 */
@Component("billGenerationTask")
public class BillGenerationTask
{
    private static final Logger log = LoggerFactory.getLogger(BillGenerationTask.class);

    /**
     * 执行账单自动生成任务
     */
    public void executeAutoGeneration()
    {
        log.info("开始执行账单自动生成任务");
        
        try {
            IBillGenerationConfigService configService = SpringUtils.getBean(IBillGenerationConfigService.class);
            ICommunityBillService billService = SpringUtils.getBean(ICommunityBillService.class);
            
            // 获取所有启用的自动生成配置
            BillGenerationConfig queryConfig = new BillGenerationConfig();
            queryConfig.setStatus(1); // 启用状态
            queryConfig.setAutoGenerate(1); // 自动生成开启
            
            List<BillGenerationConfig> configs = configService.selectBillGenerationConfigList(queryConfig);
            
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            
            for (BillGenerationConfig config : configs) {
                try {
                    // 检查是否到了生成时间
                    if (config.getNextGenerateTime() != null && now.before(config.getNextGenerateTime())) {
                        continue;
                    }
                    
                    log.info("为小区 {} 自动生成账单", config.getCommunityName());
                    
                    // 生成账单
                    String billDate = sdf.format(now);
                    String taskId = billService.generateBills(config.getCommunityId(), billDate, config.getGenerateType());
                    
                    // 更新配置信息
                    config.setLastGenerateTime(now);
                    
                    // 计算下次生成时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(now);
                    calendar.add(Calendar.DAY_OF_MONTH, config.getIntervalDays());
                    config.setNextGenerateTime(calendar.getTime());
                    
                    configService.updateBillGenerationConfig(config);
                    
                    log.info("小区 {} 账单生成任务已启动，任务ID: {}", config.getCommunityName(), taskId);
                    
                } catch (Exception e) {
                    log.error("小区 {} 自动生成账单失败: {}", config.getCommunityName(), e.getMessage(), e);
                }
            }
            
        } catch (Exception e) {
            log.error("执行账单自动生成任务失败: {}", e.getMessage(), e);
        }
        
        log.info("账单自动生成任务执行完成");
    }

    /**
     * 手动触发指定小区的账单生成
     * 
     * @param communityId 小区ID
     * @param generateType 生成类型
     */
    public void manualGeneration(Long communityId, String generateType)
    {
        log.info("手动触发小区 {} 的账单生成，类型: {}", communityId, generateType);
        
        try {
            ICommunityBillService billService = SpringUtils.getBean(ICommunityBillService.class);
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String billDate = sdf.format(new Date());
            
            String taskId = billService.generateBills(communityId, billDate, generateType);
            
            log.info("小区 {} 手动生成账单任务已启动，任务ID: {}", communityId, taskId);
            
        } catch (Exception e) {
            log.error("小区 {} 手动生成账单失败: {}", communityId, e.getMessage(), e);
            throw new RuntimeException("手动生成账单失败: " + e.getMessage());
        }
    }

    /**
     * 测试任务（用于验证定时任务是否正常工作）
     */
    public void testTask()
    {
        log.info("账单生成定时任务测试执行 - {}", DateUtils.getTime());
    }
}
