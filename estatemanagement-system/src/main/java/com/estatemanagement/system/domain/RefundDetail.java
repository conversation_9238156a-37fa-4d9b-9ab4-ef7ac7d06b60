package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 退费明细对象 refund_detail
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Setter
@Getter
public class RefundDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 退费记录ID */
    @Excel(name = "退费记录ID")
    private Long refundId;

    /** 原收费明细ID */
    @Excel(name = "原收费明细ID")
    private Long originalPaymentDetailId;

    /** 费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金) */
    @Excel(name = "费用类型", readConverterExp = "1=物业费,2=停车费,3=卫生费,4=电梯费,5=滞纳金")
    private Integer feeType;

    /** 费用名称 */
    @Excel(name = "费用名称")
    private String feeName;

    /** 原支付金额 */
    @Excel(name = "原支付金额")
    private BigDecimal originalPaymentAmount;

    /** 退费金额 */
    @Excel(name = "退费金额")
    private BigDecimal refundAmount;

    /** 退费月数 */
    @Excel(name = "退费月数")
    private Long refundMonths;

    /** 退费天数 */
    @Excel(name = "退费天数")
    private Long refundDays;

    /** 原缴费周期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "原缴费周期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date originalPeriodStart;

    /** 原缴费周期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "原缴费周期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date originalPeriodEnd;

    /** 退费周期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退费周期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundPeriodStart;

    /** 退费周期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退费周期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundPeriodEnd;

    /** 剩余周期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "剩余周期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date remainingPeriodStart;

    /** 剩余周期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "剩余周期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date remainingPeriodEnd;

    /** 车牌号(停车费相关) */
    @Excel(name = "车牌号")
    private String plateNumber;

    /** 原收费明细信息 */
    private PaymentDetail originalPaymentDetail;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("refundId", getRefundId())
            .append("originalPaymentDetailId", getOriginalPaymentDetailId())
            .append("feeType", getFeeType())
            .append("feeName", getFeeName())
            .append("originalPaymentAmount", getOriginalPaymentAmount())
            .append("refundAmount", getRefundAmount())
            .append("refundMonths", getRefundMonths())
            .append("refundDays", getRefundDays())
            .append("originalPeriodStart", getOriginalPeriodStart())
            .append("originalPeriodEnd", getOriginalPeriodEnd())
            .append("refundPeriodStart", getRefundPeriodStart())
            .append("refundPeriodEnd", getRefundPeriodEnd())
            .append("remainingPeriodStart", getRemainingPeriodStart())
            .append("remainingPeriodEnd", getRemainingPeriodEnd())
            .append("plateNumber", getPlateNumber())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .toString();
    }
}
