package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 电梯费对象 elevator_fee_bill
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class ElevatorFeeBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 账单月份(YYYY-MM-01) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单月份(YYYY-MM-01)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billMonth;

    /** 房屋面积(按面积计费时使用) */
    @Excel(name = "房屋面积(按面积计费时使用)")
    private BigDecimal houseArea;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal amount;

    /** 状态(0未缴,1已缴) */
    @Excel(name = "状态(0未缴,1已缴)")
    private Integer status;

    /** 缴费日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "缴费日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /** 收据编号 */
    @Excel(name = "收据编号")
    private String receiptNumber;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("billMonth", getBillMonth())
            .append("houseArea", getHouseArea())
            .append("amount", getAmount())
            .append("status", getStatus())
            .append("paymentDate", getPaymentDate())
            .append("receiptNumber", getReceiptNumber())
            .append("createTime", getCreateTime())
            .toString();
    }
}
