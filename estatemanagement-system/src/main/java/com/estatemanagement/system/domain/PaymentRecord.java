package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 收费记录对象 payment_record
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class PaymentRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联账单ID */
    @Excel(name = "关联账单ID")
    private Long billId;

    /** 收据编号 */
    @Excel(name = "收据编号")
    private String receiptNumber;

    /** 实收金额 */
    @Excel(name = "实收金额")
    private BigDecimal paymentAmount;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 缴费日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "缴费日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /** 操作人ID */
    @Excel(name = "操作人ID")
    private Long operatorId;

    /** 费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金,null表示混合支付) */
    @Excel(name = "费用类型")
    private Long feeType;

    /** 缴费月数(多交时记录月数) */
    @Excel(name = "缴费月数")
    private Long paymentMonths;

    /** 缴费天数(零散天数支付) */
    @Excel(name = "缴费天数")
    private Long paymentDays;

    /** 是否部分付款(0否,1是) */
    @Excel(name = "是否部分付款")
    private Integer isPartial;

    /** 是否预交费用(0否,1是) */
    @Excel(name = "是否预交费用")
    private Integer isAdvance;

    /** 物业费金额 */
    @Excel(name = "物业费金额")
    private BigDecimal propertyFeeAmount;

    /** 停车费金额 */
    @Excel(name = "停车费金额")
    private BigDecimal parkingFeeAmount;

    /** 卫生费金额 */
    @Excel(name = "卫生费金额")
    private BigDecimal sanitationFeeAmount;

    /** 电梯费金额 */
    @Excel(name = "电梯费金额")
    private BigDecimal elevatorFeeAmount;

    /** 滞纳金金额 */
    @Excel(name = "滞纳金金额")
    private BigDecimal lateFeeAmount;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 应缴金额 */
    @Excel(name = "应缴金额")
    private BigDecimal dueAmount;

    /** 审核状态(0待审核,1通过审核,2审核拒绝) */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=通过审核,2=审核拒绝")
    private Integer auditStatus;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long auditorId;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditComment;

    // 扩展字段（非数据库字段，用于显示）
    private String communityName;
    private String buildingNumber;
    private String houseNumber;
    private String ownerName;
    private String tenantName;
    private String billNumber;
    private String auditorName; // 审核人姓名（用于显示）

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("billId", getBillId())
            .append("receiptNumber", getReceiptNumber())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentMethod", getPaymentMethod())
            .append("paymentDate", getPaymentDate())
            .append("operatorId", getOperatorId())
            .append("feeType", getFeeType())
            .append("paymentMonths", getPaymentMonths())
            .append("paymentDays", getPaymentDays())
            .append("isPartial", getIsPartial())
            .append("isAdvance", getIsAdvance())
            .append("propertyFeeAmount", getPropertyFeeAmount())
            .append("parkingFeeAmount", getParkingFeeAmount())
            .append("sanitationFeeAmount", getSanitationFeeAmount())
            .append("elevatorFeeAmount", getElevatorFeeAmount())
            .append("lateFeeAmount", getLateFeeAmount())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("dueAmount", getDueAmount())
            .append("auditStatus", getAuditStatus())
            .append("auditorId", getAuditorId())
            .append("auditTime", getAuditTime())
            .append("auditComment", getAuditComment())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .toString();
    }
}
