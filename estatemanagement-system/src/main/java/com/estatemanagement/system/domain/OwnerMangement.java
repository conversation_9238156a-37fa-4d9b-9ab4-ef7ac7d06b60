package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 业主管理对象 owner_mangement
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class OwnerMangement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区id */
    @Excel(name = "小区id")
    private Long communityId;

    /** 楼号 */
    @Excel(name = "楼号")
    private String buildingNumber;

    /** 门牌号 */
    @Excel(name = "门牌号")
    private String houseNumber;

    /** 房子面积 */
    @Excel(name = "房子面积")
    private BigDecimal houseArea;

    /** 房子类型：0：车库；1：房子 */
    @Excel(name = "房子类型", readConverterExp = "0=车库,1=房子")
    private Integer houseType;

    /** 业主名字 */
    @Excel(name = "业主名字")
    private String ownerName;

    /** 业主电话 */
    @Excel(name = "业主电话")
    private String ownerPhone;

    /** 出租状态 */
    @Excel(name = "出租状态")
    private String rentalStatus;

    /** 车牌信息列表 */
    private List<PlateInfo> plateInfoList;

    /** 人脸信息列表 */
    private List<FaceInfo> faceInfoList;

    /** 租客名字 */
    @Excel(name = "租客名字")
    private String tenantName;

    /** 租客电话 */
    @Excel(name = "租客电话")
    private String tenantPhone;

    /** 卫生费到期时间 */
    @Excel(name = "卫生费到期时间")
    private Date publicSanitationFeeEnddate;

    /** 物业费到期时间 */
    @Excel(name = "物业费到期时间")
    private Date communityPriceEnddate;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("buildingNumber", getBuildingNumber())
            .append("houseNumber", getHouseNumber())
            .append("houseArea", getHouseArea())
            .append("houseType", getHouseType())
            .append("ownerName", getOwnerName())
            .append("ownerPhone", getOwnerPhone())
            .append("tenantName", getTenantName())
            .append("tenantPhone", getTenantPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    /**
     * 车牌信息内部类
     */
    @Setter
    @Getter
    public static class PlateInfo {
        /** 车牌号 */
        private String plateNumber;

        /** 是否三证合一(0否,1是) */
        private Integer isThreeCertificates;

        /** 车位号 */
        private String spaceNumber;

        /** 是否到期 */
        private Boolean expired;

        /** 结束日期 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
        private Date endDate;

        public PlateInfo() {
            this.isThreeCertificates = 0; // 默认非三证合一
            this.expired = false; // 默认未到期
        }

        public PlateInfo(String plateNumber, Integer isThreeCertificates) {
            this.plateNumber = plateNumber;
            this.isThreeCertificates = isThreeCertificates != null ? isThreeCertificates : 0;
            this.expired = false;
        }

        public PlateInfo(String plateNumber, Integer isThreeCertificates, String spaceNumber) {
            this.plateNumber = plateNumber;
            this.isThreeCertificates = isThreeCertificates != null ? isThreeCertificates : 0;
            this.spaceNumber = spaceNumber;
            this.expired = false;
        }

    }

    /**
     * 人脸信息内部类
     */
    @Setter
    @Getter
    public static class FaceInfo {
        /** 人脸记录ID */
        private Long id;

        /** 人员姓名 */
        private String personName;

        /** 人员类型(1业主,2租客,3家属) */
        private Integer personType;

        /** 开通时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "开通时间", width = 30, dateFormat = "yyyy-MM-dd")
        private Date openTime;

        /** 到期时间 */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd")
        private Date expireTime;

        /** 状态(1有效,0禁用) */
        private Integer status;

        /** 是否到期 */
        private Boolean expired;

        public FaceInfo() {
            this.personType = 1; // 默认业主
            this.status = 1; // 默认有效
            this.expired = false; // 默认未到期
        }

        public FaceInfo(String personName, Integer personType) {
            this.personName = personName;
            this.personType = personType != null ? personType : 1;
            this.status = 1;
            this.expired = false;
        }

        public FaceInfo(String personName, Integer personType, Date openTime, Date expireTime) {
            this.personName = personName;
            this.personType = personType != null ? personType : 1;
            this.openTime = openTime;
            this.expireTime = expireTime;
            this.status = 1;
            this.expired = false;
        }
    }
}
