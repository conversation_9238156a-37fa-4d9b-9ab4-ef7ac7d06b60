package com.estatemanagement.system.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 阶梯费用配置
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class TieredFeeConfig {
    
    /** 阶梯费用区间列表 */
    private List<TieredFeeRange> ranges;
    
    /**
     * 阶梯费用区间
     */
    @Data
    public static class TieredFeeRange {
        /** 区间名称 */
        private String name;
        
        /** 最小面积（包含） */
        private BigDecimal minArea;
        
        /** 最大面积（包含，null表示无上限） */
        private BigDecimal maxArea;
        
        /** 该区间的费用（月） */
        private BigDecimal fee;
        
        /** 区间描述 */
        private String description;
        
        /**
         * 检查指定面积是否在此区间内
         */
        public boolean isInRange(BigDecimal area) {
            if (area == null) {
                return false;
            }
            
            // 检查最小值
            if (minArea != null && area.compareTo(minArea) < 0) {
                return false;
            }
            
            // 检查最大值（null表示无上限）
            if (maxArea != null && area.compareTo(maxArea) > 0) {
                return false;
            }
            
            return true;
        }
        
        /**
         * 获取区间显示文本
         */
        public String getRangeText() {
            if (minArea == null && maxArea == null) {
                return "全部";
            } else if (minArea == null) {
                return maxArea + "㎡以下";
            } else if (maxArea == null) {
                return minArea + "㎡以上";
            } else {
                return minArea + "-" + maxArea + "㎡";
            }
        }
    }
    
    /**
     * 根据面积获取对应的费用
     */
    public BigDecimal getFeeByArea(BigDecimal area) {
        if (ranges == null || ranges.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        for (TieredFeeRange range : ranges) {
            if (range.isInRange(area)) {
                return range.getFee() != null ? range.getFee() : BigDecimal.ZERO;
            }
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 根据面积获取对应的区间信息
     */
    public TieredFeeRange getRangeByArea(BigDecimal area) {
        if (ranges == null || ranges.isEmpty()) {
            return null;
        }
        
        for (TieredFeeRange range : ranges) {
            if (range.isInRange(area)) {
                return range;
            }
        }
        
        return null;
    }
    

    
    /**
     * 创建费用区间
     */
    private static TieredFeeRange createRange(String name, BigDecimal minArea, BigDecimal maxArea, BigDecimal fee) {
        TieredFeeRange range = new TieredFeeRange();
        range.setName(name);
        range.setMinArea(minArea);
        range.setMaxArea(maxArea);
        range.setFee(fee);
        return range;
    }
}
