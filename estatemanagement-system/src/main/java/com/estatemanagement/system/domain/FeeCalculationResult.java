package com.estatemanagement.system.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 费用计算结果
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Setter
@Getter
public class FeeCalculationResult {
    
    /** 开始日期 */
    private LocalDate startDate;
    
    /** 结束日期 */
    private LocalDate endDate;
    
    /** 月费用单价 */
    private BigDecimal monthlyFee;
    
    /** 日费用单价 */
    private BigDecimal dailyFee;
    
    /** 完整月数 */
    private int fullMonths;
    
    /** 剩余天数 */
    private int remainingDays;
    
    /** 完整月费用（保留角分） */
    private BigDecimal fullMonthsFee;
    
    /** 剩余天数费用（舍去角分） */
    private BigDecimal remainingDaysFee;
    
    /** 总费用 */
    private BigDecimal totalFee;
    
    /** 时间计算结果 */
    private TimeCalculationResult timeResult;
    
    /** 计算说明 */
    private String description;
    
    public FeeCalculationResult() {
    }
    
    public FeeCalculationResult(LocalDate startDate, LocalDate endDate, BigDecimal monthlyFee,
                              BigDecimal dailyFee, int fullMonths, int remainingDays,
                              BigDecimal fullMonthsFee, BigDecimal remainingDaysFee,
                              BigDecimal totalFee, TimeCalculationResult timeResult, String description) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.monthlyFee = monthlyFee;
        this.dailyFee = dailyFee;
        this.fullMonths = fullMonths;
        this.remainingDays = remainingDays;
        this.fullMonthsFee = fullMonthsFee;
        this.remainingDaysFee = remainingDaysFee;
        this.totalFee = totalFee;
        this.timeResult = timeResult;
        this.description = description;
    }

    @Override
    public String toString() {
        return "FeeCalculationResult{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", monthlyFee=" + monthlyFee +
                ", dailyFee=" + dailyFee +
                ", fullMonths=" + fullMonths +
                ", remainingDays=" + remainingDays +
                ", fullMonthsFee=" + fullMonthsFee +
                ", remainingDaysFee=" + remainingDaysFee +
                ", totalFee=" + totalFee +
                ", timeResult=" + timeResult +
                ", description='" + description + '\'' +
                '}';
    }
}
