package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 小区账单明细对象 community_bill_detail
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class CommunityBillDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 账单ID */
    @Excel(name = "账单ID")
    private Long billId;

    /** 费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金) */
    @Excel(name = "费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)")
    private Long feeType;

    /** 费用名称 */
    @Excel(name = "费用名称")
    private String feeName;

    /** 计费基数(面积、车位数等) */
    @Excel(name = "计费基数(面积、车位数等)")
    private BigDecimal baseAmount;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 计费天数 */
    @Excel(name = "计费天数")
    private Long billingDays;

    /** 费用金额 */
    @Excel(name = "费用金额")
    private BigDecimal amount;

    /** 车牌号(停车费时使用) */
    @Excel(name = "车牌号(停车费时使用)")
    private String plateNumber;

    /** 车位号(停车费时使用) */
    @Excel(name = "车位号(停车费时使用)")
    private String spaceNumber;

    /** 计算公式说明 */
    @Excel(name = "计算公式说明")
    private String calculationFormula;

    /** 账单周期开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodStart;

    /** 账单周期结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodEnd;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("billId", getBillId())
            .append("feeType", getFeeType())
            .append("feeName", getFeeName())
            .append("baseAmount", getBaseAmount())
            .append("unitPrice", getUnitPrice())
            .append("billingDays", getBillingDays())
            .append("amount", getAmount())
            .append("plateNumber", getPlateNumber())
            .append("spaceNumber", getSpaceNumber())
            .append("calculationFormula", getCalculationFormula())
            .append("billPeriodStart", getBillPeriodStart())
            .append("billPeriodEnd", getBillPeriodEnd())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
