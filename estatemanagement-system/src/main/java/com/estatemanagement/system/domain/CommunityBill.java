package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 小区账单对象 community_bill
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class CommunityBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 账单编号 */
    @Excel(name = "账单编号")
    private String billNumber;

    /** 账单周期开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodStart;

    /** 账单周期结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账单周期结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date billPeriodEnd;

    /** 物业费 */
    @Excel(name = "物业费")
    private BigDecimal propertyFee;

    /** 停车费 */
    @Excel(name = "停车费")
    private BigDecimal parkingFee;

    /** 电梯费 */
    @Excel(name = "电梯费")
    private BigDecimal elevatorFee;

    /** 卫生费 */
    @Excel(name = "卫生费")
    private BigDecimal sanitationFee;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 已支付金额 */
    @Excel(name = "已支付金额")
    private BigDecimal paidAmount;

    /** 支付状态(0未支付,1部分支付,2已支付,3合并到新账单) */
    @Excel(name = "支付状态(0未支付,1部分支付,2已支付,3合并到新账单)")
    private Integer paymentStatus;

    /** 物业费支付状态(0未支付,1部分支付,2已支付) */
    @Excel(name = "物业费支付状态")
    private Integer propertyPaymentStatus;

    /** 停车费支付状态(0未支付,1部分支付,2已支付) */
    @Excel(name = "停车费支付状态")
    private Integer parkingPaymentStatus;

    /** 卫生费支付状态(0未支付,1部分支付,2已支付) */
    @Excel(name = "卫生费支付状态")
    private Integer sanitationPaymentStatus;

    /** 电梯费支付状态(0未支付,1部分支付,2已支付) */
    @Excel(name = "电梯费支付状态")
    private Integer elevatorPaymentStatus;

    /** 物业费已支付金额 */
    @Excel(name = "物业费已支付金额")
    private BigDecimal propertyPaidAmount;

    /** 停车费已支付金额 */
    @Excel(name = "停车费已支付金额")
    private BigDecimal parkingPaidAmount;

    /** 卫生费已支付金额 */
    @Excel(name = "卫生费已支付金额")
    private BigDecimal sanitationPaidAmount;

    /** 电梯费已支付金额 */
    @Excel(name = "电梯费已支付金额")
    private BigDecimal elevatorPaidAmount;

    /** 支付日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "支付日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date paymentDate;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 是否为租客账单(0业主,1租客) */
    @Excel(name = "是否为租客账单(0业主,1租客)")
    private Integer isTenant;

    /** 收费经理ID */
    @Excel(name = "收费经理ID")
    private Long managerId;

    /** 收据编号 */
    @Excel(name = "收据编号")
    private String receiptNumber;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 楼号 */
    @Excel(name = "楼号")
    private String buildingNumber;

    /** 门牌号 */
    @Excel(name = "门牌号")
    private String houseNumber;

    /** 业主名字 */
    @Excel(name = "业主名字")
    private String ownerName;

    /** 租客名字 */
    @Excel(name = "租客名字")
    private String tenantName;

    /** 出租状态 */
    @Excel(name = "出租状态", readConverterExp = "0=否,1=是")
    private String rentalStatus;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("billNumber", getBillNumber())
            .append("billPeriodStart", getBillPeriodStart())
            .append("billPeriodEnd", getBillPeriodEnd())
            .append("propertyFee", getPropertyFee())
            .append("parkingFee", getParkingFee())
            .append("elevatorFee", getElevatorFee())
            .append("sanitationFee", getSanitationFee())
            .append("totalAmount", getTotalAmount())
            .append("paidAmount", getPaidAmount())
            .append("paymentStatus", getPaymentStatus())
            .append("propertyPaymentStatus", getPropertyPaymentStatus())
            .append("parkingPaymentStatus", getParkingPaymentStatus())
            .append("sanitationPaymentStatus", getSanitationPaymentStatus())
            .append("elevatorPaymentStatus", getElevatorPaymentStatus())
            .append("propertyPaidAmount", getPropertyPaidAmount())
            .append("parkingPaidAmount", getParkingPaidAmount())
            .append("sanitationPaidAmount", getSanitationPaidAmount())
            .append("elevatorPaidAmount", getElevatorPaidAmount())
            .append("paymentDate", getPaymentDate())
            .append("paymentMethod", getPaymentMethod())
            .append("isTenant", getIsTenant())
            .append("managerId", getManagerId())
            .append("receiptNumber", getReceiptNumber())
            .append("communityName", getCommunityName())
            .append("buildingNumber", getBuildingNumber())
            .append("houseNumber", getHouseNumber())
            .append("ownerName", getOwnerName())
            .append("tenantName", getTenantName())
            .append("rentalStatus", getRentalStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
