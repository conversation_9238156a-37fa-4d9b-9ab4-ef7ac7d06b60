package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 小区管理对象 community_mangement
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Setter
@Getter
public class CommunityMangement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区名字 */
    @Excel(name = "小区名字")
    private String communityName;

    /** 物业费单价（平方月） */
    @Excel(name = "物业费单价", readConverterExp = "平=方月")
    private BigDecimal communityPrice;

    /** 三证合一停车费（月） */
    @Excel(name = "三证合一停车费", readConverterExp = "月=")
    private BigDecimal ownerParkingFee;

    /** 非三证合一停车费（月） */
    @Excel(name = "非三证合一停车费", readConverterExp = "月=")
    private BigDecimal tenantParkingFee;

    /** 电梯费（月） */
    @Excel(name = "电梯费", readConverterExp = "月=")
    private BigDecimal elevatorFee;

    /** 卫生费缴费类型(1固定费用,2阶梯费用) */
    @Excel(name = "卫生费缴费类型", readConverterExp = "1=固定费用,2=阶梯费用")
    private Integer sanitationFeeType;

    /** 固定卫生费（月） */
    @Excel(name = "固定卫生费", readConverterExp = "月=")
    private BigDecimal fixedSanitationFee;

    /** 阶梯费用配置(JSON格式) */
    @Excel(name = "阶梯费用配置")
    private String tieredFeeConfig;

    /** 入园日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入园日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryDate;

    /** 当前账单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当前账单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statementDate;
}
