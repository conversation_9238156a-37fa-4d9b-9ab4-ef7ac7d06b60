package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 小区汇总账单对象 community_summary_bill
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class CommunitySummaryBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 汇总账单编号 */
    @Excel(name = "汇总账单编号")
    private String summaryNumber;

    /** 汇总周期开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "汇总周期开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date summaryPeriodStart;

    /** 汇总周期结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "汇总周期结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date summaryPeriodEnd;

    /** 物业费总额 */
    @Excel(name = "物业费总额")
    private BigDecimal totalPropertyFee;

    /** 停车费总额 */
    @Excel(name = "停车费总额")
    private BigDecimal totalParkingFee;

    /** 电梯费总额 */
    @Excel(name = "电梯费总额")
    private BigDecimal totalElevatorFee;

    /** 卫生费总额 */
    @Excel(name = "卫生费总额")
    private BigDecimal totalSanitationFee;

    /** 应收总额 */
    @Excel(name = "应收总额")
    private BigDecimal totalAmount;

    /** 实收总额 */
    @Excel(name = "实收总额")
    private BigDecimal actualAmount;

    /** 未收总额 */
    @Excel(name = "未收总额")
    private BigDecimal unpaidAmount;

    /** 收缴率(%) */
    @Excel(name = "收缴率(%)")
    private BigDecimal collectionRate;

    /** 总户数 */
    @Excel(name = "总户数")
    private Long houseCount;

    /** 已缴费户数 */
    @Excel(name = "已缴费户数")
    private Long paidHouseCount;

    /** 未缴费户数 */
    @Excel(name = "未缴费户数")
    private Long unpaidHouseCount;

    /** 上交状态(0未上交,1已上交) */
    @Excel(name = "上交状态(0未上交,1已上交)")
    private Integer submitStatus;

    /** 上交日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上交日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submitDate;

    /** 上交人 */
    @Excel(name = "上交人")
    private String submitBy;

    /** 财务确认(0未确认,1已确认) */
    @Excel(name = "财务确认(0未确认,1已确认)")
    private Integer financeConfirm;

    /** 财务确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务确认日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date financeConfirmDate;

    /** 财务确认人 */
    @Excel(name = "财务确认人")
    private String financeConfirmBy;

    /** 财务实际收到金额 */
    @Excel(name = "财务实际收到金额")
    private BigDecimal actualReceivedAmount;

    /** 财务收钱确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "财务收钱确认日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receivedDate;

    /** 财务收钱确认人 */
    @Excel(name = "财务收钱确认人")
    private String receivedBy;

    /** 收钱差额(实收总额-实际收到金额) */
    @Excel(name = "收钱差额")
    private BigDecimal receiveDifference;

    /** 收钱备注 */
    @Excel(name = "收钱备注")
    private String receiveRemark;

    /** 收钱确认状态(0未确认,1已确认) */
    @Excel(name = "收钱确认状态")
    private Integer receiveStatus;

    /** 总退费金额 */
    @Excel(name = "总退费金额")
    private BigDecimal totalRefundAmount;

    /** 物业费退费金额 */
    @Excel(name = "物业费退费金额")
    private BigDecimal propertyRefundAmount;

    /** 停车费退费金额 */
    @Excel(name = "停车费退费金额")
    private BigDecimal parkingRefundAmount;

    /** 卫生费退费金额 */
    @Excel(name = "卫生费退费金额")
    private BigDecimal sanitationRefundAmount;

    /** 电梯费退费金额 */
    @Excel(name = "电梯费退费金额")
    private BigDecimal elevatorRefundAmount;

    /** 滞纳金退费金额 */
    @Excel(name = "滞纳金退费金额")
    private BigDecimal lateFeeRefundAmount;

    /** 退费笔数 */
    @Excel(name = "退费笔数")
    private Long refundCount;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("communityName", getCommunityName())
            .append("summaryNumber", getSummaryNumber())
            .append("summaryPeriodStart", getSummaryPeriodStart())
            .append("summaryPeriodEnd", getSummaryPeriodEnd())
            .append("totalPropertyFee", getTotalPropertyFee())
            .append("totalParkingFee", getTotalParkingFee())
            .append("totalElevatorFee", getTotalElevatorFee())
            .append("totalSanitationFee", getTotalSanitationFee())
            .append("totalAmount", getTotalAmount())
            .append("actualAmount", getActualAmount())
            .append("unpaidAmount", getUnpaidAmount())
            .append("collectionRate", getCollectionRate())
            .append("houseCount", getHouseCount())
            .append("paidHouseCount", getPaidHouseCount())
            .append("unpaidHouseCount", getUnpaidHouseCount())
            .append("submitStatus", getSubmitStatus())
            .append("submitDate", getSubmitDate())
            .append("submitBy", getSubmitBy())
            .append("financeConfirm", getFinanceConfirm())
            .append("financeConfirmDate", getFinanceConfirmDate())
            .append("financeConfirmBy", getFinanceConfirmBy())
            .append("actualReceivedAmount", getActualReceivedAmount())
            .append("receivedDate", getReceivedDate())
            .append("receivedBy", getReceivedBy())
            .append("receiveDifference", getReceiveDifference())
            .append("receiveRemark", getReceiveRemark())
            .append("receiveStatus", getReceiveStatus())
            .append("totalRefundAmount", getTotalRefundAmount())
            .append("propertyRefundAmount", getPropertyRefundAmount())
            .append("parkingRefundAmount", getParkingRefundAmount())
            .append("sanitationRefundAmount", getSanitationRefundAmount())
            .append("elevatorRefundAmount", getElevatorRefundAmount())
            .append("lateFeeRefundAmount", getLateFeeRefundAmount())
            .append("refundCount", getRefundCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
