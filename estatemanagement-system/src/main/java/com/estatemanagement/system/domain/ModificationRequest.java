package com.estatemanagement.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 修改申请工单对象 modification_request
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class ModificationRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 申请类型(1户主变更,2退费申请,3账单修改,4其他) */
    @Excel(name = "申请类型(1户主变更,2退费申请,3账单修改,4其他)")
    private Integer requestType;

    /** 申请内容 */
    @Excel(name = "申请内容")
    private String requestContent;

    /** 申请人 */
    @Excel(name = "申请人")
    private String requestBy;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date requestTime;

    /** 处理状态(0待处理,1已处理,2已驳回) */
    @Excel(name = "处理状态(0待处理,1已处理,2已驳回)")
    private Integer status;

    /** 处理人 */
    @Excel(name = "处理人")
    private String processBy;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processTime;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String processResult;

    /** 小区名称 - 用于显示 */
    private String communityName;

    /** 业主名称 - 用于显示 */
    private String ownerName;

    /** 楼号 - 用于显示 */
    private String buildingNumber;

    /** 门牌号 - 用于显示 */
    private String houseNumber;

    /** 租客名称 - 用于显示 */
    private String tenantName;

    /** 出租状态 - 用于显示 */
    private String rentalStatus;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("requestType", getRequestType())
            .append("requestContent", getRequestContent())
            .append("requestBy", getRequestBy())
            .append("requestTime", getRequestTime())
            .append("status", getStatus())
            .append("processBy", getProcessBy())
            .append("processTime", getProcessTime())
            .append("processResult", getProcessResult())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}
