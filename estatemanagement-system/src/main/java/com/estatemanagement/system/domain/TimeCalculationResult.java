package com.estatemanagement.system.domain;

import java.time.LocalDate;

/**
 * 时间计算结果
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public class TimeCalculationResult {
    
    /** 开始日期 */
    private LocalDate startDate;
    
    /** 结束日期 */
    private LocalDate endDate;
    
    /** 总年数 */
    private int totalYears;
    
    /** 总月数 */
    private int totalMonths;
    
    /** 总天数 */
    private long totalDays;
    
    /** 完整月数 */
    private int fullMonths;
    
    /** 剩余天数 */
    private int remainingDays;
    
    /** 是否包含不完整月份 */
    private boolean hasPartialMonth;
    
    public TimeCalculationResult() {
    }
    
    public TimeCalculationResult(LocalDate startDate, LocalDate endDate, 
                               int totalYears, int totalMonths, long totalDays,
                               int fullMonths, int remainingDays, boolean hasPartialMonth) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.totalYears = totalYears;
        this.totalMonths = totalMonths;
        this.totalDays = totalDays;
        this.fullMonths = fullMonths;
        this.remainingDays = remainingDays;
        this.hasPartialMonth = hasPartialMonth;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    public int getTotalYears() {
        return totalYears;
    }
    
    public void setTotalYears(int totalYears) {
        this.totalYears = totalYears;
    }
    
    public int getTotalMonths() {
        return totalMonths;
    }
    
    public void setTotalMonths(int totalMonths) {
        this.totalMonths = totalMonths;
    }
    
    public long getTotalDays() {
        return totalDays;
    }
    
    public void setTotalDays(long totalDays) {
        this.totalDays = totalDays;
    }
    
    public int getFullMonths() {
        return fullMonths;
    }
    
    public void setFullMonths(int fullMonths) {
        this.fullMonths = fullMonths;
    }
    
    public int getRemainingDays() {
        return remainingDays;
    }
    
    public void setRemainingDays(int remainingDays) {
        this.remainingDays = remainingDays;
    }
    
    public boolean isHasPartialMonth() {
        return hasPartialMonth;
    }
    
    public void setHasPartialMonth(boolean hasPartialMonth) {
        this.hasPartialMonth = hasPartialMonth;
    }
    
    @Override
    public String toString() {
        return "TimeCalculationResult{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", totalYears=" + totalYears +
                ", totalMonths=" + totalMonths +
                ", totalDays=" + totalDays +
                ", fullMonths=" + fullMonths +
                ", remainingDays=" + remainingDays +
                ", hasPartialMonth=" + hasPartialMonth +
                '}';
    }
}
