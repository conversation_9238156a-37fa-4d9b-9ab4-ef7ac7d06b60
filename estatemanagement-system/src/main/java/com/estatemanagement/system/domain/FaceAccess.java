package com.estatemanagement.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 人脸识别开通记录对象 face_access
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class FaceAccess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 租客ID */
    @Excel(name = "租客ID")
    private Long tenantId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;

    /** 人员类型(1业主,2租客,3家属) */
    @Excel(name = "人员类型(1业主,2租客,3家属)")
    private Integer personType;

    /** 开通时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开通时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date openTime;

    /** 到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    /** 状态(1有效,0禁用) */
    @Excel(name = "状态(1有效,0禁用)")
    private Integer status;

    /** 楼栋号 */
    @Excel(name = "楼栋号")
    private String buildingNumber;

    /** 房号 */
    @Excel(name = "房号")
    private String houseNumber;

    /** 业主姓名 */
    @Excel(name = "业主姓名")
    private String ownerName;

    /** 业主电话 */
    @Excel(name = "业主电话")
    private String ownerPhone;

    /** 租赁状态 */
    @Excel(name = "租赁状态")
    private String rentalStatus;

    /** 租客姓名 */
    @Excel(name = "租客姓名")
    private String tenantName;

    /** 租客电话 */
    @Excel(name = "租客电话")
    private String tenantPhone;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("tenantId", getTenantId())
            .append("personName", getPersonName())
            .append("personType", getPersonType())
            .append("openTime", getOpenTime())
            .append("expireTime", getExpireTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
