package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 押金管理对象 deposit_management
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Setter
@Getter
public class DepositManagement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 押金类型(1装修押金,2其他押金) */
    @Excel(name = "押金类型", readConverterExp = "1=装修押金,2=其他押金")
    private Integer depositType;

    /** 押金名称 */
    @Excel(name = "押金名称")
    private String depositName;

    /** 押金金额 */
    @Excel(name = "押金金额")
    private BigDecimal depositAmount;

    /** 收取日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收取日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectDate;

    /** 收取人ID */
    @Excel(name = "收取人ID")
    private Long collectUserId;

    /** 收取人姓名 */
    @Excel(name = "收取人姓名")
    private String collectUserName;

    /** 收据编号 */
    @Excel(name = "收据编号")
    private String receiptNumber;

    /** 退还日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退还日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundDate;

    /** 退还金额 */
    @Excel(name = "退还金额")
    private BigDecimal refundAmount;

    /** 退还人ID */
    @Excel(name = "退还人ID")
    private Long refundUserId;

    /** 退还人姓名 */
    @Excel(name = "退还人姓名")
    private String refundUserName;

    /** 退还原因 */
    @Excel(name = "退还原因")
    private String refundReason;

    /** 状态(0已收取,1已退还,2部分退还) */
    @Excel(name = "状态", readConverterExp = "0=已收取,1=已退还,2=部分退还")
    private Integer status;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 装修开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装修开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date renovationStartDate;

    /** 装修结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "装修结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date renovationEndDate;

    /** 装修负责人 */
    @Excel(name = "装修负责人")
    private String renovationManager;

    /** 装修负责人电话 */
    @Excel(name = "装修负责人电话")
    private String renovationManagerPhone;

    // 扩展字段（非数据库字段，用于显示）
    private String communityName;
    private String buildingNumber;
    private String houseNumber;
    private String ownerName;
    private String ownerPhone;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("depositType", getDepositType())
            .append("depositName", getDepositName())
            .append("depositAmount", getDepositAmount())
            .append("collectDate", getCollectDate())
            .append("collectUserId", getCollectUserId())
            .append("collectUserName", getCollectUserName())
            .append("receiptNumber", getReceiptNumber())
            .append("refundDate", getRefundDate())
            .append("refundAmount", getRefundAmount())
            .append("refundUserId", getRefundUserId())
            .append("refundUserName", getRefundUserName())
            .append("refundReason", getRefundReason())
            .append("status", getStatus())
            .append("paymentMethod", getPaymentMethod())
            .append("renovationStartDate", getRenovationStartDate())
            .append("renovationEndDate", getRenovationEndDate())
            .append("renovationManager", getRenovationManager())
            .append("renovationManagerPhone", getRenovationManagerPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
