package com.estatemanagement.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 汇总账单生成配置对象 summary_bill_generation_config
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Setter
@Getter
public class SummaryBillGenerationConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 是否启用自动生成(0否,1是) */
    @Excel(name = "是否启用自动生成")
    private Integer autoGenerate;

    /** 生成间隔天数 */
    @Excel(name = "生成间隔天数")
    private Integer intervalDays;

    /** 上次生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上次生成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastGenerateTime;

    /** 下次生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下次生成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date nextGenerateTime;

    /** 状态(0停用,1启用) */
    @Excel(name = "状态")
    private Integer status;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("communityName", getCommunityName())
            .append("autoGenerate", getAutoGenerate())
            .append("intervalDays", getIntervalDays())
            .append("lastGenerateTime", getLastGenerateTime())
            .append("nextGenerateTime", getNextGenerateTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
