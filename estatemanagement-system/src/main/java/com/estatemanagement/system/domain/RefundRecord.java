package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 退费记录对象 refund_record
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Setter
@Getter
public class RefundRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 退费单号 */
    @Excel(name = "退费单号")
    private String refundNumber;

    /** 原收费记录ID */
    @Excel(name = "原收费记录ID")
    private Long originalPaymentId;

    /** 原收据号 */
    @Excel(name = "原收据号")
    private String originalReceiptNumber;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 退费类型(1全额退费,2部分退费) */
    @Excel(name = "退费类型", readConverterExp = "1=全额退费,2=部分退费")
    private Integer refundType;

    /** 退费原因 */
    @Excel(name = "退费原因")
    private String refundReason;

    /** 总退费金额 */
    @Excel(name = "总退费金额")
    private BigDecimal totalRefundAmount;

    /** 物业费退费金额 */
    @Excel(name = "物业费退费金额")
    private BigDecimal propertyFeeAmount;

    /** 停车费退费金额 */
    @Excel(name = "停车费退费金额")
    private BigDecimal parkingFeeAmount;

    /** 卫生费退费金额 */
    @Excel(name = "卫生费退费金额")
    private BigDecimal sanitationFeeAmount;

    /** 电梯费退费金额 */
    @Excel(name = "电梯费退费金额")
    private BigDecimal elevatorFeeAmount;

    /** 滞纳金退费金额 */
    @Excel(name = "滞纳金退费金额")
    private BigDecimal lateFeeAmount;

    /** 退费方式 */
    @Excel(name = "退费方式", readConverterExp = "cash=现金,bank_transfer=银行转账,wechat=微信,alipay=支付宝")
    private String refundMethod;

    /** 退费日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退费日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundDate;

    /** 操作员ID */
    @Excel(name = "操作员ID")
    private Long operatorId;

    /** 操作员姓名 */
    @Excel(name = "操作员姓名")
    private String operatorName;

    /** 状态(0待审核,1已完成,2已取消) */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=已完成,2=已取消")
    private Integer status;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long auditUserId;

    /** 审核人姓名 */
    @Excel(name = "审核人姓名")
    private String auditUserName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 退费明细列表 */
    private List<RefundDetail> refundDetailList;

    /** 原收费记录信息 */
    private PaymentRecord originalPaymentRecord;

    /** 业主信息 */
    private String ownerName;

    /** 小区名称 */
    private String communityName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("refundNumber", getRefundNumber())
            .append("originalPaymentId", getOriginalPaymentId())
            .append("originalReceiptNumber", getOriginalReceiptNumber())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("refundType", getRefundType())
            .append("refundReason", getRefundReason())
            .append("totalRefundAmount", getTotalRefundAmount())
            .append("propertyFeeAmount", getPropertyFeeAmount())
            .append("parkingFeeAmount", getParkingFeeAmount())
            .append("sanitationFeeAmount", getSanitationFeeAmount())
            .append("elevatorFeeAmount", getElevatorFeeAmount())
            .append("lateFeeAmount", getLateFeeAmount())
            .append("refundMethod", getRefundMethod())
            .append("refundDate", getRefundDate())
            .append("operatorId", getOperatorId())
            .append("operatorName", getOperatorName())
            .append("status", getStatus())
            .append("auditUserId", getAuditUserId())
            .append("auditUserName", getAuditUserName())
            .append("auditTime", getAuditTime())
            .append("auditRemark", getAuditRemark())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
