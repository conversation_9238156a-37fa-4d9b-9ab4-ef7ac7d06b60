package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 停车费信息对象 parking_info
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class ParkingInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 小区ID */
    @Excel(name = "小区ID")
    private Long communityId;

    /** 业主ID */
    @Excel(name = "业主ID")
    private Long ownerId;

    /** 租客ID */
    @Excel(name = "租客ID")
    private Long tenantId;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNumber;

    /** 车位号(如需要) */
    @Excel(name = "车位号(如需要)")
    private String spaceNumber;

    /** 月租金 */
    @Excel(name = "月租金")
    private BigDecimal monthlyFee;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 状态(1有效,0无效) */
    @Excel(name = "状态(1有效,0无效)")
    private Integer status;

    /** 是否三证合一(0否,1是) */
    @Excel(name = "是否三证合一", readConverterExp = "0=否,1=是")
    private Integer isThreeCertificates;

    /** 小区名字 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 楼号 */
    @Excel(name = "楼号")
    private String buildingNumber;

    /** 门牌号 */
    @Excel(name = "门牌号")
    private String houseNumber;

    /** 业主名字 */
    @Excel(name = "业主名字")
    private String ownerName;

    /** 租客名字 */
    @Excel(name = "租客名字")
    private String tenantName;

    /** 出租状态 */
    @Excel(name = "出租状态", readConverterExp = "0=否,1=是")
    private Integer rentalStatus;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String ownerPhone;
    /** 业主停车费（月） */
    private BigDecimal ownerParkingFee;

    /** 租客停车费（月） */
    private BigDecimal tenantParkingFee;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityId", getCommunityId())
            .append("ownerId", getOwnerId())
            .append("tenantId", getTenantId())
            .append("plateNumber", getPlateNumber())
            .append("spaceNumber", getSpaceNumber())
            .append("monthlyFee", getMonthlyFee())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }

    /**
     * 判断停车记录是否已到期
     * @return true-已到期, false-未到期
     */
    public boolean isExpired() {
        if (this.endDate == null) {
            return false;
        }
        Date now = new Date();
        return now.after(this.endDate);
    }

    /**
     * 获取停车状态描述
     * @return 状态描述：有效、无效、到期
     */
    public String getStatusDescription() {
        if (this.status == null || this.status == 0) {
            return "无效";
        }
        if (isExpired()) {
            return "到期";
        }
        return "有效";
    }
}
