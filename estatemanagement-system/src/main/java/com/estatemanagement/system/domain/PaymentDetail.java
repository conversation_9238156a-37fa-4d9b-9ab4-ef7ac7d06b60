package com.estatemanagement.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.estatemanagement.common.annotation.Excel;
import com.estatemanagement.common.core.domain.BaseEntity;

/**
 * 收费明细对象 payment_detail
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Setter
@Getter
public class PaymentDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 收费记录ID */
    @Excel(name = "收费记录ID")
    private Long paymentId;

    /** 费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金) */
    @Excel(name = "费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)")
    private Integer feeType;

    /** 费用名称 */
    @Excel(name = "费用名称")
    private String feeName;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal paymentAmount;

    /** 支付月数 */
    @Excel(name = "支付月数")
    private Long paymentMonths;

    /** 支付天数 */
    @Excel(name = "支付天数")
    private Long paymentDays;

    /** 关联账单明细ID */
    @Excel(name = "关联账单明细ID")
    private Long billDetailId;

    /** 是否预交(0否,1是) */
    @Excel(name = "是否预交(0否,1是)")
    private Integer isAdvance;

    /** 缴费周期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "缴费周期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date periodStart;

    /** 缴费周期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "缴费周期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date periodEnd;

    /** 车牌号（停车费时记录具体车牌号） */
    @Excel(name = "车牌号")
    private String plateNumber;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("paymentId", getPaymentId())
            .append("feeType", getFeeType())
            .append("feeName", getFeeName())
            .append("paymentAmount", getPaymentAmount())
            .append("paymentMonths", getPaymentMonths())
            .append("paymentDays", getPaymentDays())
            .append("billDetailId", getBillDetailId())
            .append("isAdvance", getIsAdvance())
            .append("periodStart", getPeriodStart())
            .append("periodEnd", getPeriodEnd())
            .append("plateNumber", getPlateNumber())
            .append("createTime", getCreateTime())
            .toString();
    }
}
