package com.estatemanagement.system.service;

import java.util.List;
import com.estatemanagement.system.domain.BillGenerationConfig;

/**
 * 账单生成配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface IBillGenerationConfigService 
{
    /**
     * 查询账单生成配置
     * 
     * @param id 账单生成配置主键
     * @return 账单生成配置
     */
    public BillGenerationConfig selectBillGenerationConfigById(Long id);

    /**
     * 查询账单生成配置列表
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 账单生成配置集合
     */
    public List<BillGenerationConfig> selectBillGenerationConfigList(BillGenerationConfig billGenerationConfig);

    /**
     * 根据小区ID查询账单生成配置
     * 
     * @param communityId 小区ID
     * @return 账单生成配置
     */
    public BillGenerationConfig selectBillGenerationConfigByCommunityId(Long communityId);

    /**
     * 新增账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    public int insertBillGenerationConfig(BillGenerationConfig billGenerationConfig);

    /**
     * 修改账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    public int updateBillGenerationConfig(BillGenerationConfig billGenerationConfig);

    /**
     * 批量删除账单生成配置
     * 
     * @param ids 需要删除的账单生成配置主键集合
     * @return 结果
     */
    public int deleteBillGenerationConfigByIds(Long[] ids);

    /**
     * 删除账单生成配置信息
     * 
     * @param id 账单生成配置主键
     * @return 结果
     */
    public int deleteBillGenerationConfigById(Long id);

    /**
     * 启用或停用账单自动生成
     * 
     * @param communityId 小区ID
     * @param status 状态(0停用,1启用)
     * @return 结果
     */
    public int toggleAutoGeneration(Long communityId, Integer status);

    /**
     * 手动触发账单生成
     * 
     * @param communityId 小区ID
     * @param generateType 生成类型
     * @return 任务ID
     */
    public String manualGenerateBills(Long communityId, String generateType);
}
