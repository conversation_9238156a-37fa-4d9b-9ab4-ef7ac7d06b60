package com.estatemanagement.system.service;

import java.util.List;
import com.estatemanagement.system.domain.ModificationRequest;

/**
 * 修改申请工单Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IModificationRequestService 
{
    /**
     * 查询修改申请工单
     * 
     * @param id 修改申请工单主键
     * @return 修改申请工单
     */
    public ModificationRequest selectModificationRequestById(Long id);

    /**
     * 查询修改申请工单列表
     * 
     * @param modificationRequest 修改申请工单
     * @return 修改申请工单集合
     */
    public List<ModificationRequest> selectModificationRequestList(ModificationRequest modificationRequest);

    /**
     * 新增修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    public int insertModificationRequest(ModificationRequest modificationRequest);

    /**
     * 修改修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    public int updateModificationRequest(ModificationRequest modificationRequest);

    /**
     * 批量删除修改申请工单
     * 
     * @param ids 需要删除的修改申请工单主键集合
     * @return 结果
     */
    public int deleteModificationRequestByIds(Long[] ids);

    /**
     * 删除修改申请工单信息
     * 
     * @param id 修改申请工单主键
     * @return 结果
     */
    public int deleteModificationRequestById(Long id);
}
