package com.estatemanagement.system.service;

import java.util.Date;
import java.util.List;
import com.estatemanagement.system.domain.CommunitySummaryBill;

/**
 * 小区汇总账单Service接口
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface ICommunitySummaryBillService
{
    /**
     * 查询小区汇总账单
     *
     * @param id 小区汇总账单主键
     * @return 小区汇总账单
     */
    public CommunitySummaryBill selectCommunitySummaryBillById(Long id);

    /**
     * 查询小区汇总账单列表
     *
     * @param communitySummaryBill 小区汇总账单
     * @return 小区汇总账单集合
     */
    public List<CommunitySummaryBill> selectCommunitySummaryBillList(CommunitySummaryBill communitySummaryBill);

    /**
     * 新增小区汇总账单
     *
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    public int insertCommunitySummaryBill(CommunitySummaryBill communitySummaryBill);

    /**
     * 修改小区汇总账单
     *
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    public int updateCommunitySummaryBill(CommunitySummaryBill communitySummaryBill);

    /**
     * 批量删除小区汇总账单
     *
     * @param ids 需要删除的小区汇总账单主键集合
     * @return 结果
     */
    public int deleteCommunitySummaryBillByIds(Long[] ids);

    /**
     * 删除小区汇总账单信息
     *
     * @param id 小区汇总账单主键
     * @return 结果
     */
    public int deleteCommunitySummaryBillById(Long id);

    /**
     * 手动生成小区汇总账单
     *
     * @param communityId 小区ID（null表示全部小区）
     * @param startDate 汇总开始日期
     * @param endDate 汇总结束日期
     * @return 任务ID
     */
    public String generateSummaryBills(Long communityId, Date startDate, Date endDate);

    /**
     * 检查指定时间段的汇总账单是否已存在
     *
     * @param communityId 小区ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否存在
     */
    public boolean checkSummaryBillExists(Long communityId, Date startDate, Date endDate);

    /**
     * 为指定小区生成汇总账单
     *
     * @param communityId 小区ID
     * @param startDate 汇总开始日期
     * @param endDate 汇总结束日期
     * @return 生成的汇总账单
     */
    public CommunitySummaryBill generateSummaryBillForCommunity(Long communityId, Date startDate, Date endDate);
}
