package com.estatemanagement.system.service;

import java.util.Date;
import java.util.List;
import com.estatemanagement.system.domain.SummaryBillGenerationConfig;

/**
 * 汇总账单生成配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface ISummaryBillGenerationConfigService 
{
    /**
     * 查询汇总账单生成配置
     * 
     * @param id 汇总账单生成配置主键
     * @return 汇总账单生成配置
     */
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigById(Long id);

    /**
     * 根据小区ID查询汇总账单生成配置
     * 
     * @param communityId 小区ID
     * @return 汇总账单生成配置
     */
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigByCommunityId(Long communityId);

    /**
     * 查询汇总账单生成配置列表
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 汇总账单生成配置集合
     */
    public List<SummaryBillGenerationConfig> selectSummaryBillGenerationConfigList(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 新增汇总账单生成配置
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    public int insertSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 修改汇总账单生成配置
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    public int updateSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 批量删除汇总账单生成配置
     * 
     * @param ids 需要删除的汇总账单生成配置主键集合
     * @return 结果
     */
    public int deleteSummaryBillGenerationConfigByIds(Long[] ids);

    /**
     * 删除汇总账单生成配置信息
     * 
     * @param id 汇总账单生成配置主键
     * @return 结果
     */
    public int deleteSummaryBillGenerationConfigById(Long id);

    /**
     * 启用或停用汇总账单自动生成
     * 
     * @param communityId 小区ID
     * @param status 状态(0停用,1启用)
     * @return 结果
     */
    public int toggleAutoGeneration(Long communityId, Integer status);

    /**
     * 手动触发汇总账单生成
     * 
     * @param communityId 小区ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务ID
     */
    public String manualGenerateSummaryBills(Long communityId, Date startDate, Date endDate);
}
