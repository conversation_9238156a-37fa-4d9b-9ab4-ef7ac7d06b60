package com.estatemanagement.system.service;

import java.util.List;
import com.estatemanagement.system.domain.CommunityMangement;

/**
 * 小区管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface ICommunityMangementService 
{
    /**
     * 查询小区管理
     * 
     * @param id 小区管理主键
     * @return 小区管理
     */
    public CommunityMangement selectCommunityMangementById(Long id);

    /**
     * 查询小区管理列表
     * 
     * @param communityMangement 小区管理
     * @return 小区管理集合
     */
    public List<CommunityMangement> selectCommunityMangementList(CommunityMangement communityMangement);

    /**
     * 新增小区管理
     * 
     * @param communityMangement 小区管理
     * @return 结果
     */
    public int insertCommunityMangement(CommunityMangement communityMangement);

    /**
     * 修改小区管理
     * 
     * @param communityMangement 小区管理
     * @return 结果
     */
    public int updateCommunityMangement(CommunityMangement communityMangement);

    /**
     * 批量删除小区管理
     * 
     * @param ids 需要删除的小区管理主键集合
     * @return 结果
     */
    public int deleteCommunityMangementByIds(Long[] ids);

    /**
     * 删除小区管理信息
     * 
     * @param id 小区管理主键
     * @return 结果
     */
    public int deleteCommunityMangementById(Long id);
}
