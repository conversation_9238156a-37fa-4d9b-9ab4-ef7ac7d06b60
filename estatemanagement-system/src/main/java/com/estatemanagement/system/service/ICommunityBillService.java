package com.estatemanagement.system.service;

import java.util.List;
import java.util.Map;
import com.estatemanagement.system.domain.CommunityBill;
import com.estatemanagement.system.service.impl.CommunityBillServiceImpl.BillGenerationProgress;

/**
 * 小区账单Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface ICommunityBillService 
{
    /**
     * 查询小区账单
     * 
     * @param id 小区账单主键
     * @return 小区账单
     */
    public CommunityBill selectCommunityBillById(Long id);

    /**
     * 查询小区账单详情（包含明细）
     * 
     * @param id 小区账单主键
     * @return 包含账单和明细的Map对象
     */
    public Map<String, Object> selectCommunityBillWithDetails(Long id);

    /**
     * 查询小区账单列表
     * 
     * @param communityBill 小区账单
     * @return 小区账单集合
     */
    public List<CommunityBill> selectCommunityBillList(CommunityBill communityBill);

    /**
     * 新增小区账单
     * 
     * @param communityBill 小区账单
     * @return 结果
     */
    public int insertCommunityBill(CommunityBill communityBill);

    /**
     * 修改小区账单
     * 
     * @param communityBill 小区账单
     * @return 结果
     */
    public int updateCommunityBill(CommunityBill communityBill);

    /**
     * 批量删除小区账单
     * 
     * @param ids 需要删除的小区账单主键集合
     * @return 结果
     */
    public int deleteCommunityBillByIds(Long[] ids);

    /**
     * 删除小区账单信息
     * 
     * @param id 小区账单主键
     * @return 结果
     */
    public int deleteCommunityBillById(Long id);

    /**
     * 自动生成账单
     * 
     * @param communityId 小区ID
     * @param billDate 账单日期
     * @param generateType 生成类型
     * @return 任务ID
     */
    public String generateBills(Long communityId, String billDate, String generateType);

    /**
     * 获取生成进度
     *
     * @param taskId 任务ID
     * @return 进度信息
     */
    public BillGenerationProgress getGenerationProgress(String taskId);

    /**
     * 根据小区ID获取未缴费账单列表（用于批量打印催缴单）
     *
     * @param communityId 小区ID
     * @return 未缴费账单列表
     */
    public List<Map<String, Object>> selectUnpaidBillsByCommunity(Long communityId);
}
