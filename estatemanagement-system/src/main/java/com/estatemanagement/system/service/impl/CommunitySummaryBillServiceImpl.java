package com.estatemanagement.system.service.impl;

import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.system.constants.SummaryBillConstants;
import com.estatemanagement.system.domain.CommunityBill;
import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.domain.CommunitySummaryBill;
import com.estatemanagement.system.domain.OwnerMangement;
import com.estatemanagement.system.mapper.CommunityBillMapper;
import com.estatemanagement.system.mapper.CommunityMangementMapper;
import com.estatemanagement.system.mapper.CommunitySummaryBillMapper;
import com.estatemanagement.system.mapper.OwnerMangementMapper;
import com.estatemanagement.system.mapper.RefundRecordMapper;
import com.estatemanagement.system.service.ICommunitySummaryBillService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 小区汇总账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class CommunitySummaryBillServiceImpl implements ICommunitySummaryBillService
{
    @Autowired
    private CommunitySummaryBillMapper communitySummaryBillMapper;

    @Autowired
    private CommunityMangementMapper communityMangementMapper;

    @Autowired
    private CommunityBillMapper communityBillMapper;

    @Autowired
    private OwnerMangementMapper ownerMangementMapper;

    @Autowired
    private RefundRecordMapper refundRecordMapper;

    // 汇总账单生成进度跟踪
    private static final ConcurrentMap<String, SummaryBillGenerationProgress> progressMap = new ConcurrentHashMap<>();

    /**
     * 汇总账单生成进度类
     */
    @Setter
    @Getter
    public static class SummaryBillGenerationProgress {
        // getters and setters
        private String status; // RUNNING, COMPLETED, FAILED
        private String message;
        private int totalCommunities;
        private int completedCommunities;
        private long startTime;
        private long endTime;

    }

    /**
     * 查询小区汇总账单
     * 
     * @param id 小区汇总账单主键
     * @return 小区汇总账单
     */
    @Override
    public CommunitySummaryBill selectCommunitySummaryBillById(Long id)
    {
        return communitySummaryBillMapper.selectCommunitySummaryBillById(id);
    }

    /**
     * 查询小区汇总账单列表
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 小区汇总账单
     */
    @Override
    public List<CommunitySummaryBill> selectCommunitySummaryBillList(CommunitySummaryBill communitySummaryBill)
    {
        return communitySummaryBillMapper.selectCommunitySummaryBillList(communitySummaryBill);
    }

    /**
     * 新增小区汇总账单
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    @Override
    public int insertCommunitySummaryBill(CommunitySummaryBill communitySummaryBill)
    {
        communitySummaryBill.setCreateTime(DateUtils.getNowDate());
        return communitySummaryBillMapper.insertCommunitySummaryBill(communitySummaryBill);
    }

    /**
     * 修改小区汇总账单
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    @Override
    public int updateCommunitySummaryBill(CommunitySummaryBill communitySummaryBill)
    {
        communitySummaryBill.setUpdateTime(DateUtils.getNowDate());
        return communitySummaryBillMapper.updateCommunitySummaryBill(communitySummaryBill);
    }

    /**
     * 批量删除小区汇总账单
     * 
     * @param ids 需要删除的小区汇总账单主键
     * @return 结果
     */
    @Override
    public int deleteCommunitySummaryBillByIds(Long[] ids)
    {
        return communitySummaryBillMapper.deleteCommunitySummaryBillByIds(ids);
    }

    /**
     * 删除小区汇总账单信息
     *
     * @param id 小区汇总账单主键
     * @return 结果
     */
    @Override
    public int deleteCommunitySummaryBillById(Long id)
    {
        return communitySummaryBillMapper.deleteCommunitySummaryBillById(id);
    }

    /**
     * 手动生成小区汇总账单
     *
     * @param communityId 小区ID（null表示全部小区）
     * @param startDate 汇总开始日期
     * @param endDate 汇总结束日期
     * @return 任务ID
     */
    @Override
    public String generateSummaryBills(Long communityId, Date startDate, Date endDate) {
        String taskId = generateTaskId(communityId, startDate, endDate);

        // 检查是否已有正在执行的任务
        SummaryBillGenerationProgress existingProgress = progressMap.get(taskId);
        if (existingProgress != null && SummaryBillConstants.TaskStatus.RUNNING.equals(existingProgress.getStatus())) {
            throw new RuntimeException("汇总账单正在生成中，请稍后再试");
        }

        // 创建进度记录
        SummaryBillGenerationProgress progress = new SummaryBillGenerationProgress();
        progress.setStatus(SummaryBillConstants.TaskStatus.RUNNING);
        progress.setMessage("正在初始化...");
        progress.setStartTime(System.currentTimeMillis());
        progressMap.put(taskId, progress);

        // 异步执行生成任务
        generateSummaryBillsAsync(taskId, communityId, startDate, endDate);

        return taskId;
    }

    /**
     * 检查指定时间段的汇总账单是否已存在
     *
     * @param communityId 小区ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否存在
     */
    @Override
    public boolean checkSummaryBillExists(Long communityId, Date startDate, Date endDate) {
        CommunitySummaryBill query = new CommunitySummaryBill();
        query.setCommunityId(communityId);
        query.setSummaryPeriodStart(startDate);
        query.setSummaryPeriodEnd(endDate);

        List<CommunitySummaryBill> existingBills = communitySummaryBillMapper.selectCommunitySummaryBillList(query);
        return !existingBills.isEmpty();
    }

    /**
     * 为指定小区生成汇总账单
     *
     * @param communityId 小区ID
     * @param startDate 汇总开始日期
     * @param endDate 汇总结束日期
     * @return 生成的汇总账单
     */
    @Override
    public CommunitySummaryBill generateSummaryBillForCommunity(Long communityId, Date startDate, Date endDate) {
        // 检查是否已存在
        if (checkSummaryBillExists(communityId, startDate, endDate)) {
            throw new RuntimeException("该小区在指定时间段的汇总账单已存在");
        }

        // 获取小区信息
        CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
        if (community == null) {
            throw new RuntimeException("小区不存在");
        }

        // 查询该小区在指定时间段内的所有账单
        CommunityBill billQuery = new CommunityBill();
        billQuery.setCommunityId(communityId);
        // 这里需要根据账单的时间范围进行查询，具体的查询条件需要在Mapper中实现

        List<CommunityBill> bills = communityBillMapper.selectCommunityBillList(billQuery);

        // 统计汇总数据
        BigDecimal totalPropertyFee = BigDecimal.ZERO;
        BigDecimal totalParkingFee = BigDecimal.ZERO;
        BigDecimal totalElevatorFee = BigDecimal.ZERO;
        BigDecimal totalSanitationFee = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal actualAmount = BigDecimal.ZERO;
        BigDecimal unpaidAmount = BigDecimal.ZERO;

        long totalHouseCount = 0L;
        long paidHouseCount = 0L;
        long unpaidHouseCount = 0L;

        // 获取该小区的总户数
        OwnerMangement ownerQuery = new OwnerMangement();
        ownerQuery.setCommunityId(communityId);
        List<OwnerMangement> owners = ownerMangementMapper.selectOwnerMangementList(ownerQuery);
        totalHouseCount = owners.size();

        for (CommunityBill bill : bills) {
            // 检查账单是否在指定时间范围内
            if (isDateInRange(bill.getBillPeriodStart(), bill.getBillPeriodEnd(), startDate, endDate)) {
                if (bill.getPropertyFee() != null) {
                    totalPropertyFee = totalPropertyFee.add(bill.getPropertyFee());
                }
                if (bill.getParkingFee() != null) {
                    totalParkingFee = totalParkingFee.add(bill.getParkingFee());
                }
                if (bill.getElevatorFee() != null) {
                    totalElevatorFee = totalElevatorFee.add(bill.getElevatorFee());
                }
                if (bill.getSanitationFee() != null) {
                    totalSanitationFee = totalSanitationFee.add(bill.getSanitationFee());
                }
                if (bill.getTotalAmount() != null) {
                    totalAmount = totalAmount.add(bill.getTotalAmount());
                }
                if (bill.getPaidAmount() != null) {
                    actualAmount = actualAmount.add(bill.getPaidAmount());
                }

                if (bill.getPaymentStatus() != null && bill.getPaymentStatus().equals(SummaryBillConstants.PaymentStatus.PAID)) {
                    paidHouseCount++;
                }
            }
        }

        unpaidHouseCount = totalHouseCount - paidHouseCount;

        unpaidAmount = totalAmount.subtract(actualAmount);

        // 计算收缴率
        BigDecimal collectionRate = BigDecimal.ZERO;
        if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
            collectionRate = actualAmount.divide(totalAmount, SummaryBillConstants.COLLECTION_RATE_SCALE, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(SummaryBillConstants.PERCENTAGE_FACTOR));
        }

        // 计算退费金额
        Map<String, Object> refundData = calculateRefundAmounts(communityId, startDate, endDate);

        // 创建汇总账单
        CommunitySummaryBill summaryBill = new CommunitySummaryBill();
        summaryBill.setCommunityId(communityId);
        summaryBill.setCommunityName(community.getCommunityName());
        summaryBill.setSummaryNumber(generateSummaryNumber(communityId));
        summaryBill.setSummaryPeriodStart(startDate);
        summaryBill.setSummaryPeriodEnd(endDate);
        summaryBill.setTotalPropertyFee(totalPropertyFee);
        summaryBill.setTotalParkingFee(totalParkingFee);
        summaryBill.setTotalElevatorFee(totalElevatorFee);
        summaryBill.setTotalSanitationFee(totalSanitationFee);
        summaryBill.setTotalAmount(totalAmount);
        summaryBill.setActualAmount(actualAmount);
        summaryBill.setUnpaidAmount(unpaidAmount);
        summaryBill.setCollectionRate(collectionRate);
        summaryBill.setHouseCount(totalHouseCount);
        summaryBill.setPaidHouseCount(paidHouseCount);
        summaryBill.setUnpaidHouseCount(unpaidHouseCount);

        // 设置退费相关字段
        summaryBill.setTotalRefundAmount((BigDecimal) refundData.get("totalRefundAmount"));
        summaryBill.setPropertyRefundAmount((BigDecimal) refundData.get("propertyRefundAmount"));
        summaryBill.setParkingRefundAmount((BigDecimal) refundData.get("parkingRefundAmount"));
        summaryBill.setSanitationRefundAmount((BigDecimal) refundData.get("sanitationRefundAmount"));
        summaryBill.setElevatorRefundAmount((BigDecimal) refundData.get("elevatorRefundAmount"));
        summaryBill.setLateFeeRefundAmount((BigDecimal) refundData.get("lateFeeRefundAmount"));
        summaryBill.setRefundCount((Long) refundData.get("refundCount"));

        summaryBill.setSubmitStatus(SummaryBillConstants.SubmitStatus.NOT_SUBMITTED);
        summaryBill.setFinanceConfirm(SummaryBillConstants.FinanceConfirmStatus.NOT_CONFIRMED);
        summaryBill.setReceiveStatus(SummaryBillConstants.ReceiveStatus.NOT_RECEIVED);
        summaryBill.setCreateTime(DateUtils.getNowDate());

        // 保存汇总账单
        communitySummaryBillMapper.insertCommunitySummaryBill(summaryBill);

        return summaryBill;
    }

    /**
     * 计算指定时间段内的退费金额
     *
     * @param communityId 小区ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 退费统计数据
     */
    private Map<String, Object> calculateRefundAmounts(Long communityId, Date startDate, Date endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("communityId", communityId);
        params.put("beginDate", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, startDate));
        params.put("endDate", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, endDate));
        params.put("status", 1); // 只统计已完成的退费

        Map<String, Object> refundStats = refundRecordMapper.selectRefundStatistics(params);

        // 确保所有字段都有默认值
        Map<String, Object> result = new HashMap<>();
        result.put("totalRefundAmount", refundStats.getOrDefault("total_refund_amount", BigDecimal.ZERO));
        result.put("propertyRefundAmount", refundStats.getOrDefault("total_property_fee_amount", BigDecimal.ZERO));
        result.put("parkingRefundAmount", refundStats.getOrDefault("total_parking_fee_amount", BigDecimal.ZERO));
        result.put("sanitationRefundAmount", refundStats.getOrDefault("total_sanitation_fee_amount", BigDecimal.ZERO));
        result.put("elevatorRefundAmount", refundStats.getOrDefault("total_elevator_fee_amount", BigDecimal.ZERO));
        result.put("lateFeeRefundAmount", refundStats.getOrDefault("total_late_fee_amount", BigDecimal.ZERO));
        result.put("refundCount", refundStats.getOrDefault("total_count", 0));

        return result;
    }

    /**
     * 异步生成汇总账单
     */
    private void generateSummaryBillsAsync(String taskId, Long communityId, Date startDate, Date endDate) {
        CompletableFuture.runAsync(() -> {
            SummaryBillGenerationProgress progress = progressMap.get(taskId);

            try {
                List<CommunityMangement> communities;

                if (communityId != null) {
                    // 生成单个小区的汇总账单
                    CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
                    if (community == null) {
                        throw new RuntimeException("小区不存在");
                    }
                    communities = Collections.singletonList(community);
                } else {
                    // 生成所有小区的汇总账单
                    communities = communityMangementMapper.selectCommunityMangementList(new CommunityMangement());
                }

                progress.setTotalCommunities(communities.size());
                progress.setMessage("开始生成汇总账单，共 " + communities.size() + " 个小区");

                int completedCount = 0;
                for (CommunityMangement community : communities) {
                    try {
                        // 检查是否已存在
                        if (!checkSummaryBillExists(community.getId(), startDate, endDate)) {
                            generateSummaryBillForCommunity(community.getId(), startDate, endDate);
                            progress.setMessage("已完成小区：" + community.getCommunityName());
                        } else {
                            progress.setMessage("跳过已存在的小区：" + community.getCommunityName());
                        }
                        completedCount++;
                        progress.setCompletedCommunities(completedCount);
                    } catch (Exception e) {
                        System.err.println("生成小区 " + community.getCommunityName() + " 汇总账单失败: " + e.getMessage());
                        // 继续处理其他小区
                    }
                }

                progress.setStatus(SummaryBillConstants.TaskStatus.COMPLETED);
                progress.setMessage("汇总账单生成完成，共处理 " + completedCount + " 个小区");
                progress.setEndTime(System.currentTimeMillis());

            } catch (Exception e) {
                progress.setStatus(SummaryBillConstants.TaskStatus.FAILED);
                progress.setMessage("生成汇总账单失败: " + e.getMessage());
                progress.setEndTime(System.currentTimeMillis());
            }
        });
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(Long communityId, Date startDate, Date endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat(SummaryBillConstants.DateFormat.DATE_SHORT);
        String communityStr = communityId != null ? communityId.toString() : SummaryBillConstants.ALL_COMMUNITIES;
        return SummaryBillConstants.TASK_ID_PREFIX + communityStr + "_" + sdf.format(startDate) + "_" + sdf.format(endDate);
    }

    /**
     * 生成汇总账单编号
     */
    private String generateSummaryNumber(Long communityId) {
        SimpleDateFormat sdf = new SimpleDateFormat(SummaryBillConstants.DateFormat.DATETIME_SHORT);
        return SummaryBillConstants.SUMMARY_NUMBER_PREFIX + communityId + sdf.format(new Date());
    }

    /**
     * 检查日期是否在范围内
     */
    private boolean isDateInRange(Date billStart, Date billEnd, Date rangeStart, Date rangeEnd) {
        if (billStart == null || billEnd == null) {
            return false;
        }
        // 账单时间段与查询时间段有重叠即算在范围内
        return !(billEnd.before(rangeStart) || billStart.after(rangeEnd));
    }
}
