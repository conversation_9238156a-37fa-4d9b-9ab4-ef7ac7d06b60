package com.estatemanagement.system.service.impl;

import java.math.BigDecimal;
import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Transactional;
import com.estatemanagement.system.mapper.ParkingInfoMapper;
import com.estatemanagement.system.mapper.CommunityMangementMapper;
import com.estatemanagement.system.mapper.OwnerMangementMapper;
import com.estatemanagement.system.domain.ParkingInfo;
import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.domain.OwnerMangement;
import com.estatemanagement.system.service.IParkingInfoService;

import static com.estatemanagement.common.utils.SecurityUtils.getUsername;

/**
 * 停车费信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class ParkingInfoServiceImpl implements IParkingInfoService 
{
    @Autowired
    private ParkingInfoMapper parkingInfoMapper;
    
    @Autowired
    private CommunityMangementMapper communityMangementMapper;
    
    @Autowired
    private OwnerMangementMapper ownerMangementMapper;

    /**
     * 查询停车费信息
     * 
     * @param id 停车费信息主键
     * @return 停车费信息
     */
    @Override
    public ParkingInfo selectParkingInfoById(Long id)
    {
        return parkingInfoMapper.selectParkingInfoById(id);
    }

    /**
     * 查询停车费信息列表
     *
     * @param parkingInfo 停车费信息
     * @return 停车费信息
     */
    @Override
    public List<ParkingInfo> selectParkingInfoList(ParkingInfo parkingInfo)
    {
        return parkingInfoMapper.selectParkingInfoList(parkingInfo);
    }

    /**
     * 查询车位管理列表（显示所有车位，包括空车位）
     *
     * @param parkingInfo 停车费信息
     * @return 停车费信息
     */
    @Override
    public List<ParkingInfo> selectParkingManagementList(ParkingInfo parkingInfo)
    {
        return parkingInfoMapper.selectParkingManagementList(parkingInfo);
    }

    /**
     * 新增停车费信息
     *
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertParkingInfo(ParkingInfo parkingInfo)
    {
        // 如果指定了车位号，检查是否已存在该车位
        if (parkingInfo.getSpaceNumber() != null && !parkingInfo.getSpaceNumber().trim().isEmpty()) {
            // 查询指定车位是否已存在
            ParkingInfo queryParking = new ParkingInfo();
            queryParking.setCommunityId(parkingInfo.getCommunityId());
            queryParking.setSpaceNumber(parkingInfo.getSpaceNumber());

            List<ParkingInfo> existingSpaces = parkingInfoMapper.selectParkingManagementList(queryParking);

            // 检查是否已存在相同车位号的记录
            ParkingInfo existingSpace = existingSpaces.stream()
                .filter(space -> parkingInfo.getSpaceNumber().equals(space.getSpaceNumber()))
                .findFirst()
                .orElse(null);

            if (existingSpace != null) {
                // 车位已存在，检查状态
                if (existingSpace.getStatus() == 0 &&
                    (existingSpace.getPlateNumber() == null || existingSpace.getPlateNumber().trim().isEmpty())) {
                    // 车位存在且为空闲状态，更新该车位记录
                    existingSpace.setOwnerId(parkingInfo.getOwnerId());
                    existingSpace.setTenantId(parkingInfo.getTenantId());
                    existingSpace.setPlateNumber(parkingInfo.getPlateNumber());
                    existingSpace.setIsThreeCertificates(parkingInfo.getIsThreeCertificates() != null ? parkingInfo.getIsThreeCertificates() : 0);
                    existingSpace.setStatus(1); // 设置为有效状态
                    existingSpace.setStartDate(parkingInfo.getStartDate());
                    existingSpace.setEndDate(parkingInfo.getEndDate());
                    existingSpace.setUpdateTime(DateUtils.getNowDate());
                    existingSpace.setUpdateBy(parkingInfo.getCreateBy());

                    // 自动计算月租金
                    calculateMonthlyFee(existingSpace);

                    return parkingInfoMapper.updateParkingInfo(existingSpace);
                } else {
                    // 车位已被占用
                    throw new RuntimeException("车位 " + parkingInfo.getSpaceNumber() + " 已被占用");
                }
            } else {
                // 车位不存在，创建新车位记录并绑定车牌
                // 不调用ensureParkingSpaceExists，直接创建绑定车牌的记录
                System.out.println("创建新车位并绑定车牌：小区ID=" + parkingInfo.getCommunityId() + ", 车位号=" + parkingInfo.getSpaceNumber());
            }
        }

        // 设置创建时间
        parkingInfo.setCreateTime(DateUtils.getNowDate());

        // 自动设置状态为有效
        if (parkingInfo.getStatus() == null) {
            parkingInfo.setStatus(1);
        }

        // 自动设置结束时间为开始时间（如果没有设置）
        if (parkingInfo.getEndDate() == null) {
            parkingInfo.setEndDate(parkingInfo.getStartDate());
        }

        // 自动计算月租金
        calculateMonthlyFee(parkingInfo);

        return parkingInfoMapper.insertParkingInfo(parkingInfo);
    }

    /**
     * 修改停车费信息
     *
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    @Override
    public int updateParkingInfo(ParkingInfo parkingInfo)
    {
        // 设置更新时间
        parkingInfo.setUpdateTime(DateUtils.getNowDate());

        // 自动重新计算月租金（从小区配置中获取）
        calculateMonthlyFee(parkingInfo);

        return parkingInfoMapper.updateParkingInfo(parkingInfo);
    }

    /**
     * 强制更新停车费信息（包括null值字段）
     * 用于车位释放等需要将字段设置为null的场景
     * 注意：此方法不会自动计算月租金，直接使用传入的值
     *
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    @Override
    public int forceUpdateParkingInfo(ParkingInfo parkingInfo)
    {
        // 1. 设置状态为无效/空闲状态
        parkingInfo.setStatus(0);

        // 2. 清空业主和租客信息
        parkingInfo.setOwnerId(null);
        parkingInfo.setTenantId(null);

        // 3. 清空车牌相关信息
        parkingInfo.setPlateNumber(null);
        parkingInfo.setIsThreeCertificates(null);

        // 4. 清空时间相关信息
        parkingInfo.setStartDate(null);
        parkingInfo.setEndDate(null);

        // 5. 清空费用信息
        parkingInfo.setMonthlyFee(BigDecimal.ZERO);

        // 6. 清空备注信息
        parkingInfo.setRemark(null);

        // 7. 设置更新信息
        parkingInfo.setUpdateBy(getUsername());
        parkingInfo.setUpdateTime(new java.util.Date());



        // 设置更新时间（如果没有设置的话）
        if (parkingInfo.getUpdateTime() == null) {
            parkingInfo.setUpdateTime(DateUtils.getNowDate());
        }

        // 记录强制更新操作的日志
        System.out.println("执行强制更新操作：车位ID=" + parkingInfo.getId() +
            ", 车位号=" + parkingInfo.getSpaceNumber() +
            ", 状态=" + parkingInfo.getStatus() +
            ", 业主ID=" + parkingInfo.getOwnerId() +
            ", 车牌号=" + parkingInfo.getPlateNumber());

        // 直接调用强制更新的mapper方法，不进行任何业务逻辑处理
        return parkingInfoMapper.forceUpdateParkingInfo(parkingInfo);
    }

    /**
     * 批量删除停车费信息
     * 
     * @param ids 需要删除的停车费信息主键
     * @return 结果
     */
    @Override
    public int deleteParkingInfoByIds(Long[] ids)
    {
        return parkingInfoMapper.deleteParkingInfoByIds(ids);
    }

    /**
     * 删除停车费信息信息
     *
     * @param id 停车费信息主键
     * @return 结果
     */
    @Override
    public int deleteParkingInfoById(Long id)
    {
        return parkingInfoMapper.deleteParkingInfoById(id);
    }

    /**
     * 批量创建车位
     *
     * @param request 批量创建请求参数
     * @return 创建的车位数量
     */
    @Override
    @Transactional
    public int batchCreateParkingSpaces(Object request) {
        try {
            // 使用反射获取参数值
            Class<?> clazz = request.getClass();
            Long communityId = (Long) clazz.getMethod("getCommunityId").invoke(request);
            String prefix = (String) clazz.getMethod("getPrefix").invoke(request);
            Integer startNum = (Integer) clazz.getMethod("getStartNum").invoke(request);
            Integer endNum = (Integer) clazz.getMethod("getEndNum").invoke(request);
            Integer numLength = (Integer) clazz.getMethod("getNumLength").invoke(request);

            System.out.println("批量创建车位参数：小区ID=" + communityId + ", 前缀=" + prefix +
                             ", 起始=" + startNum + ", 结束=" + endNum + ", 长度=" + numLength);

            int createdCount = 0;

            for (int i = startNum; i <= endNum; i++) {
                String spaceNumber = prefix + String.format("%0" + numLength + "d", i);

                // 检查车位号是否已存在（使用车位管理查询，包括所有车位）
                ParkingInfo existingParking = new ParkingInfo();
                existingParking.setCommunityId(communityId);
                existingParking.setSpaceNumber(spaceNumber);
                List<ParkingInfo> existingList = parkingInfoMapper.selectParkingManagementList(existingParking);

                System.out.println("检查车位 " + spaceNumber + " 是否存在，查询结果数量：" + existingList.size());

                // 检查是否已存在相同车位号的记录
                boolean spaceExists = existingList.stream()
                    .anyMatch(space -> spaceNumber.equals(space.getSpaceNumber()));

                System.out.println("车位 " + spaceNumber + " 是否存在：" + spaceExists);

                if (!spaceExists) {
                    ParkingInfo parkingInfo = new ParkingInfo();
                    parkingInfo.setCommunityId(communityId);
                    parkingInfo.setSpaceNumber(spaceNumber);
                    parkingInfo.setStatus(0); // 空闲状态
                    parkingInfo.setIsThreeCertificates(0); // 默认非三证合一
                    parkingInfo.setCreateTime(DateUtils.getNowDate());

                    // 设置默认月租金为0，避免数据库字段必填错误
                    parkingInfo.setMonthlyFee(BigDecimal.ZERO);

                    try {
                        parkingInfoMapper.insertParkingInfo(parkingInfo);
                        createdCount++;
                        System.out.println("成功创建车位：" + spaceNumber);
                    } catch (Exception e) {
                        System.err.println("创建车位失败：" + spaceNumber + ", 错误：" + e.getMessage());
                        // 继续创建其他车位，不中断整个批量创建过程
                    }
                } else {
                    System.out.println("车位已存在，跳过创建：" + spaceNumber);
                }
            }

            return createdCount;
        } catch (Exception e) {
            throw new RuntimeException("批量创建车位失败：" + e.getMessage(), e);
        }
    }

    /**
     * 计算月租金（从小区配置中获取）
     *
     * @param parkingInfo 停车信息
     */
    private void calculateMonthlyFee(ParkingInfo parkingInfo) {
        if (parkingInfo.getCommunityId() != null) {
            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(parkingInfo.getCommunityId());

            if (community != null) {
                BigDecimal monthlyFee = null;

                // 根据停车记录的三证合一状态选择对应的停车费
                if (parkingInfo.getIsThreeCertificates() != null && parkingInfo.getIsThreeCertificates() == 1) {
                    // 三证合一停车费（使用原业主停车费字段）
                    monthlyFee = community.getOwnerParkingFee();
                } else {
                    // 非三证合一停车费（使用原租客停车费字段）
                    monthlyFee = community.getTenantParkingFee();
                }

                // 总是使用小区配置的费用，重新计算月租金
                if (monthlyFee != null && monthlyFee.compareTo(BigDecimal.ZERO) > 0) {
                    parkingInfo.setMonthlyFee(monthlyFee);
                } else {
                    // 如果小区没有配置停车费，设置为0
                    parkingInfo.setMonthlyFee(BigDecimal.ZERO);
                }

                System.out.println("重新计算月租金：车位=" + parkingInfo.getSpaceNumber() +
                                 ", 三证合一=" + parkingInfo.getIsThreeCertificates() +
                                 ", 月租金=" + parkingInfo.getMonthlyFee());
            }
        }
    }

    /**
     * 根据小区ID查询有效停车记录（有车牌号和车位号的记录）
     *
     * @param communityId 小区ID
     * @return 有效停车记录集合
     */
    @Override
    public List<ParkingInfo> selectValidParkingByCommunity(Long communityId) {
        return parkingInfoMapper.selectValidParkingByCommunity(communityId);
    }
}
