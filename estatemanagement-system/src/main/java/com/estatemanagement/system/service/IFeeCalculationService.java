package com.estatemanagement.system.service;

import com.estatemanagement.system.domain.FeeCalculationResult;
import com.estatemanagement.system.domain.TimeCalculationResult;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 费用计算服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IFeeCalculationService {
    
    /**
     * 计算时间差（按自然月计算）
     * 时间计算公式：2023年10月10日-2024年10月9日为一年，2月5日-3月6日算一个月
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 时间计算结果
     */
    TimeCalculationResult calculateTimeDifference(LocalDate startDate, LocalDate endDate);
    
    /**
     * 计算费用
     * 金额计算公式：满一个月按照一个月计算，不满一个月，就按照当月总天数来计算每一天的金额，
     * 然后算一天多少钱，在舍去角分。满月的不舍弃角分，只有零散的天才舍弃角分。
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param monthlyFee 月费用
     * @return 费用计算结果
     */
    FeeCalculationResult calculateFee(LocalDate startDate, LocalDate endDate, BigDecimal monthlyFee);
    
    /**
     * 计算物业费
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param unitPrice 单价（每平方米每月）
     * @param area 房屋面积
     * @return 费用计算结果
     */
    FeeCalculationResult calculatePropertyFee(LocalDate startDate, LocalDate endDate, 
                                            BigDecimal unitPrice, BigDecimal area);
    
    /**
     * 计算停车费
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param monthlyParkingFee 月停车费
     * @return 费用计算结果
     */
    FeeCalculationResult calculateParkingFee(LocalDate startDate, LocalDate endDate, 
                                           BigDecimal monthlyParkingFee);
    
    /**
     * 计算卫生费
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param monthlySanitationFee 月卫生费
     * @return 费用计算结果
     */
    FeeCalculationResult calculateSanitationFee(LocalDate startDate, LocalDate endDate, 
                                              BigDecimal monthlySanitationFee);
    
    /**
     * 计算电梯费
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param monthlyElevatorFee 月电梯费
     * @return 费用计算结果
     */
    FeeCalculationResult calculateElevatorFee(LocalDate startDate, LocalDate endDate, 
                                            BigDecimal monthlyElevatorFee);
    
    /**
     * 根据月数计算结束日期（按自然月计算）
     *
     * @param startDate 开始日期
     * @param months 月数
     * @return 结束日期
     */
    LocalDate calculateEndDateByMonths(LocalDate startDate, int months);

    /**
     * 获取指定月份的天数
     *
     * @param year 年份
     * @param month 月份
     * @return 天数
     */
    int getDaysInMonth(int year, int month);

    /**
     * 统一费用计算API - 根据小区、业主、费用类型和时间段计算费用
     *
     * @param communityId 小区ID
     * @param ownerId 业主ID
     * @param feeType 费用类型（1-物业费，2-停车费，3-卫生费，4-电梯费）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param plateNumber 车牌号（停车费时必填）
     * @return 费用计算结果
     */
    FeeCalculationResult calculateUnifiedFee(Long communityId, Long ownerId, Integer feeType,
                                           LocalDate startDate, LocalDate endDate, String plateNumber);
}
