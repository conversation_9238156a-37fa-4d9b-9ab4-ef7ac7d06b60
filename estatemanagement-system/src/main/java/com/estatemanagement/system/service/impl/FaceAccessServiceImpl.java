package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.FaceAccessMapper;
import com.estatemanagement.system.domain.FaceAccess;
import com.estatemanagement.system.service.IFaceAccessService;

/**
 * 人脸识别开通记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class FaceAccessServiceImpl implements IFaceAccessService 
{
    @Autowired
    private FaceAccessMapper faceAccessMapper;

    /**
     * 查询人脸识别开通记录
     * 
     * @param id 人脸识别开通记录主键
     * @return 人脸识别开通记录
     */
    @Override
    public FaceAccess selectFaceAccessById(Long id)
    {
        return faceAccessMapper.selectFaceAccessById(id);
    }

    /**
     * 查询人脸识别开通记录列表
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 人脸识别开通记录
     */
    @Override
    public List<FaceAccess> selectFaceAccessList(FaceAccess faceAccess)
    {
        return faceAccessMapper.selectFaceAccessList(faceAccess);
    }

    /**
     * 新增人脸识别开通记录
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 结果
     */
    @Override
    public int insertFaceAccess(FaceAccess faceAccess)
    {
        faceAccess.setCreateTime(DateUtils.getNowDate());
        return faceAccessMapper.insertFaceAccess(faceAccess);
    }

    /**
     * 修改人脸识别开通记录
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 结果
     */
    @Override
    public int updateFaceAccess(FaceAccess faceAccess)
    {
        return faceAccessMapper.updateFaceAccess(faceAccess);
    }

    /**
     * 批量删除人脸识别开通记录
     * 
     * @param ids 需要删除的人脸识别开通记录主键
     * @return 结果
     */
    @Override
    public int deleteFaceAccessByIds(Long[] ids)
    {
        return faceAccessMapper.deleteFaceAccessByIds(ids);
    }

    /**
     * 删除人脸识别开通记录信息
     * 
     * @param id 人脸识别开通记录主键
     * @return 结果
     */
    @Override
    public int deleteFaceAccessById(Long id)
    {
        return faceAccessMapper.deleteFaceAccessById(id);
    }
}
