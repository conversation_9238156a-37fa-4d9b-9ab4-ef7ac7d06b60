package com.estatemanagement.system.service.impl;

import com.estatemanagement.system.domain.FeeCalculationResult;
import com.estatemanagement.system.domain.TimeCalculationResult;
import com.estatemanagement.system.service.IFeeCalculationService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 费用计算服务实现
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class FeeCalculationServiceImpl implements IFeeCalculationService {
    
    @Override
    public TimeCalculationResult calculateTimeDifference(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }

        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        // 计算总天数
        long totalDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;

        // 按自然月计算月数
        int fullMonths = 0;
        int remainingDays = 0;
        LocalDate currentDate = startDate;

        // 计算完整月数
        // 修复逻辑：正确处理月末日期的计算
        while (true) {
            // 计算下个月的对应日期
            LocalDate nextMonth = currentDate.plusMonths(1);

            // 处理月末日期的特殊情况
            // 如果原始开始日期是月末，那么每个月的对应日期都应该是月末
            if (isLastDayOfMonth(startDate)) {
                nextMonth = nextMonth.withDayOfMonth(nextMonth.lengthOfMonth());
            }

            // 如果下个月的对应日期超过了结束日期，则停止计算完整月
            // 特殊处理：如果原始开始日期是月末，且结束日期也是对应的月末前一天，则算完整月
            if (nextMonth.isAfter(endDate)) {
                // 特殊情况：如果开始日期是月末，结束日期是下个月的月末前一天，算完整月
                if (isLastDayOfMonth(startDate) && endDate.equals(nextMonth.minusDays(1))) {
                    fullMonths++;
                    currentDate = nextMonth;
                }
                break;
            }

            // 如果下个月的对应日期等于结束日期，也算完整月
            if (nextMonth.equals(endDate)) {
                fullMonths++;
                currentDate = nextMonth;
                break;
            }

            fullMonths++;
            currentDate = nextMonth;
        }

        // 计算剩余天数
        if (currentDate.isBefore(endDate) || currentDate.isEqual(endDate)) {
            remainingDays = (int) ChronoUnit.DAYS.between(currentDate, endDate) + 1;
        }

        // 计算总年数和总月数
        int totalYears = fullMonths / 12;
        int totalMonths = fullMonths;

        // 判断是否有不完整月份
        boolean hasPartialMonth = remainingDays > 0;

        return new TimeCalculationResult(
            startDate, endDate, totalYears, totalMonths, totalDays,
            fullMonths, remainingDays, hasPartialMonth
        );
    }

    /**
     * 判断是否是月末最后一天
     */
    private boolean isLastDayOfMonth(LocalDate date) {
        return date.getDayOfMonth() == date.lengthOfMonth();
    }

    /**
     * 检查是否是标准预交费用模式（结束日期 = 开始日期 + N个月 - 1天）
     */
    private boolean isStandardAdvancePaymentPeriod(LocalDate startDate, LocalDate endDate) {
        // 尝试1-24个月，看是否有匹配的
        for (int months = 1; months <= 24; months++) {
            LocalDate expectedEndDate = startDate.plusMonths(months).minusDays(1);
            if (expectedEndDate.equals(endDate)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算两个日期之间的月数（用于标准预交费用）
     */
    private int calculateMonthsBetweenDates(LocalDate startDate, LocalDate endDate) {
        for (int months = 1; months <= 24; months++) {
            LocalDate expectedEndDate = startDate.plusMonths(months).minusDays(1);
            if (expectedEndDate.equals(endDate)) {
                return months;
            }
        }
        return 1; // 默认返回1个月
    }
    
    @Override
    public FeeCalculationResult calculateFee(LocalDate startDate, LocalDate endDate, BigDecimal monthlyFee) {
        if (monthlyFee == null || monthlyFee.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("月费用不能为空或负数");
        }

        // 添加调试日志
        System.out.println("=== 费用计算调试 ===");
        System.out.println("开始日期: " + startDate);
        System.out.println("结束日期: " + endDate);
        System.out.println("月费用: " + monthlyFee);

        // 检查是否是预交费用的标准模式（结束日期 = 开始日期 + N个月 - 1天）
        boolean isStandardAdvancePayment = isStandardAdvancePaymentPeriod(startDate, endDate);

        TimeCalculationResult timeResult;
        if (isStandardAdvancePayment) {
            // 对于标准预交费用，直接计算月数
            int months = calculateMonthsBetweenDates(startDate, endDate);
            timeResult = new TimeCalculationResult(
                startDate, endDate, months / 12, months,
                ChronoUnit.DAYS.between(startDate, endDate) + 1,
                months, 0, false
            );
            System.out.println("检测到标准预交费用模式，直接计算月数: " + months);
        } else {
            // 对于其他情况，使用原有的复杂时间计算
            timeResult = calculateTimeDifference(startDate, endDate);
        }

        System.out.println("完整月数: " + timeResult.getFullMonths());
        System.out.println("剩余天数: " + timeResult.getRemainingDays());

        // 初始化日费用（将在剩余天数计算中使用实际月份天数）
        BigDecimal dailyFee = BigDecimal.ZERO;

        // 计算完整月费用（保留角分，舍去分以下位数）
        BigDecimal fullMonthsFee = monthlyFee.multiply(new BigDecimal(timeResult.getFullMonths()))
                                            .setScale(0, RoundingMode.DOWN);

        System.out.println("完整月费用计算: " + monthlyFee + " × " + timeResult.getFullMonths() + " = " + fullMonthsFee);

        // 计算剩余天数费用（舍去角分）
        BigDecimal remainingDaysFee = BigDecimal.ZERO;
        if (timeResult.getRemainingDays() > 0) {
            // 获取剩余天数所在月份的实际天数
            LocalDate remainingStartDate = startDate.plusMonths(timeResult.getFullMonths());
            int daysInMonth = getDaysInMonth(remainingStartDate.getYear(), remainingStartDate.getMonthValue());

            // 按实际月天数计算日费用
            dailyFee = monthlyFee.divide(new BigDecimal(daysInMonth), 4, RoundingMode.HALF_UP);

            // 计算剩余天数费用并舍去角分
            remainingDaysFee = dailyFee.multiply(new BigDecimal(timeResult.getRemainingDays()))
                                      .setScale(0, RoundingMode.DOWN);

            System.out.println("剩余天数费用计算: " + dailyFee + " × " + timeResult.getRemainingDays() + " = " + remainingDaysFee);
        }

        // 计算总费用
        BigDecimal totalFee = fullMonthsFee.add(remainingDaysFee);

        System.out.println("总费用: " + fullMonthsFee + " + " + remainingDaysFee + " = " + totalFee);
        System.out.println("=== 费用计算调试结束 ===");
        
        // 生成计算说明
        String description;
        if (timeResult.getRemainingDays() > 0) {
            description = String.format(
                "计算期间：%s 至 %s，共 %d 个完整月，%d 天零散天数。" +
                "完整月费用：%.2f元（%d月 × %.2f元/月），" +
                "零散天数费用：%.0f元（%d天，舍去角分）",
                startDate, endDate, timeResult.getFullMonths(), timeResult.getRemainingDays(),
                fullMonthsFee, timeResult.getFullMonths(), monthlyFee,
                remainingDaysFee, timeResult.getRemainingDays()
            );
        } else {
            description = String.format(
                "计算期间：%s 至 %s，共 %d 个完整月。" +
                "完整月费用：%.2f元（%d月 × %.2f元/月）",
                startDate, endDate, timeResult.getFullMonths(),
                fullMonthsFee, timeResult.getFullMonths(), monthlyFee
            );
        }
        
        return new FeeCalculationResult(
            startDate, endDate, monthlyFee, dailyFee,
            timeResult.getFullMonths(), timeResult.getRemainingDays(),
            fullMonthsFee, remainingDaysFee, totalFee, timeResult, description
        );
    }
    
    @Override
    public FeeCalculationResult calculatePropertyFee(LocalDate startDate, LocalDate endDate, 
                                                   BigDecimal unitPrice, BigDecimal area) {
        if (unitPrice == null || area == null) {
            throw new IllegalArgumentException("单价和面积不能为空");
        }
        
        BigDecimal monthlyFee = unitPrice.multiply(area);
        FeeCalculationResult result = calculateFee(startDate, endDate, monthlyFee);
        result.setDescription("物业费计算：" + result.getDescription());
        
        return result;
    }
    
    @Override
    public FeeCalculationResult calculateParkingFee(LocalDate startDate, LocalDate endDate, 
                                                  BigDecimal monthlyParkingFee) {
        FeeCalculationResult result = calculateFee(startDate, endDate, monthlyParkingFee);
        result.setDescription("停车费计算：" + result.getDescription());
        
        return result;
    }
    
    @Override
    public FeeCalculationResult calculateSanitationFee(LocalDate startDate, LocalDate endDate, 
                                                     BigDecimal monthlySanitationFee) {
        FeeCalculationResult result = calculateFee(startDate, endDate, monthlySanitationFee);
        result.setDescription("卫生费计算：" + result.getDescription());
        
        return result;
    }
    
    @Override
    public FeeCalculationResult calculateElevatorFee(LocalDate startDate, LocalDate endDate, 
                                                   BigDecimal monthlyElevatorFee) {
        FeeCalculationResult result = calculateFee(startDate, endDate, monthlyElevatorFee);
        result.setDescription("电梯费计算：" + result.getDescription());
        
        return result;
    }
    
    @Override
    public LocalDate calculateEndDateByMonths(LocalDate startDate, int months) {
        if (startDate == null) {
            throw new IllegalArgumentException("开始日期不能为空");
        }

        if (months <= 0) {
            throw new IllegalArgumentException("月数必须大于0");
        }

        // 按自然月计算，减去1天是因为包含开始日期
        LocalDate endDate = startDate.plusMonths(months).minusDays(1);

        System.out.println("=== 结束日期计算调试 ===");
        System.out.println("开始日期: " + startDate);
        System.out.println("月数: " + months);
        System.out.println("结束日期: " + endDate);
        System.out.println("=== 结束日期计算调试结束 ===");

        return endDate;
    }
    
    @Override
    public int getDaysInMonth(int year, int month) {
        LocalDate date = LocalDate.of(year, month, 1);
        return date.lengthOfMonth();
    }

    @Override
    public FeeCalculationResult calculateUnifiedFee(Long communityId, Long ownerId, Integer feeType,
                                                   LocalDate startDate, LocalDate endDate, String plateNumber) {
        // 这个方法需要注入其他服务，暂时抛出异常提示需要在Controller层实现
        throw new UnsupportedOperationException("统一费用计算API需要在Controller层实现，因为需要访问数据库");
    }
}
