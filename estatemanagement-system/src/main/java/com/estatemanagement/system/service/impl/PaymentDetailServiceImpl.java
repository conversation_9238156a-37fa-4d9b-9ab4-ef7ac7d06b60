package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.PaymentDetailMapper;
import com.estatemanagement.system.domain.PaymentDetail;
import com.estatemanagement.system.service.IPaymentDetailService;

/**
 * 收费明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class PaymentDetailServiceImpl implements IPaymentDetailService 
{
    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    /**
     * 查询收费明细
     * 
     * @param id 收费明细主键
     * @return 收费明细
     */
    @Override
    public PaymentDetail selectPaymentDetailById(Long id)
    {
        return paymentDetailMapper.selectPaymentDetailById(id);
    }

    /**
     * 查询收费明细列表
     * 
     * @param paymentDetail 收费明细
     * @return 收费明细
     */
    @Override
    public List<PaymentDetail> selectPaymentDetailList(PaymentDetail paymentDetail)
    {
        return paymentDetailMapper.selectPaymentDetailList(paymentDetail);
    }

    /**
     * 新增收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    @Override
    public int insertPaymentDetail(PaymentDetail paymentDetail)
    {
        paymentDetail.setCreateTime(DateUtils.getNowDate());
        return paymentDetailMapper.insertPaymentDetail(paymentDetail);
    }

    /**
     * 修改收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    @Override
    public int updatePaymentDetail(PaymentDetail paymentDetail)
    {
        return paymentDetailMapper.updatePaymentDetail(paymentDetail);
    }

    /**
     * 批量删除收费明细
     * 
     * @param ids 需要删除的收费明细主键
     * @return 结果
     */
    @Override
    public int deletePaymentDetailByIds(Long[] ids)
    {
        return paymentDetailMapper.deletePaymentDetailByIds(ids);
    }

    /**
     * 删除收费明细信息
     * 
     * @param id 收费明细主键
     * @return 结果
     */
    @Override
    public int deletePaymentDetailById(Long id)
    {
        return paymentDetailMapper.deletePaymentDetailById(id);
    }
}
