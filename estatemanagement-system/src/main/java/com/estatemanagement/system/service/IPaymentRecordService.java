package com.estatemanagement.system.service;

import java.util.List;
import java.util.Map;
import com.estatemanagement.system.domain.PaymentRecord;

/**
 * 收费记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IPaymentRecordService 
{
    /**
     * 查询收费记录
     * 
     * @param id 收费记录主键
     * @return 收费记录
     */
    public PaymentRecord selectPaymentRecordById(Long id);

    /**
     * 查询收费记录列表
     * 
     * @param paymentRecord 收费记录
     * @return 收费记录集合
     */
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord);

    /**
     * 查询业主的未完全支付账单，用于收费管理
     * 
     * @param communityId 小区ID（可选）
     * @param ownerId 业主ID（可选）
     * @return 账单列表
     */
    public List<Map<String, Object>> selectUnpaidBillsForPayment(Long communityId, Long ownerId);

    /**
     * 执行收费操作
     * 
     * @param paymentData 收费数据
     * @return 收费记录ID
     */
    public Long processPayment(Map<String, Object> paymentData);

    /**
     * 查询指定账单的收费记录及明细
     * 
     * @param billId 账单ID
     * @return 收费记录及明细列表
     */
    public List<Map<String, Object>> selectPaymentRecordsByBillId(Long billId);

    /**
     * 新增收费记录
     * 
     * @param paymentRecord 收费记录
     * @return 结果
     */
    public int insertPaymentRecord(PaymentRecord paymentRecord);

    /**
     * 修改收费记录
     * 
     * @param paymentRecord 收费记录
     * @return 结果
     */
    public int updatePaymentRecord(PaymentRecord paymentRecord);

    /**
     * 批量删除收费记录
     * 
     * @param ids 需要删除的收费记录主键集合
     * @return 结果
     */
    public int deletePaymentRecordByIds(Long[] ids);

    /**
     * 删除收费记录信息
     * 
     * @param id 收费记录主键
     * @return 结果
     */
    public int deletePaymentRecordById(Long id);

    /**
     * 计算预交费用金额
     * 
     * @param communityId 小区ID
     * @param ownerId 业主ID
     * @param feeType 费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months 预交月数
     * @return 预交费用金额
     */
    public java.math.BigDecimal calculateAdvanceFeeAmount(Long communityId, Long ownerId, Integer feeType, Integer months);

    /**
     * 根据收费记录ID查询收费明细
     *
     * @param paymentId 收费记录ID
     * @return 收费明细集合
     */
    public List<Map<String, Object>> selectPaymentDetailsByPaymentId(Long paymentId);

    /**
     * 计算部分支付费用金额
     *
     * @param communityId 小区ID
     * @param ownerId 业主ID
     * @param billId 账单ID（可选）
     * @param feeType 费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months 支付月数
     * @param plateNumber 车牌号（停车费时需要）
     * @return 部分支付费用金额
     */
    public java.math.BigDecimal calculatePartialFeeAmount(Long communityId, Long ownerId, Long billId, Integer feeType, Integer months, String plateNumber);

    /**
     * 计算部分支付的详细信息（包括缴费周期）
     *
     * @param communityId 小区ID
     * @param ownerId 业主ID
     * @param billId 账单ID（必填）
     * @param feeType 费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months 支付月数
     * @param plateNumber 车牌号（停车费时需要）
     * @return 包含金额、开始时间、结束时间的Map
     */
    public java.util.Map<String, Object> calculatePartialFeeDetails(Long communityId, Long ownerId, Long billId, Integer feeType, Integer months, String plateNumber);

    /**
     * 查询收费记录详情（包含明细）用于打印
     *
     * @param paymentId 收费记录ID
     * @return 包含收费记录和明细的Map对象
     */
    public java.util.Map<String, Object> selectPaymentRecordWithDetails(Long paymentId);

    /**
     * 计算混合支付金额
     *
     * @param params 计算参数
     * @return 计算结果
     */
    public java.util.Map<String, Object> calculateMixedPaymentAmount(java.util.Map<String, Object> params);

    /**
     * 审核收费记录
     *
     * @param paymentId 收费记录ID
     * @param auditStatus 审核状态（1通过，2拒绝）
     * @param auditComment 审核意见
     * @param auditorId 审核人ID
     * @return 审核结果
     */
    public boolean auditPaymentRecord(Long paymentId, Integer auditStatus, String auditComment, Long auditorId);

    /**
     * 批量审核收费记录
     *
     * @param paymentIds 收费记录ID数组
     * @param auditStatus 审核状态（1通过，2拒绝）
     * @param auditComment 审核意见
     * @param auditorId 审核人ID
     * @return 审核结果
     */
    public boolean batchAuditPaymentRecords(Long[] paymentIds, Integer auditStatus, String auditComment, Long auditorId);

    /**
     * 查询待审核的收费记录列表
     *
     * @param paymentRecord 查询条件
     * @return 待审核收费记录列表
     */
    public List<PaymentRecord> selectPendingAuditPaymentRecords(PaymentRecord paymentRecord);

    /**
     * 获取收费记录审核统计信息
     *
     * @param communityId 小区ID（可选）
     * @return 审核统计信息
     */
    public java.util.Map<String, Object> getPaymentAuditStatistics(Long communityId);
}
