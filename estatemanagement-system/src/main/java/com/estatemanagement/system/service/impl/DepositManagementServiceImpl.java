package com.estatemanagement.system.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.estatemanagement.system.mapper.DepositManagementMapper;
import com.estatemanagement.system.domain.DepositManagement;
import com.estatemanagement.system.service.IDepositManagementService;

/**
 * 押金管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class DepositManagementServiceImpl implements IDepositManagementService 
{
    @Autowired
    private DepositManagementMapper depositManagementMapper;

    /**
     * 查询押金管理
     * 
     * @param id 押金管理主键
     * @return 押金管理
     */
    @Override
    public DepositManagement selectDepositManagementById(Long id)
    {
        return depositManagementMapper.selectDepositManagementById(id);
    }

    /**
     * 查询押金管理列表
     * 
     * @param depositManagement 押金管理
     * @return 押金管理
     */
    @Override
    public List<DepositManagement> selectDepositManagementList(DepositManagement depositManagement)
    {
        return depositManagementMapper.selectDepositManagementList(depositManagement);
    }

    /**
     * 新增押金管理
     * 
     * @param depositManagement 押金管理
     * @return 结果
     */
    @Override
    public int insertDepositManagement(DepositManagement depositManagement)
    {
        // 设置默认值
        if (depositManagement.getCollectDate() == null) {
            depositManagement.setCollectDate(DateUtils.getNowDate());
        }
        if (depositManagement.getStatus() == null) {
            depositManagement.setStatus(0); // 默认为已收取状态
        }
        
        // 生成收据编号
        if (depositManagement.getReceiptNumber() == null || depositManagement.getReceiptNumber().isEmpty()) {
            depositManagement.setReceiptNumber(generateReceiptNumber(depositManagement.getCommunityId()));
        }
        
        depositManagement.setCreateTime(DateUtils.getNowDate());
        return depositManagementMapper.insertDepositManagement(depositManagement);
    }

    /**
     * 修改押金管理
     * 
     * @param depositManagement 押金管理
     * @return 结果
     */
    @Override
    public int updateDepositManagement(DepositManagement depositManagement)
    {
        depositManagement.setUpdateTime(DateUtils.getNowDate());
        return depositManagementMapper.updateDepositManagement(depositManagement);
    }

    /**
     * 批量删除押金管理
     * 
     * @param ids 需要删除的押金管理主键
     * @return 结果
     */
    @Override
    public int deleteDepositManagementByIds(Long[] ids)
    {
        return depositManagementMapper.deleteDepositManagementByIds(ids);
    }

    /**
     * 删除押金管理信息
     * 
     * @param id 押金管理主键
     * @return 结果
     */
    @Override
    public int deleteDepositManagementById(Long id)
    {
        return depositManagementMapper.deleteDepositManagementById(id);
    }

    /**
     * 退还押金
     * 
     * @param depositManagement 押金管理信息（包含退还相关字段）
     * @return 结果
     */
    @Override
    @Transactional
    public int refundDeposit(DepositManagement depositManagement)
    {
        // 获取原始押金记录
        DepositManagement originalDeposit = depositManagementMapper.selectDepositManagementById(depositManagement.getId());
        if (originalDeposit == null) {
            throw new RuntimeException("押金记录不存在");
        }
        
        // 检查押金状态
        if (originalDeposit.getStatus() == 1) {
            throw new RuntimeException("押金已全额退还，无法重复退还");
        }
        
        // 计算已退还金额
        BigDecimal alreadyRefunded = originalDeposit.getRefundAmount() != null ? originalDeposit.getRefundAmount() : BigDecimal.ZERO;
        BigDecimal newRefundAmount = depositManagement.getRefundAmount();
        BigDecimal totalRefunded = alreadyRefunded.add(newRefundAmount);
        
        // 检查退还金额是否超过押金金额
        if (totalRefunded.compareTo(originalDeposit.getDepositAmount()) > 0) {
            throw new RuntimeException("退还金额不能超过押金金额");
        }
        
        // 更新退还信息
        originalDeposit.setRefundDate(depositManagement.getRefundDate() != null ? depositManagement.getRefundDate() : DateUtils.getNowDate());
        originalDeposit.setRefundAmount(totalRefunded);
        originalDeposit.setRefundUserId(depositManagement.getRefundUserId());
        originalDeposit.setRefundUserName(depositManagement.getRefundUserName());
        originalDeposit.setRefundReason(depositManagement.getRefundReason());
        originalDeposit.setUpdateTime(DateUtils.getNowDate());
        
        // 更新状态
        if (totalRefunded.compareTo(originalDeposit.getDepositAmount()) == 0) {
            originalDeposit.setStatus(1); // 已退还
        } else {
            originalDeposit.setStatus(2); // 部分退还
        }
        
        return depositManagementMapper.updateDepositManagement(originalDeposit);
    }
    
    /**
     * 生成收据编号
     */
    private String generateReceiptNumber(Long communityId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = String.format("YJ%03d%s", communityId, dateStr);
        
        // 查询当天该小区已生成的押金收据数量
        DepositManagement queryDeposit = new DepositManagement();
        queryDeposit.setCommunityId(communityId);
        List<DepositManagement> todayRecords = depositManagementMapper.selectDepositManagementList(queryDeposit);
        
        // 过滤当天的记录
        long todayCount = todayRecords.stream()
            .filter(record -> record.getCreateTime() != null && 
                    sdf.format(record.getCreateTime()).equals(dateStr))
            .count();
        
        // 生成序号（从001开始）
        String sequence = String.format("%03d", todayCount + 1);
        
        return prefix + sequence;
    }
}
