package com.estatemanagement.system.service;

import java.util.List;
import com.estatemanagement.system.domain.ElevatorFeeBill;

/**
 * 电梯费Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IElevatorFeeBillService 
{
    /**
     * 查询电梯费
     * 
     * @param id 电梯费主键
     * @return 电梯费
     */
    public ElevatorFeeBill selectElevatorFeeBillById(Long id);

    /**
     * 查询电梯费列表
     * 
     * @param elevatorFeeBill 电梯费
     * @return 电梯费集合
     */
    public List<ElevatorFeeBill> selectElevatorFeeBillList(ElevatorFeeBill elevatorFeeBill);

    /**
     * 新增电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    public int insertElevatorFeeBill(ElevatorFeeBill elevatorFeeBill);

    /**
     * 修改电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    public int updateElevatorFeeBill(ElevatorFeeBill elevatorFeeBill);

    /**
     * 批量删除电梯费
     * 
     * @param ids 需要删除的电梯费主键集合
     * @return 结果
     */
    public int deleteElevatorFeeBillByIds(Long[] ids);

    /**
     * 删除电梯费信息
     * 
     * @param id 电梯费主键
     * @return 结果
     */
    public int deleteElevatorFeeBillById(Long id);
}
