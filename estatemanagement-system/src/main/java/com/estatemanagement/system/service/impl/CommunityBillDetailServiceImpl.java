package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.CommunityBillDetailMapper;
import com.estatemanagement.system.domain.CommunityBillDetail;
import com.estatemanagement.system.service.ICommunityBillDetailService;

/**
 * 小区账单明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class CommunityBillDetailServiceImpl implements ICommunityBillDetailService 
{
    @Autowired
    private CommunityBillDetailMapper communityBillDetailMapper;

    /**
     * 查询小区账单明细
     * 
     * @param id 小区账单明细主键
     * @return 小区账单明细
     */
    @Override
    public CommunityBillDetail selectCommunityBillDetailById(Long id)
    {
        return communityBillDetailMapper.selectCommunityBillDetailById(id);
    }

    /**
     * 查询小区账单明细列表
     * 
     * @param communityBillDetail 小区账单明细
     * @return 小区账单明细
     */
    @Override
    public List<CommunityBillDetail> selectCommunityBillDetailList(CommunityBillDetail communityBillDetail)
    {
        return communityBillDetailMapper.selectCommunityBillDetailList(communityBillDetail);
    }

    /**
     * 新增小区账单明细
     * 
     * @param communityBillDetail 小区账单明细
     * @return 结果
     */
    @Override
    public int insertCommunityBillDetail(CommunityBillDetail communityBillDetail)
    {
        communityBillDetail.setCreateTime(DateUtils.getNowDate());
        return communityBillDetailMapper.insertCommunityBillDetail(communityBillDetail);
    }

    /**
     * 修改小区账单明细
     * 
     * @param communityBillDetail 小区账单明细
     * @return 结果
     */
    @Override
    public int updateCommunityBillDetail(CommunityBillDetail communityBillDetail)
    {
        communityBillDetail.setUpdateTime(DateUtils.getNowDate());
        return communityBillDetailMapper.updateCommunityBillDetail(communityBillDetail);
    }

    /**
     * 批量删除小区账单明细
     * 
     * @param ids 需要删除的小区账单明细主键
     * @return 结果
     */
    @Override
    public int deleteCommunityBillDetailByIds(Long[] ids)
    {
        return communityBillDetailMapper.deleteCommunityBillDetailByIds(ids);
    }

    /**
     * 删除小区账单明细信息
     * 
     * @param id 小区账单明细主键
     * @return 结果
     */
    @Override
    public int deleteCommunityBillDetailById(Long id)
    {
        return communityBillDetailMapper.deleteCommunityBillDetailById(id);
    }
}
