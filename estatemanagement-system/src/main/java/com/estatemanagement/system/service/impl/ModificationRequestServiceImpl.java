package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.ModificationRequestMapper;
import com.estatemanagement.system.domain.ModificationRequest;
import com.estatemanagement.system.service.IModificationRequestService;

/**
 * 修改申请工单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class ModificationRequestServiceImpl implements IModificationRequestService 
{
    @Autowired
    private ModificationRequestMapper modificationRequestMapper;

    /**
     * 查询修改申请工单
     * 
     * @param id 修改申请工单主键
     * @return 修改申请工单
     */
    @Override
    public ModificationRequest selectModificationRequestById(Long id)
    {
        return modificationRequestMapper.selectModificationRequestById(id);
    }

    /**
     * 查询修改申请工单列表
     * 
     * @param modificationRequest 修改申请工单
     * @return 修改申请工单
     */
    @Override
    public List<ModificationRequest> selectModificationRequestList(ModificationRequest modificationRequest)
    {
        return modificationRequestMapper.selectModificationRequestList(modificationRequest);
    }

    /**
     * 新增修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    @Override
    public int insertModificationRequest(ModificationRequest modificationRequest)
    {
        modificationRequest.setCreateTime(DateUtils.getNowDate());
        return modificationRequestMapper.insertModificationRequest(modificationRequest);
    }

    /**
     * 修改修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    @Override
    public int updateModificationRequest(ModificationRequest modificationRequest)
    {
        modificationRequest.setUpdateTime(DateUtils.getNowDate());
        return modificationRequestMapper.updateModificationRequest(modificationRequest);
    }

    /**
     * 批量删除修改申请工单
     * 
     * @param ids 需要删除的修改申请工单主键
     * @return 结果
     */
    @Override
    public int deleteModificationRequestByIds(Long[] ids)
    {
        return modificationRequestMapper.deleteModificationRequestByIds(ids);
    }

    /**
     * 删除修改申请工单信息
     * 
     * @param id 修改申请工单主键
     * @return 结果
     */
    @Override
    public int deleteModificationRequestById(Long id)
    {
        return modificationRequestMapper.deleteModificationRequestById(id);
    }
}
