package com.estatemanagement.system.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;

import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.system.utils.SanitationFeeCalculator;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.CommunityBillMapper;
import com.estatemanagement.system.mapper.CommunityBillDetailMapper;
import com.estatemanagement.system.mapper.CommunityMangementMapper;
import com.estatemanagement.system.mapper.OwnerMangementMapper;
import com.estatemanagement.system.mapper.ParkingInfoMapper;
import com.estatemanagement.system.mapper.PaymentDetailMapper;
import com.estatemanagement.system.domain.CommunityBill;
import com.estatemanagement.system.domain.CommunityBillDetail;
import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.domain.OwnerMangement;
import com.estatemanagement.system.domain.ParkingInfo;
import com.estatemanagement.system.domain.FeeCalculationResult;
import com.estatemanagement.system.service.ICommunityBillService;
import com.estatemanagement.system.service.IFeeCalculationService;

import static com.estatemanagement.system.constants.SummaryBillConstants.COMMUNITY_BILL_PREFIX;

/**
 * 小区账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
@Slf4j
public class CommunityBillServiceImpl implements ICommunityBillService {
    @Autowired
    private CommunityBillMapper communityBillMapper;

    @Autowired
    private CommunityBillDetailMapper communityBillDetailMapper;

    @Autowired
    private CommunityMangementMapper communityMangementMapper;

    @Autowired
    private OwnerMangementMapper ownerMangementMapper;

    @Autowired
    private ParkingInfoMapper parkingInfoMapper;

    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    @Autowired
    private IFeeCalculationService feeCalculationService;

    // 存储生成任务的进度信息
    private static final Map<String, BillGenerationProgress> progressMap = new ConcurrentHashMap<>();

    /**
     * 账单生成进度信息
     */
    @Setter
    @Getter
    public static class BillGenerationProgress {
        private String status; // RUNNING, COMPLETED, FAILED
        private int totalCount;
        private int processedCount;
        private int successCount;
        private String message;
        private long startTime;
        private long endTime;

        // 构造函数和getter/setter方法
        public BillGenerationProgress() {
            this.startTime = System.currentTimeMillis();
        }
    }

    /**
     * 查询小区账单
     *
     * @param id 小区账单主键
     * @return 小区账单
     */
    @Override
    public CommunityBill selectCommunityBillById(Long id) {
        return communityBillMapper.selectCommunityBillById(id);
    }

    /**
     * 查询小区账单详情（包含明细）
     *
     * @param id 小区账单主键
     * @return 包含账单和明细的Map对象
     */
    @Override
    public Map<String, Object> selectCommunityBillWithDetails(Long id) {
        Map<String, Object> result = new HashMap<>();

        // 查询账单信息
        CommunityBill bill = communityBillMapper.selectCommunityBillById(id);
        result.put("bill", bill);

        // 查询账单明细
        CommunityBillDetail queryDetail = new CommunityBillDetail();
        queryDetail.setBillId(id);
        List<CommunityBillDetail> details = communityBillDetailMapper.selectCommunityBillDetailList(queryDetail);

        // 创建新的List来存储包含已交金额和欠费金额的明细
        List<Map<String, Object>> detailsWithPayment = new ArrayList<>();

        // 为每个明细计算已交金额和欠费金额
        for (CommunityBillDetail detail : details) {
            // 查询该费用类型的已支付金额
            List<Map<String, Object>> paymentDetails = paymentDetailMapper.selectPaymentDetailsByBillAndFeeType(id, detail.getFeeType().intValue());

            BigDecimal paidAmount = BigDecimal.ZERO;
            for (Map<String, Object> paymentDetail : paymentDetails) {
                BigDecimal amount = (BigDecimal) paymentDetail.get("payment_amount");
                if (amount != null) {
                    paidAmount = paidAmount.add(amount);
                }
            }

            // 计算欠费金额
            BigDecimal owedAmount = detail.getAmount().subtract(paidAmount);
            if (owedAmount.compareTo(BigDecimal.ZERO) < 0) {
                owedAmount = BigDecimal.ZERO; // 不能为负数
            }

            // 创建包含所有信息的Map
            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("id", detail.getId());
            detailMap.put("billId", detail.getBillId());
            detailMap.put("feeType", detail.getFeeType());
            detailMap.put("feeName", detail.getFeeName());
            detailMap.put("baseAmount", detail.getBaseAmount());
            detailMap.put("unitPrice", detail.getUnitPrice());
            detailMap.put("billingDays", detail.getBillingDays());
            detailMap.put("amount", detail.getAmount());
            detailMap.put("paidAmount", paidAmount);
            detailMap.put("owedAmount", owedAmount);
            detailMap.put("plateNumber", detail.getPlateNumber());
            detailMap.put("spaceNumber", detail.getSpaceNumber());
            detailMap.put("calculationFormula", detail.getCalculationFormula());
            detailMap.put("billPeriodStart", detail.getBillPeriodStart());
            detailMap.put("billPeriodEnd", detail.getBillPeriodEnd());
            detailMap.put("remark", detail.getRemark());

            detailsWithPayment.add(detailMap);
        }

        result.put("details", detailsWithPayment);

        return result;
    }

    /**
     * 查询小区账单列表
     *
     * @param communityBill 小区账单
     * @return 小区账单
     */
    @Override
    public List<CommunityBill> selectCommunityBillList(CommunityBill communityBill) {
        return communityBillMapper.selectCommunityBillList(communityBill);
    }

    /**
     * 新增小区账单
     *
     * @param communityBill 小区账单
     * @return 结果
     */
    @Override
    public int insertCommunityBill(CommunityBill communityBill) {
        communityBill.setCreateTime(DateUtils.getNowDate());
        return communityBillMapper.insertCommunityBill(communityBill);
    }

    /**
     * 修改小区账单
     *
     * @param communityBill 小区账单
     * @return 结果
     */
    @Override
    public int updateCommunityBill(CommunityBill communityBill) {
        communityBill.setUpdateTime(DateUtils.getNowDate());
        return communityBillMapper.updateCommunityBill(communityBill);
    }

    /**
     * 批量删除小区账单
     *
     * @param ids 需要删除的小区账单主键
     * @return 结果
     */
    @Override
    public int deleteCommunityBillByIds(Long[] ids) {
        return communityBillMapper.deleteCommunityBillByIds(ids);
    }

    /**
     * 删除小区账单信息
     *
     * @param id 小区账单主键
     * @return 结果
     */
    @Override
    public int deleteCommunityBillById(Long id) {
        return communityBillMapper.deleteCommunityBillById(id);
    }

    /**
     * 自动生成账单（同步方法，返回任务ID）
     *
     * @param communityId  小区ID
     * @param billDate     账单日期
     * @param generateType 生成类型
     * @return 任务ID
     */
    @Override
    public String generateBills(Long communityId, String billDate, String generateType) {
        String taskId = generateTaskId(communityId, billDate);

        // 检查是否已有正在执行的任务
        BillGenerationProgress existingProgress = progressMap.get(taskId);
        if (existingProgress != null && "RUNNING".equals(existingProgress.getStatus())) {
            throw new RuntimeException("该小区正在生成账单中，请稍后再试");
        }

        // 创建进度记录
        BillGenerationProgress progress = new BillGenerationProgress();
        progress.setStatus("RUNNING");
        progress.setMessage("正在初始化...");
        progressMap.put(taskId, progress);

        // 异步执行生成任务
        generateBillsAsync(taskId, communityId, billDate, generateType);

        return taskId;
    }

    /**
     * 获取生成进度
     *
     * @param taskId 任务ID
     * @return 进度信息
     */
    @Override
    public BillGenerationProgress getGenerationProgress(String taskId) {
        return progressMap.get(taskId);
    }

    /**
     * 异步生成账单
     */
    @Async
    public void generateBillsAsync(String taskId, Long communityId, String billDate, String generateType) {
        BillGenerationProgress progress = progressMap.get(taskId);

        try {
            // 获取小区信息
            progress.setMessage("正在获取小区信息...");
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
            if (community == null) {
                throw new RuntimeException("小区不存在");
            }

            // 获取小区的所有业主
            progress.setMessage("正在获取业主列表...");
            OwnerMangement queryOwner = new OwnerMangement();
            queryOwner.setCommunityId(communityId);
            List<OwnerMangement> owners = ownerMangementMapper.selectOwnerMangementList(queryOwner);

            progress.setTotalCount(owners.size());
            progress.setMessage("开始生成账单...");

            int successCount = 0;

            for (int i = 0; i < owners.size(); i++) {
                OwnerMangement owner = owners.get(i);
                progress.setProcessedCount(i + 1);
                progress.setMessage("正在为业主 " + owner.getOwnerName() + " 生成账单...");

                try {
                    CommunityBill bill = generateBillForOwner(community, owner, billDate, generateType);
                    if (bill != null) {
                        successCount++;
                    }
                } catch (Exception e) {
                    // 记录单个业主生成失败，但继续处理其他业主
                    System.err.println("为业主 " + owner.getOwnerName() + " 生成账单失败: " + e.getMessage());
                }
            }

            // 更新小区的当前账单日期
            progress.setMessage("正在更新小区账单日期...");
            // 小区的账单日期应该设置为实际生成的账单结束日期
            // 例如：如果生成的账单是2025-06-13到2026-06-12，则设置为2026-06-12
            LocalDate inputDate = LocalDate.parse(billDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 直接使用传入的日期作为新的statementDate
            community.setStatementDate(java.sql.Date.valueOf(inputDate));
            communityMangementMapper.updateCommunityMangement(community);

            progress.setSuccessCount(successCount);
            progress.setStatus("COMPLETED");
            progress.setMessage("账单生成完成，共生成 " + successCount + " 条账单");
            progress.setEndTime(System.currentTimeMillis());

        } catch (Exception e) {
            progress.setStatus("FAILED");
            progress.setMessage("生成账单失败: " + e.getMessage());
            progress.setEndTime(System.currentTimeMillis());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(Long communityId, String billDate) {
        return "BILL_GEN_" + communityId + "_" + billDate.replace("-", "");
    }

    /**
     * 为单个业主生成账单
     *
     * @param community    小区信息
     * @param owner        业主信息
     * @param billDate     账单结束日期
     * @param generateType 生成类型
     * @return
     */
    private CommunityBill generateBillForOwner(CommunityMangement community, OwnerMangement owner, String billDate, String generateType) {
        try {
            // 前端传入的billDate就是账单结束日期，不需要减1天
            LocalDate inputDate = LocalDate.parse(billDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 检查是否已存在相同结束时间的账单
            if (hasExistingBillWithSameEndDate(owner.getId(), inputDate)) {
                System.out.println("业主 " + owner.getOwnerName() + " 已存在结束时间为 " + inputDate + " 的账单，跳过生成");
                return null;
            }

            // 确定账单开始日期：优先使用业主的费用到期时间，其次使用小区入园日期
            LocalDate billStartDate = determineBillStartDate(community, owner, generateType);

            if (billStartDate == null) {
                System.err.println("无法确定账单开始日期：小区ID=" + community.getId() + "，业主=" + owner.getOwnerName());
                return null;
            }

            // 查找并处理历史欠费账单
            List<String> mergedPeriods = new ArrayList<>();
            handleHistoricalUnpaidBills(owner.getId(), billStartDate, inputDate, mergedPeriods);

            // 调试信息
            System.out.println("业主：" + owner.getOwnerName() + "，开始日期：" + billStartDate + "，结束日期：" + inputDate);

            if (!billStartDate.isBefore(inputDate) && !billStartDate.isEqual(inputDate)) {
                System.err.println("日期范围无效：开始日期=" + billStartDate + "，结束日期=" + inputDate);
                return null; // 开始日期不能晚于结束日期
            }

            // 计算天数
            long days = ChronoUnit.DAYS.between(billStartDate, inputDate) + 1;

            CommunityBill bill = new CommunityBill();
            bill.setCommunityId(community.getId());
            bill.setOwnerId(owner.getId());
            bill.setBillNumber(generateBillNumber(community.getId(), owner.getId()));
            bill.setBillPeriodStart(java.sql.Date.valueOf(billStartDate));
            bill.setBillPeriodEnd(java.sql.Date.valueOf(inputDate));
            bill.setPaymentStatus(0); // 未支付
            bill.setCreateTime(DateUtils.getNowDate());

            // 设置备注，包含合并的历史周期信息
            if (!mergedPeriods.isEmpty()) {
                String remark = "包含历史欠费周期：" + String.join("、", mergedPeriods);
                bill.setRemark(remark);
            }

            // 判断是否为租客账单
            bill.setIsTenant("1".equals(String.valueOf(owner.getRentalStatus())) ? 1 : 0);

            BigDecimal totalAmount = BigDecimal.ZERO;

            // 计算物业费并生成明细
            if ("all".equals(generateType) || "property".equals(generateType)) {
                BigDecimal propertyFee = calculatePropertyFeeWithDetail(community, owner, billStartDate, inputDate, null, (int) days);
                bill.setPropertyFee(propertyFee);
                totalAmount = totalAmount.add(propertyFee);
                System.out.println("物业费：" + propertyFee);
            }

            // 计算停车费并生成明细
            if ("all".equals(generateType) || "parking".equals(generateType)) {
                BigDecimal parkingFee = calculateParkingFeeWithDetail(community, owner, billStartDate, inputDate, null, (int) days);
                bill.setParkingFee(parkingFee);
                totalAmount = totalAmount.add(parkingFee);
                System.out.println("停车费：" + parkingFee);
            }

            // 计算卫生费并生成明细
            if ("all".equals(generateType) || "sanitation".equals(generateType)) {
                BigDecimal sanitationFee = calculateSanitationFeeWithDetail(community, owner, billStartDate, inputDate, null, (int) days);
                bill.setSanitationFee(sanitationFee);
                totalAmount = totalAmount.add(sanitationFee);
                System.out.println("卫生费：" + sanitationFee);
            }

            bill.setTotalAmount(totalAmount);
            bill.setPaidAmount(BigDecimal.ZERO);

            System.out.println("总金额：" + totalAmount);

            // 如果总金额大于0，才插入账单和明细
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 插入账单获取ID
                communityBillMapper.insertCommunityBill(bill);
                Long billId = bill.getId();

                // 重新计算并插入明细（因为之前传入的billId是null）
                if ("all".equals(generateType) || "property".equals(generateType)) {
                    calculatePropertyFeeWithDetail(community, owner, billStartDate, inputDate, billId, (int) days);
                }

                if ("all".equals(generateType) || "parking".equals(generateType)) {
                    calculateParkingFeeWithDetail(community, owner, billStartDate, inputDate, billId, (int) days);
                }

                if ("all".equals(generateType) || "sanitation".equals(generateType)) {
                    calculateSanitationFeeWithDetail(community, owner, billStartDate, inputDate, billId, (int) days);
                }

                return bill;
            } else {
                System.out.println("业主 " + owner.getOwnerName() + " 无需生成账单，总金额为0");
                return null;
            }

        } catch (Exception e) {
            System.err.println("为业主 " + owner.getOwnerName() + " 生成账单异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 确定账单开始日期
     * 1. 如果业主的物业费、卫生费到期时间不为空，使用最早的到期时间+1天
     * 2. 如果都为空，使用小区入园日期
     */
    private LocalDate determineBillStartDate(CommunityMangement community, OwnerMangement owner, String generateType) {
        LocalDate startDate = null;

        // 根据生成类型确定使用哪个到期时间
        if ("all".equals(generateType) || "property".equals(generateType)) {
            if (owner.getCommunityPriceEnddate() != null) {
                LocalDate propertyEndDate = owner.getCommunityPriceEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                startDate = propertyEndDate.plusDays(1);
            }
        }

        if ("all".equals(generateType) || "sanitation".equals(generateType)) {
            if (owner.getPublicSanitationFeeEnddate() != null) {
                LocalDate sanitationEndDate = owner.getPublicSanitationFeeEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                LocalDate sanitationStartDate = sanitationEndDate.plusDays(1);

                // 如果卫生费开始日期更早，使用卫生费开始日期
                if (startDate == null || sanitationStartDate.isBefore(startDate)) {
                    startDate = sanitationStartDate;
                }
            }
        }

        // 如果没有费用到期时间，使用小区入园日期
        if (startDate == null) {
            if (community.getEntryDate() != null) {
                startDate = community.getEntryDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            } else if (community.getStatementDate() != null) {
                startDate = community.getStatementDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate().plusDays(1);
            }
        }

        return startDate;
    }

    /**
     * 检查是否已存在相同结束时间的账单
     */
    private boolean hasExistingBillWithSameEndDate(Long ownerId, LocalDate billEndDate) {
        try {
            CommunityBill queryBill = new CommunityBill();
            queryBill.setOwnerId(ownerId);
            List<CommunityBill> existingBills = communityBillMapper.selectCommunityBillList(queryBill);

            for (CommunityBill bill : existingBills) {
                if (bill.getBillPeriodEnd() != null) {
                    LocalDate existingEndDate = bill.getBillPeriodEnd().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                    if (existingEndDate.equals(billEndDate)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查已存在账单失败: " + e.getMessage());
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 处理历史未交账单
     * 将历史未交或未交全的账单状态设为合并状态(3)，并记录合并的周期信息
     * 检查时间周期重叠，包括：历史账单和与新账单时间重叠的账单
     */
    private void handleHistoricalUnpaidBills(Long ownerId, LocalDate newBillStartDate, LocalDate newBillEndDate, List<String> mergedPeriods) {
        try {
            // 查询该业主的所有未完全支付的账单（支付状态为0或1，排除已合并的账单）
            CommunityBill queryBill = new CommunityBill();
            queryBill.setOwnerId(ownerId);
            List<CommunityBill> allBills = communityBillMapper.selectCommunityBillList(queryBill);

            for (CommunityBill bill : allBills) {
                // 只处理未支付(0)或部分支付(1)的账单，排除已合并(3)和已支付(2)的账单
                if (bill.getPaymentStatus() != null && (bill.getPaymentStatus() == 0 || bill.getPaymentStatus() == 1)) {
                    if (bill.getBillPeriodStart() != null && bill.getBillPeriodEnd() != null) {
                        LocalDate billStartDate = bill.getBillPeriodStart().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        LocalDate billEndDate = bill.getBillPeriodEnd().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

                        boolean shouldMerge = false;
                        String mergeReason = "";

                        // 情况1：历史账单（账单结束日期在新账单开始日期之前）
                        if (billEndDate.isBefore(newBillStartDate)) {
                            shouldMerge = true;
                            mergeReason = "历史欠费";
                        }
                        // 情况2：时间周期重叠
                        else if (isDateRangeOverlap(billStartDate, billEndDate, newBillStartDate, newBillEndDate)) {
                            shouldMerge = true;
                            mergeReason = "时间周期重叠";
                        }

                        if (shouldMerge) {
                            // 设置为合并状态
                            bill.setPaymentStatus(3); // 3表示合并到新账单
                            bill.setUpdateTime(DateUtils.getNowDate());

                            // 在备注中记录合并原因和新账单信息
                            String originalRemark = bill.getRemark() != null ? bill.getRemark() : "";
                            String mergeInfo = String.format("【%s】已合并到新账单(%s至%s)", mergeReason, formatDate(java.sql.Date.valueOf(newBillStartDate)), formatDate(java.sql.Date.valueOf(newBillEndDate)));

                            if (!originalRemark.isEmpty()) {
                                bill.setRemark(originalRemark + " " + mergeInfo);
                            } else {
                                bill.setRemark(mergeInfo);
                            }

                            communityBillMapper.updateCommunityBill(bill);

                            // 记录合并的周期信息
                            String period = formatDate(bill.getBillPeriodStart()) + "至" + formatDate(bill.getBillPeriodEnd());
                            mergedPeriods.add(period);

                            System.out.println("账单ID=" + bill.getId() + " 设置为合并状态，原因：" + mergeReason + "，周期：" + period);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("处理历史未交账单异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查两个日期范围是否重叠
     */
    private boolean isDateRangeOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        // 两个日期范围重叠的条件：start1 <= end2 && start2 <= end1
        return !start1.isAfter(end2) && !start2.isAfter(end1);
    }

    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        if (date == null) return "";
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }

    /**
     * 生成账单编号
     */
    private String generateBillNumber(Long communityId, Long ownerId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        // 格式化小区ID为3位，不足前面补0
        String communityIdStr = String.format("%03d", communityId);

        // 格式化业主ID为5位，不足前面补0
        String ownerIdStr = String.format("%05d", ownerId);

        // 生成顺序号（5位），这里使用时间戳的后5位作为顺序号
        long timestamp = System.currentTimeMillis();
        String sequenceStr = String.format("%05d", timestamp % 100000);

        return COMMUNITY_BILL_PREFIX + timestamp;
    }

    /**
     * 计算物业费并生成明细
     *
     * @param community 小区信息
     * @param owner     业主信息
     * @param startDate 账单开始日期
     * @param endDate   账单结束日期
     * @param billId    账单ID
     * @param days      账单天数
     * @return
     */
    private BigDecimal calculatePropertyFeeWithDetail(CommunityMangement community, OwnerMangement owner, LocalDate startDate, LocalDate endDate, Long billId, int days) {
        // 如果业主的物业费到期时间不为空，且到期时间在账单结束日期之后，说明该期间已预交
        if (owner.getCommunityPriceEnddate() != null) {
            LocalDate paidEndDate = owner.getCommunityPriceEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

            // 如果预交结束时间晚于等于账单结束时间，说明整个账单期间都已预交
            if (!paidEndDate.isBefore(endDate)) {
                return BigDecimal.ZERO; // 已预交，不需要生成物业费
            }

            // 如果预交结束时间在账单期间内，调整开始时间为预交结束时间+1天
            if (paidEndDate.isAfter(startDate) || paidEndDate.isEqual(startDate)) {
                startDate = paidEndDate.plusDays(1);

                // 重新计算天数
                if (!startDate.isBefore(endDate) && !startDate.isEqual(endDate)) {
                    return BigDecimal.ZERO; // 调整后没有需要计费的天数
                }
                days = (int) (ChronoUnit.DAYS.between(startDate, endDate) + 1);
            }
        }

        if (community.getCommunityPrice() == null || owner.getHouseArea() == null) {
            return BigDecimal.ZERO;
        }

        // 使用统一的费用计算服务
        FeeCalculationResult feeResult = feeCalculationService.calculatePropertyFee(startDate, endDate, community.getCommunityPrice(), owner.getHouseArea());

        BigDecimal finalFee = feeResult.getTotalFee();

        // 只有当billId不为null时才生成明细
        if (billId != null && finalFee.compareTo(BigDecimal.ZERO) > 0) {
            CommunityBillDetail detail = new CommunityBillDetail();
            detail.setBillId(billId);
            detail.setFeeType(1L); // 物业费
            detail.setFeeName("物业费");
            detail.setBaseAmount(owner.getHouseArea());
            detail.setUnitPrice(community.getCommunityPrice());
            detail.setBillingDays((long) days);
            detail.setAmount(finalFee);
            detail.setCalculationFormula(feeResult.getDescription());
            detail.setBillPeriodStart(java.sql.Date.valueOf(startDate));
            detail.setBillPeriodEnd(java.sql.Date.valueOf(endDate));
            detail.setCreateTime(DateUtils.getNowDate());

            communityBillDetailMapper.insertCommunityBillDetail(detail);
        }

        return finalFee;
    }

    /**
     * 计算停车费并生成明细
     *
     * @param community 小区信息
     * @param owner     业主信息
     * @param startDate 账单开始日期
     * @param endDate   账单结束日期
     * @param billId    账单ID
     * @param days      账单天数
     * @return
     */
    private BigDecimal calculateParkingFeeWithDetail(CommunityMangement community, OwnerMangement owner, LocalDate startDate, LocalDate endDate, Long billId, int days) {
        // 查询业主的停车位信息
        ParkingInfo queryParking = new ParkingInfo();
        queryParking.setOwnerId(owner.getId());
        queryParking.setStatus(1); // 有效状态
        List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);

        BigDecimal totalParkingFee = BigDecimal.ZERO;

        for (ParkingInfo parking : parkingList) {
            // 检查停车记录的时间范围是否与账单周期有重叠
            LocalDate parkingStartDate = null;
            LocalDate parkingEndDate = null;

            if (parking.getStartDate() != null) {
                parkingStartDate = parking.getStartDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            }
            if (parking.getEndDate() != null) {
                parkingEndDate = parking.getEndDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            }

            // 计算停车记录与账单周期的重叠时间段
            LocalDate effectiveStartDate = startDate;
            LocalDate effectiveEndDate = endDate;

            // 如果停车记录有开始时间，取较晚的开始时间
            if (parkingStartDate != null && parkingStartDate.isAfter(startDate)) {
                effectiveStartDate = parkingStartDate;
            }

            // 停车记录结束时间不影响计费结束时间，始终计费到账单结束时间
            // 这样可以计算停车到期后的欠费
            // effectiveEndDate 保持为账单结束时间

            // 如果有效开始时间晚于有效结束时间，说明没有重叠，跳过此停车记录
            if (effectiveStartDate.isAfter(effectiveEndDate)) {
                System.out.println("停车记录时间范围与账单周期无重叠，跳过：车牌" + parking.getPlateNumber() +
                    "，停车时间：" + parkingStartDate + " 至 " + parkingEndDate +
                    "，账单周期：" + startDate + " 至 " + endDate);
                continue;
            }

            // 根据停车位的三证合一状态从小区配置中获取停车费
            BigDecimal monthlyParkingFee = null;

            if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                // 三证合一停车费（使用原业主停车费字段）
                monthlyParkingFee = community.getOwnerParkingFee();
            } else {
                // 非三证合一停车费（使用原租客停车费字段）
                monthlyParkingFee = community.getTenantParkingFee();
            }

            if (monthlyParkingFee != null && monthlyParkingFee.compareTo(BigDecimal.ZERO) > 0) {
                // 使用有效的时间段计算停车费
                FeeCalculationResult feeResult = feeCalculationService.calculateParkingFee(effectiveStartDate, effectiveEndDate, monthlyParkingFee);

                BigDecimal finalFee = feeResult.getTotalFee();
                totalParkingFee = totalParkingFee.add(finalFee);

                // 计算实际计费天数
                long actualBillingDays = java.time.temporal.ChronoUnit.DAYS.between(effectiveStartDate, effectiveEndDate) + 1;

                // 判断是否存在超期使用情况
                boolean isOverdue = parkingEndDate != null && parkingEndDate.isBefore(endDate);
                String overdueInfo = isOverdue ? "（包含超期使用：" + parkingEndDate.plusDays(1) + " 至 " + endDate + "）" : "";

                System.out.println("停车费计算：车牌" + parking.getPlateNumber() +
                    "，停车时间：" + parkingStartDate + " 至 " + parkingEndDate +
                    "，账单周期：" + startDate + " 至 " + endDate +
                    "，有效计费时间：" + effectiveStartDate + " 至 " + effectiveEndDate + overdueInfo +
                    "，计费天数：" + actualBillingDays + "天，费用：" + finalFee + "元");

                // 只有当billId不为null时才生成明细
                if (billId != null && finalFee.compareTo(BigDecimal.ZERO) > 0) {
                    CommunityBillDetail detail = new CommunityBillDetail();
                    detail.setBillId(billId);
                    detail.setFeeType(2L); // 停车费
                    detail.setFeeName("停车费");
                    detail.setBaseAmount(new BigDecimal("1")); // 车位数量
                    detail.setUnitPrice(monthlyParkingFee);
                    detail.setBillingDays(actualBillingDays);
                    detail.setAmount(finalFee);
                    detail.setPlateNumber(parking.getPlateNumber());
                    detail.setSpaceNumber(parking.getSpaceNumber());

                    // 生成详细的停车费计算说明
                    // 使用之前已经计算的 isOverdue 变量
                    String overdueDescription = "";
                    if (isOverdue) {
                        long overdueDays = java.time.temporal.ChronoUnit.DAYS.between(parkingEndDate, endDate);
                        overdueDescription = String.format("，其中超期使用%d天（%s 至 %s）",
                            overdueDays, parkingEndDate.plusDays(1).toString(), endDate.toString());
                    }

                    String parkingDescription = String.format("停车费计算：车牌%s，%s停车费，停车时间：%s 至 %s，账单周期：%s 至 %s，有效计费时间：%s 至 %s，计费天数：%d天%s，%s",
                        parking.getPlateNumber() != null ? parking.getPlateNumber() : "未知",
                        parking.getIsThreeCertificates() == 1 ? "三证合一" : "非三证合一",
                        parkingStartDate != null ? parkingStartDate.toString() : "无限制",
                        parkingEndDate != null ? parkingEndDate.toString() : "无限制",
                        startDate.toString(),
                        endDate.toString(),
                        effectiveStartDate.toString(),
                        effectiveEndDate.toString(),
                        actualBillingDays,
                        overdueDescription,
                        feeResult.getDescription().replace("停车费计算：", ""));

                    detail.setCalculationFormula(parkingDescription);
                    detail.setBillPeriodStart(java.sql.Date.valueOf(effectiveStartDate));
                    detail.setBillPeriodEnd(java.sql.Date.valueOf(effectiveEndDate));
                    detail.setCreateTime(DateUtils.getNowDate());

                    communityBillDetailMapper.insertCommunityBillDetail(detail);
                }
            }
        }

        return totalParkingFee;
    }

    /**
     * 计算卫生费并生成明细
     *
     * @param community 小区信息
     * @param owner     业主信息
     * @param startDate 账单开始日期
     * @param endDate   账单结束日期
     * @param billId    账单ID
     * @param days      账单天数
     * @return
     */
    private BigDecimal calculateSanitationFeeWithDetail(CommunityMangement community, OwnerMangement owner, LocalDate startDate, LocalDate endDate, Long billId, int days) {
        // 如果业主的卫生费到期时间不为空，且到期时间在账单结束日期之后，说明该期间已预交
        if (owner.getPublicSanitationFeeEnddate() != null) {
            LocalDate paidEndDate = owner.getPublicSanitationFeeEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

            // 如果预交结束时间晚于等于账单结束时间，说明整个账单期间都已预交
            if (!paidEndDate.isBefore(endDate)) {
                return BigDecimal.ZERO; // 已预交，不需要生成卫生费
            }

            // 如果预交结束时间在账单期间内，调整开始时间为预交结束时间+1天
            if (paidEndDate.isAfter(startDate) || paidEndDate.isEqual(startDate)) {
                startDate = paidEndDate.plusDays(1);

                // 重新计算天数
                if (!startDate.isBefore(endDate) && !startDate.isEqual(endDate)) {
                    return BigDecimal.ZERO; // 调整后没有需要计费的天数
                }
                days = (int) (ChronoUnit.DAYS.between(startDate, endDate) + 1);
            }
        }

        // 根据房屋面积确定卫生费标准
        BigDecimal sanitationFeeRate = SanitationFeeCalculator.calculateSanitationFee(community, owner.getHouseArea());
        if (sanitationFeeRate == null) {
            log.error("卫生费计算结果为null，跳过卫生费生成");
            return BigDecimal.ZERO;
        }

        // 使用统一的费用计算服务
        FeeCalculationResult feeResult = feeCalculationService.calculateSanitationFee(startDate, endDate, sanitationFeeRate);

        BigDecimal finalFee = feeResult.getTotalFee();

        // 只有当billId不为null时才生成明细
        if (billId != null && finalFee.compareTo(BigDecimal.ZERO) > 0) {
            CommunityBillDetail detail = new CommunityBillDetail();
            detail.setBillId(billId);
            detail.setFeeType(3L); // 卫生费
            detail.setFeeName("卫生费");

            // 如果是固定费用 计费基数为 1
            if (community.getSanitationFeeType() != null && community.getSanitationFeeType() == 1) {
                detail.setBaseAmount(BigDecimal.ONE);
            } else {
                detail.setBaseAmount(owner.getHouseArea());
            }
            detail.setUnitPrice(sanitationFeeRate);
            detail.setBillingDays((long) days);
            detail.setAmount(finalFee);
            detail.setCalculationFormula(feeResult.getDescription());
            detail.setBillPeriodStart(java.sql.Date.valueOf(startDate));
            detail.setBillPeriodEnd(java.sql.Date.valueOf(endDate));
            detail.setCreateTime(DateUtils.getNowDate());

            communityBillDetailMapper.insertCommunityBillDetail(detail);
        }

        return finalFee;
    }

    /**
     * 根据小区ID获取未缴费账单列表（用于批量打印催缴单）
     *
     * @param communityId 小区ID
     * @return 未缴费账单列表
     */
    @Override
    public List<Map<String, Object>> selectUnpaidBillsByCommunity(Long communityId) {
        // 查询该小区的所有未缴费账单（支付状态为0未支付或1部分支付）
        CommunityBill queryBill = new CommunityBill();
        queryBill.setCommunityId(communityId);

        List<CommunityBill> allBills = communityBillMapper.selectCommunityBillList(queryBill);

        // 过滤出未缴费的账单，并获取详细信息
        List<Map<String, Object>> unpaidBills = new ArrayList<>();

        for (CommunityBill bill : allBills) {
            // 只处理未支付(0)和部分支付(1)的账单，排除合并状态(3)的账单
            if (bill.getPaymentStatus() != null && (bill.getPaymentStatus() == 0 || bill.getPaymentStatus() == 1)) {
                // 获取账单详细信息
                Map<String, Object> billWithDetails = selectCommunityBillWithDetails(bill.getId());
                unpaidBills.add(billWithDetails);
            }
        }

        return unpaidBills;
    }
}

