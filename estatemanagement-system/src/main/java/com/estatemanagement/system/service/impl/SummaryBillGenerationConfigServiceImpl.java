package com.estatemanagement.system.service.impl;

import java.util.Date;
import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.common.utils.spring.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.SummaryBillGenerationConfigMapper;
import com.estatemanagement.system.domain.SummaryBillGenerationConfig;
import com.estatemanagement.system.service.ISummaryBillGenerationConfigService;
import com.estatemanagement.system.service.ICommunitySummaryBillService;

/**
 * 汇总账单生成配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class SummaryBillGenerationConfigServiceImpl implements ISummaryBillGenerationConfigService 
{
    @Autowired
    private SummaryBillGenerationConfigMapper summaryBillGenerationConfigMapper;

    @Autowired
    private com.estatemanagement.system.mapper.CommunityMangementMapper communityMangementMapper;

    /**
     * 查询汇总账单生成配置
     * 
     * @param id 汇总账单生成配置主键
     * @return 汇总账单生成配置
     */
    @Override
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigById(Long id)
    {
        return summaryBillGenerationConfigMapper.selectSummaryBillGenerationConfigById(id);
    }

    /**
     * 根据小区ID查询汇总账单生成配置
     * 
     * @param communityId 小区ID
     * @return 汇总账单生成配置
     */
    @Override
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigByCommunityId(Long communityId)
    {
        return summaryBillGenerationConfigMapper.selectSummaryBillGenerationConfigByCommunityId(communityId);
    }

    /**
     * 查询汇总账单生成配置列表
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 汇总账单生成配置
     */
    @Override
    public List<SummaryBillGenerationConfig> selectSummaryBillGenerationConfigList(SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        return summaryBillGenerationConfigMapper.selectSummaryBillGenerationConfigList(summaryBillGenerationConfig);
    }

    /**
     * 新增汇总账单生成配置
     *
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    @Override
    public int insertSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        // 设置小区名称
        if (summaryBillGenerationConfig.getCommunityId() != null) {
            com.estatemanagement.system.domain.CommunityMangement community = communityMangementMapper.selectCommunityMangementById(summaryBillGenerationConfig.getCommunityId());
            if (community != null) {
                summaryBillGenerationConfig.setCommunityName(community.getCommunityName());
            }
        } else {
            summaryBillGenerationConfig.setCommunityName("全部小区");
        }

        summaryBillGenerationConfig.setCreateTime(DateUtils.getNowDate());
        return summaryBillGenerationConfigMapper.insertSummaryBillGenerationConfig(summaryBillGenerationConfig);
    }

    /**
     * 修改汇总账单生成配置
     *
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    @Override
    public int updateSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        // 设置小区名称
        if (summaryBillGenerationConfig.getCommunityId() != null) {
            com.estatemanagement.system.domain.CommunityMangement community = communityMangementMapper.selectCommunityMangementById(summaryBillGenerationConfig.getCommunityId());
            if (community != null) {
                summaryBillGenerationConfig.setCommunityName(community.getCommunityName());
            }
        } else {
            summaryBillGenerationConfig.setCommunityName("全部小区");
        }

        summaryBillGenerationConfig.setUpdateTime(DateUtils.getNowDate());
        return summaryBillGenerationConfigMapper.updateSummaryBillGenerationConfig(summaryBillGenerationConfig);
    }

    /**
     * 批量删除汇总账单生成配置
     * 
     * @param ids 需要删除的汇总账单生成配置主键
     * @return 结果
     */
    @Override
    public int deleteSummaryBillGenerationConfigByIds(Long[] ids)
    {
        return summaryBillGenerationConfigMapper.deleteSummaryBillGenerationConfigByIds(ids);
    }

    /**
     * 删除汇总账单生成配置信息
     * 
     * @param id 汇总账单生成配置主键
     * @return 结果
     */
    @Override
    public int deleteSummaryBillGenerationConfigById(Long id)
    {
        return summaryBillGenerationConfigMapper.deleteSummaryBillGenerationConfigById(id);
    }

    /**
     * 启用或停用汇总账单自动生成
     * 
     * @param communityId 小区ID
     * @param status 状态(0停用,1启用)
     * @return 结果
     */
    @Override
    public int toggleAutoGeneration(Long communityId, Integer status)
    {
        SummaryBillGenerationConfig config = summaryBillGenerationConfigMapper.selectSummaryBillGenerationConfigByCommunityId(communityId);
        if (config == null) {
            throw new RuntimeException("该小区尚未配置汇总账单生成设置");
        }
        
        config.setStatus(status);
        config.setUpdateTime(DateUtils.getNowDate());
        
        return summaryBillGenerationConfigMapper.updateSummaryBillGenerationConfig(config);
    }

    /**
     * 手动触发汇总账单生成
     *
     * @param communityId 小区ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务ID
     */
    @Override
    public String manualGenerateSummaryBills(Long communityId, Date startDate, Date endDate)
    {
        try {
            // 直接调用汇总账单生成服务
            ICommunitySummaryBillService summaryBillService = SpringUtils.getBean(ICommunitySummaryBillService.class);

            return summaryBillService.generateSummaryBills(communityId, startDate, endDate);
        } catch (Exception e) {
            throw new RuntimeException("手动生成汇总账单失败: " + e.getMessage());
        }
    }
}
