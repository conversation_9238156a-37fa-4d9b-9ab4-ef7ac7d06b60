package com.estatemanagement.system.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.common.utils.spring.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.BillGenerationConfigMapper;
import com.estatemanagement.system.domain.BillGenerationConfig;
import com.estatemanagement.system.service.IBillGenerationConfigService;
import com.estatemanagement.system.service.ICommunityBillService;

/**
 * 账单生成配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class BillGenerationConfigServiceImpl implements IBillGenerationConfigService 
{
    @Autowired
    private BillGenerationConfigMapper billGenerationConfigMapper;

    /**
     * 查询账单生成配置
     * 
     * @param id 账单生成配置主键
     * @return 账单生成配置
     */
    @Override
    public BillGenerationConfig selectBillGenerationConfigById(Long id)
    {
        return billGenerationConfigMapper.selectBillGenerationConfigById(id);
    }

    /**
     * 查询账单生成配置列表
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 账单生成配置
     */
    @Override
    public List<BillGenerationConfig> selectBillGenerationConfigList(BillGenerationConfig billGenerationConfig)
    {
        return billGenerationConfigMapper.selectBillGenerationConfigList(billGenerationConfig);
    }

    /**
     * 根据小区ID查询账单生成配置
     * 
     * @param communityId 小区ID
     * @return 账单生成配置
     */
    @Override
    public BillGenerationConfig selectBillGenerationConfigByCommunityId(Long communityId)
    {
        return billGenerationConfigMapper.selectBillGenerationConfigByCommunityId(communityId);
    }

    /**
     * 新增账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    @Override
    public int insertBillGenerationConfig(BillGenerationConfig billGenerationConfig)
    {
        // 设置下次生成时间
        if (billGenerationConfig.getAutoGenerate() == 1 && billGenerationConfig.getIntervalDays() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, billGenerationConfig.getIntervalDays());
            billGenerationConfig.setNextGenerateTime(calendar.getTime());
        }
        
        billGenerationConfig.setCreateTime(DateUtils.getNowDate());
        return billGenerationConfigMapper.insertBillGenerationConfig(billGenerationConfig);
    }

    /**
     * 修改账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    @Override
    public int updateBillGenerationConfig(BillGenerationConfig billGenerationConfig)
    {
        // 如果启用自动生成且间隔天数有变化，重新计算下次生成时间
        if (billGenerationConfig.getAutoGenerate() == 1 && billGenerationConfig.getIntervalDays() != null) {
            Date baseTime = billGenerationConfig.getLastGenerateTime() != null ? 
                billGenerationConfig.getLastGenerateTime() : new Date();
            
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(baseTime);
            calendar.add(Calendar.DAY_OF_MONTH, billGenerationConfig.getIntervalDays());
            billGenerationConfig.setNextGenerateTime(calendar.getTime());
        }
        
        billGenerationConfig.setUpdateTime(DateUtils.getNowDate());
        return billGenerationConfigMapper.updateBillGenerationConfig(billGenerationConfig);
    }

    /**
     * 批量删除账单生成配置
     * 
     * @param ids 需要删除的账单生成配置主键
     * @return 结果
     */
    @Override
    public int deleteBillGenerationConfigByIds(Long[] ids)
    {
        return billGenerationConfigMapper.deleteBillGenerationConfigByIds(ids);
    }

    /**
     * 删除账单生成配置信息
     * 
     * @param id 账单生成配置主键
     * @return 结果
     */
    @Override
    public int deleteBillGenerationConfigById(Long id)
    {
        return billGenerationConfigMapper.deleteBillGenerationConfigById(id);
    }

    /**
     * 启用或停用账单自动生成
     * 
     * @param communityId 小区ID
     * @param status 状态(0停用,1启用)
     * @return 结果
     */
    @Override
    public int toggleAutoGeneration(Long communityId, Integer status)
    {
        BillGenerationConfig config = billGenerationConfigMapper.selectBillGenerationConfigByCommunityId(communityId);
        if (config == null) {
            throw new RuntimeException("该小区尚未配置账单生成设置");
        }
        
        config.setStatus(status);
        config.setUpdateTime(DateUtils.getNowDate());
        
        return billGenerationConfigMapper.updateBillGenerationConfig(config);
    }

    /**
     * 手动触发账单生成
     *
     * @param communityId 小区ID
     * @param generateType 生成类型
     * @return 任务ID
     */
    @Override
    public String manualGenerateBills(Long communityId, String generateType)
    {
        try {
            // 直接调用账单生成服务，而不是通过定时任务
            ICommunityBillService billService = SpringUtils.getBean(ICommunityBillService.class);

            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
            String billDate = sdf.format(new Date());

            return billService.generateBills(communityId, billDate, generateType);
        } catch (Exception e) {
            throw new RuntimeException("手动生成账单失败: " + e.getMessage());
        }
    }
}
