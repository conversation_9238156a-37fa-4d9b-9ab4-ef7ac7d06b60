package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.CommunityMangementMapper;
import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.service.ICommunityMangementService;

/**
 * 小区管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Service
public class CommunityMangementServiceImpl implements ICommunityMangementService 
{
    @Autowired
    private CommunityMangementMapper communityMangementMapper;

    /**
     * 查询小区管理
     * 
     * @param id 小区管理主键
     * @return 小区管理
     */
    @Override
    public CommunityMangement selectCommunityMangementById(Long id)
    {
        return communityMangementMapper.selectCommunityMangementById(id);
    }

    /**
     * 查询小区管理列表
     * 
     * @param communityMangement 小区管理
     * @return 小区管理
     */
    @Override
    public List<CommunityMangement> selectCommunityMangementList(CommunityMangement communityMangement)
    {
        return communityMangementMapper.selectCommunityMangementList(communityMangement);
    }

    /**
     * 新增小区管理
     * 
     * @param communityMangement 小区管理
     * @return 结果
     */
    @Override
    public int insertCommunityMangement(CommunityMangement communityMangement)
    {
        communityMangement.setCreateTime(DateUtils.getNowDate());
        return communityMangementMapper.insertCommunityMangement(communityMangement);
    }

    /**
     * 修改小区管理
     * 
     * @param communityMangement 小区管理
     * @return 结果
     */
    @Override
    public int updateCommunityMangement(CommunityMangement communityMangement)
    {
        communityMangement.setUpdateTime(DateUtils.getNowDate());
        return communityMangementMapper.updateCommunityMangement(communityMangement);
    }

    /**
     * 批量删除小区管理
     * 
     * @param ids 需要删除的小区管理主键
     * @return 结果
     */
    @Override
    public int deleteCommunityMangementByIds(Long[] ids)
    {
        return communityMangementMapper.deleteCommunityMangementByIds(ids);
    }

    /**
     * 删除小区管理信息
     * 
     * @param id 小区管理主键
     * @return 结果
     */
    @Override
    public int deleteCommunityMangementById(Long id)
    {
        return communityMangementMapper.deleteCommunityMangementById(id);
    }
}
