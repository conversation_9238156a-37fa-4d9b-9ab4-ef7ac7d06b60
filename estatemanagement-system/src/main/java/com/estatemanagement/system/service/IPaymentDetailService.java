package com.estatemanagement.system.service;

import java.util.List;
import com.estatemanagement.system.domain.PaymentDetail;

/**
 * 收费明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IPaymentDetailService 
{
    /**
     * 查询收费明细
     * 
     * @param id 收费明细主键
     * @return 收费明细
     */
    public PaymentDetail selectPaymentDetailById(Long id);

    /**
     * 查询收费明细列表
     * 
     * @param paymentDetail 收费明细
     * @return 收费明细集合
     */
    public List<PaymentDetail> selectPaymentDetailList(PaymentDetail paymentDetail);

    /**
     * 新增收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    public int insertPaymentDetail(PaymentDetail paymentDetail);

    /**
     * 修改收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    public int updatePaymentDetail(PaymentDetail paymentDetail);

    /**
     * 批量删除收费明细
     * 
     * @param ids 需要删除的收费明细主键集合
     * @return 结果
     */
    public int deletePaymentDetailByIds(Long[] ids);

    /**
     * 删除收费明细信息
     * 
     * @param id 收费明细主键
     * @return 结果
     */
    public int deletePaymentDetailById(Long id);
}
