package com.estatemanagement.system.service;

import java.util.List;
import java.util.Map;
import com.estatemanagement.system.domain.RefundRecord;

/**
 * 退费记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IRefundRecordService 
{
    /**
     * 查询退费记录
     * 
     * @param id 退费记录主键
     * @return 退费记录
     */
    public RefundRecord selectRefundRecordById(Long id);

    /**
     * 查询退费记录列表
     * 
     * @param refundRecord 退费记录
     * @return 退费记录集合
     */
    public List<RefundRecord> selectRefundRecordList(RefundRecord refundRecord);

    /**
     * 查询退费记录列表（包含关联信息）
     * 
     * @param refundRecord 退费记录
     * @return 退费记录集合
     */
    public List<Map<String, Object>> selectRefundRecordListWithDetails(RefundRecord refundRecord);

    /**
     * 根据原收费记录ID查询退费记录
     * 
     * @param originalPaymentId 原收费记录ID
     * @return 退费记录集合
     */
    public List<RefundRecord> selectRefundRecordsByOriginalPaymentId(Long originalPaymentId);

    /**
     * 统计退费金额
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    public Map<String, Object> selectRefundStatistics(Map<String, Object> params);

    /**
     * 新增退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    public int insertRefundRecord(RefundRecord refundRecord);

    /**
     * 修改退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    public int updateRefundRecord(RefundRecord refundRecord);

    /**
     * 批量删除退费记录
     * 
     * @param ids 需要删除的退费记录主键集合
     * @return 结果
     */
    public int deleteRefundRecordByIds(Long[] ids);

    /**
     * 删除退费记录信息
     * 
     * @param id 退费记录主键
     * @return 结果
     */
    public int deleteRefundRecordById(Long id);

    /**
     * 审核退费记录
     * 
     * @param id 退费记录ID
     * @param status 审核状态
     * @param auditUserId 审核人ID
     * @param auditUserName 审核人姓名
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditRefundRecord(Long id, Integer status, Long auditUserId, String auditUserName, String auditRemark);

    /**
     * 处理退费申请
     * 
     * @param refundData 退费数据
     * @return 退费记录ID
     */
    public Long processRefund(Map<String, Object> refundData);

    /**
     * 计算可退费金额
     * 
     * @param paymentId 收费记录ID
     * @param refundType 退费类型
     * @param refundItems 退费项目
     * @return 可退费金额信息
     */
    public Map<String, Object> calculateRefundAmount(Long paymentId, Integer refundType, List<Map<String, Object>> refundItems);

    /**
     * 获取收费记录的退费信息
     * 
     * @param paymentId 收费记录ID
     * @return 退费信息
     */
    public Map<String, Object> getPaymentRefundInfo(Long paymentId);
}
