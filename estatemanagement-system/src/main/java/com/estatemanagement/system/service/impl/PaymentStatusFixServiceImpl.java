package com.estatemanagement.system.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.estatemanagement.system.mapper.CommunityBillMapper;
import com.estatemanagement.system.mapper.PaymentDetailMapper;
import com.estatemanagement.system.domain.CommunityBill;

/**
 * 支付状态修复服务
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
public class PaymentStatusFixServiceImpl 
{
    @Autowired
    private CommunityBillMapper communityBillMapper;
    
    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    /**
     * 修复所有账单的支付状态
     * 
     * @return 修复的账单数量
     */
    @Transactional
    public int fixAllBillPaymentStatus() {
        int fixedCount = 0;
        
        // 查询所有账单（使用原始查询，不使用实时计算的查询）
        List<CommunityBill> allBills = communityBillMapper.selectCommunityBillListForFix();
        
        for (CommunityBill bill : allBills) {
            boolean needUpdate = false;
            
            // 重新计算各项费用的支付状态
            needUpdate |= updateFeePaymentStatus(bill, 1); // 物业费
            needUpdate |= updateFeePaymentStatus(bill, 2); // 停车费
            needUpdate |= updateFeePaymentStatus(bill, 3); // 卫生费
            needUpdate |= updateFeePaymentStatus(bill, 4); // 电梯费
            needUpdate |= updateFeePaymentStatus(bill, 5); // 滞纳金
            
            // 重新计算总的支付状态
            BigDecimal totalPaidAmount = BigDecimal.ZERO;
            if (bill.getPropertyPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getPropertyPaidAmount());
            if (bill.getParkingPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getParkingPaidAmount());
            if (bill.getSanitationPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getSanitationPaidAmount());
            if (bill.getElevatorPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getElevatorPaidAmount());


            // 更新总的已支付金额
            BigDecimal oldPaidAmount = bill.getPaidAmount();
            bill.setPaidAmount(totalPaidAmount);

            // 确定总的支付状态
            Integer oldPaymentStatus = bill.getPaymentStatus();
            if (totalPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
                bill.setPaymentStatus(0); // 未支付
            } else if (totalPaidAmount.compareTo(bill.getTotalAmount()) >= 0) {
                bill.setPaymentStatus(2); // 已支付
            } else {
                bill.setPaymentStatus(1); // 部分支付
            }

            // 检查是否需要更新（支付状态或已支付金额发生变化）
            if (oldPaymentStatus == null || !oldPaymentStatus.equals(bill.getPaymentStatus()) ||
                oldPaidAmount == null || oldPaidAmount.compareTo(totalPaidAmount) != 0) {
                needUpdate = true;
            }
            
            if (needUpdate) {
                communityBillMapper.updateCommunityBill(bill);
                fixedCount++;
                System.out.println("修复账单ID=" + bill.getId() +
                    "，支付状态从 " + oldPaymentStatus + " 更新为 " + bill.getPaymentStatus() +
                    "，已支付金额从 " + oldPaidAmount + " 更新为 " + totalPaidAmount);
            }
        }
        
        return fixedCount;
    }
    
    /**
     * 更新单项费用的支付状态
     * 
     * @param bill 账单
     * @param feeType 费用类型
     * @return 是否需要更新
     */
    private boolean updateFeePaymentStatus(CommunityBill bill, int feeType) {
        boolean needUpdate = false;
        
        // 通过payment_detail表计算该费用类型的总支付金额
        BigDecimal totalPaid = BigDecimal.ZERO;
        
        // 查询该账单下指定费用类型的所有支付明细
        List<Map<String, Object>> paymentDetails = paymentDetailMapper.selectPaymentDetailsByBillAndFeeType(bill.getId(), feeType);
        
        for (Map<String, Object> detail : paymentDetails) {
            BigDecimal amount = (BigDecimal) detail.get("payment_amount");
            if (amount != null) {
                totalPaid = totalPaid.add(amount);
            }
        }

        // 更新账单中的已支付金额和支付状态
        BigDecimal totalFee = BigDecimal.ZERO;
        if (feeType == 1 && bill.getPropertyFee() != null) {
            totalFee = bill.getPropertyFee();
            BigDecimal oldPaidAmount = bill.getPropertyPaidAmount();
            Integer oldStatus = bill.getPropertyPaymentStatus();
            
            bill.setPropertyPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setPropertyPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setPropertyPaymentStatus(2);
            } else {
                bill.setPropertyPaymentStatus(1);
            }
            
            if (!totalPaid.equals(oldPaidAmount) || !bill.getPropertyPaymentStatus().equals(oldStatus)) {
                needUpdate = true;
            }
        } else if (feeType == 2 && bill.getParkingFee() != null) {
            totalFee = bill.getParkingFee();
            BigDecimal oldPaidAmount = bill.getParkingPaidAmount();
            Integer oldStatus = bill.getParkingPaymentStatus();
            
            bill.setParkingPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setParkingPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setParkingPaymentStatus(2);
            } else {
                bill.setParkingPaymentStatus(1);
            }
            
            if (!totalPaid.equals(oldPaidAmount) || !bill.getParkingPaymentStatus().equals(oldStatus)) {
                needUpdate = true;
            }
        } else if (feeType == 3 && bill.getSanitationFee() != null) {
            totalFee = bill.getSanitationFee();
            BigDecimal oldPaidAmount = bill.getSanitationPaidAmount();
            Integer oldStatus = bill.getSanitationPaymentStatus();
            
            bill.setSanitationPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setSanitationPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setSanitationPaymentStatus(2);
            } else {
                bill.setSanitationPaymentStatus(1);
            }
            
            if (!totalPaid.equals(oldPaidAmount) || !bill.getSanitationPaymentStatus().equals(oldStatus)) {
                needUpdate = true;
            }
        } else if (feeType == 4 && bill.getElevatorFee() != null) {
            totalFee = bill.getElevatorFee();
            BigDecimal oldPaidAmount = bill.getElevatorPaidAmount();
            Integer oldStatus = bill.getElevatorPaymentStatus();
            
            bill.setElevatorPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setElevatorPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setElevatorPaymentStatus(2);
            } else {
                bill.setElevatorPaymentStatus(1);
            }
            
            if (!totalPaid.equals(oldPaidAmount) || !bill.getElevatorPaymentStatus().equals(oldStatus)) {
                needUpdate = true;
            }
        }

        return needUpdate;
    }
}
