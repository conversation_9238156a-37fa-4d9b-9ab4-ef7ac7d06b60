package com.estatemanagement.system.service.impl;

import java.util.List;
import com.estatemanagement.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.estatemanagement.system.mapper.ElevatorFeeBillMapper;
import com.estatemanagement.system.domain.ElevatorFeeBill;
import com.estatemanagement.system.service.IElevatorFeeBillService;

/**
 * 电梯费Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class ElevatorFeeBillServiceImpl implements IElevatorFeeBillService 
{
    @Autowired
    private ElevatorFeeBillMapper elevatorFeeBillMapper;

    /**
     * 查询电梯费
     * 
     * @param id 电梯费主键
     * @return 电梯费
     */
    @Override
    public ElevatorFeeBill selectElevatorFeeBillById(Long id)
    {
        return elevatorFeeBillMapper.selectElevatorFeeBillById(id);
    }

    /**
     * 查询电梯费列表
     * 
     * @param elevatorFeeBill 电梯费
     * @return 电梯费
     */
    @Override
    public List<ElevatorFeeBill> selectElevatorFeeBillList(ElevatorFeeBill elevatorFeeBill)
    {
        return elevatorFeeBillMapper.selectElevatorFeeBillList(elevatorFeeBill);
    }

    /**
     * 新增电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    @Override
    public int insertElevatorFeeBill(ElevatorFeeBill elevatorFeeBill)
    {
        elevatorFeeBill.setCreateTime(DateUtils.getNowDate());
        return elevatorFeeBillMapper.insertElevatorFeeBill(elevatorFeeBill);
    }

    /**
     * 修改电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    @Override
    public int updateElevatorFeeBill(ElevatorFeeBill elevatorFeeBill)
    {
        return elevatorFeeBillMapper.updateElevatorFeeBill(elevatorFeeBill);
    }

    /**
     * 批量删除电梯费
     * 
     * @param ids 需要删除的电梯费主键
     * @return 结果
     */
    @Override
    public int deleteElevatorFeeBillByIds(Long[] ids)
    {
        return elevatorFeeBillMapper.deleteElevatorFeeBillByIds(ids);
    }

    /**
     * 删除电梯费信息
     * 
     * @param id 电梯费主键
     * @return 结果
     */
    @Override
    public int deleteElevatorFeeBillById(Long id)
    {
        return elevatorFeeBillMapper.deleteElevatorFeeBillById(id);
    }
}
