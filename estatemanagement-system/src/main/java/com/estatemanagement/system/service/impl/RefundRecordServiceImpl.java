package com.estatemanagement.system.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.system.domain.RefundRecord;
import com.estatemanagement.system.domain.RefundDetail;
import com.estatemanagement.system.domain.PaymentRecord;
import com.estatemanagement.system.domain.PaymentDetail;
import com.estatemanagement.system.mapper.RefundRecordMapper;
import com.estatemanagement.system.mapper.RefundDetailMapper;
import com.estatemanagement.system.mapper.PaymentRecordMapper;
import com.estatemanagement.system.mapper.PaymentDetailMapper;
import com.estatemanagement.system.service.IRefundRecordService;

/**
 * 退费记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class RefundRecordServiceImpl implements IRefundRecordService 
{
    @Autowired
    private RefundRecordMapper refundRecordMapper;

    @Autowired
    private RefundDetailMapper refundDetailMapper;

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    /**
     * 查询退费记录
     * 
     * @param id 退费记录主键
     * @return 退费记录
     */
    @Override
    public RefundRecord selectRefundRecordById(Long id)
    {
        RefundRecord refundRecord = refundRecordMapper.selectRefundRecordById(id);
        if (refundRecord != null) {
            // 加载退费明细
            RefundDetail queryDetail = new RefundDetail();
            queryDetail.setRefundId(id);
            List<RefundDetail> refundDetails = refundDetailMapper.selectRefundDetailList(queryDetail);
            refundRecord.setRefundDetailList(refundDetails);
        }
        return refundRecord;
    }

    /**
     * 查询退费记录列表
     * 
     * @param refundRecord 退费记录
     * @return 退费记录
     */
    @Override
    public List<RefundRecord> selectRefundRecordList(RefundRecord refundRecord)
    {
        return refundRecordMapper.selectRefundRecordList(refundRecord);
    }

    /**
     * 查询退费记录列表（包含关联信息）
     * 
     * @param refundRecord 退费记录
     * @return 退费记录集合
     */
    @Override
    public List<Map<String, Object>> selectRefundRecordListWithDetails(RefundRecord refundRecord)
    {
        return refundRecordMapper.selectRefundRecordListWithDetails(refundRecord);
    }

    /**
     * 根据原收费记录ID查询退费记录
     * 
     * @param originalPaymentId 原收费记录ID
     * @return 退费记录集合
     */
    @Override
    public List<RefundRecord> selectRefundRecordsByOriginalPaymentId(Long originalPaymentId)
    {
        return refundRecordMapper.selectRefundRecordsByOriginalPaymentId(originalPaymentId);
    }

    /**
     * 统计退费金额
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    @Override
    public Map<String, Object> selectRefundStatistics(Map<String, Object> params)
    {
        return refundRecordMapper.selectRefundStatistics(params);
    }

    /**
     * 新增退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRefundRecord(RefundRecord refundRecord)
    {
        refundRecord.setCreateTime(DateUtils.getNowDate());
        int result = refundRecordMapper.insertRefundRecord(refundRecord);
        
        // 插入退费明细
        if (refundRecord.getRefundDetailList() != null && !refundRecord.getRefundDetailList().isEmpty()) {
            for (RefundDetail detail : refundRecord.getRefundDetailList()) {
                detail.setRefundId(refundRecord.getId());
                detail.setCreateTime(DateUtils.getNowDate());
                refundDetailMapper.insertRefundDetail(detail);
            }
        }
        
        return result;
    }

    /**
     * 修改退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRefundRecord(RefundRecord refundRecord)
    {
        refundRecord.setUpdateTime(DateUtils.getNowDate());
        
        // 删除原有退费明细
        refundDetailMapper.deleteRefundDetailByRefundId(refundRecord.getId());
        
        // 重新插入退费明细
        if (refundRecord.getRefundDetailList() != null && !refundRecord.getRefundDetailList().isEmpty()) {
            for (RefundDetail detail : refundRecord.getRefundDetailList()) {
                detail.setRefundId(refundRecord.getId());
                detail.setCreateTime(DateUtils.getNowDate());
                refundDetailMapper.insertRefundDetail(detail);
            }
        }
        
        return refundRecordMapper.updateRefundRecord(refundRecord);
    }

    /**
     * 批量删除退费记录
     * 
     * @param ids 需要删除的退费记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRefundRecordByIds(Long[] ids)
    {
        // 删除退费明细
        for (Long id : ids) {
            refundDetailMapper.deleteRefundDetailByRefundId(id);
        }
        
        return refundRecordMapper.deleteRefundRecordByIds(ids);
    }

    /**
     * 删除退费记录信息
     * 
     * @param id 退费记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRefundRecordById(Long id)
    {
        // 删除退费明细
        refundDetailMapper.deleteRefundDetailByRefundId(id);
        
        return refundRecordMapper.deleteRefundRecordById(id);
    }

    /**
     * 审核退费记录
     * 
     * @param id 退费记录ID
     * @param status 审核状态
     * @param auditUserId 审核人ID
     * @param auditUserName 审核人姓名
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    public int auditRefundRecord(Long id, Integer status, Long auditUserId, String auditUserName, String auditRemark)
    {
        return refundRecordMapper.auditRefundRecord(id, status, auditUserId, auditUserName, auditRemark);
    }

    /**
     * 处理退费申请
     * 
     * @param refundData 退费数据
     * @return 退费记录ID
     */
    @Override
    @Transactional
    public Long processRefund(Map<String, Object> refundData)
    {
        try {
            // 解析退费数据
            Long originalPaymentId = Long.valueOf(refundData.get("originalPaymentId").toString());
            Integer refundType = Integer.valueOf(refundData.get("refundType").toString());
            String refundReason = refundData.get("refundReason").toString();
            String refundMethod = refundData.get("refundMethod").toString();
            Long operatorId = Long.valueOf(refundData.get("operatorId").toString());
            String operatorName = refundData.get("operatorName").toString();
            BigDecimal totalRefundAmount = new BigDecimal(refundData.get("totalRefundAmount").toString());
            
            // 获取原收费记录
            PaymentRecord originalPayment = paymentRecordMapper.selectPaymentRecordById(originalPaymentId);
            if (originalPayment == null) {
                throw new RuntimeException("原收费记录不存在");
            }
            
            // 创建退费记录
            RefundRecord refundRecord = new RefundRecord();
            refundRecord.setRefundNumber(generateRefundNumber());
            refundRecord.setOriginalPaymentId(originalPaymentId);
            refundRecord.setOriginalReceiptNumber(originalPayment.getReceiptNumber());
            refundRecord.setCommunityId(originalPayment.getCommunityId());
            refundRecord.setOwnerId(originalPayment.getOwnerId());
            refundRecord.setRefundType(refundType);
            refundRecord.setRefundReason(refundReason);
            refundRecord.setRefundMethod(refundMethod);
            refundRecord.setRefundDate(DateUtils.getNowDate());
            refundRecord.setOperatorId(operatorId);
            refundRecord.setOperatorName(operatorName);
            refundRecord.setTotalRefundAmount(totalRefundAmount);

            // 计算退费金额和明细
            calculateRefundAmountAndDetails(refundRecord, originalPayment, refundData);
            
            // 保存退费记录
            insertRefundRecord(refundRecord);
            
            return refundRecord.getId();
            
        } catch (Exception e) {
            System.err.println("处理退费申请失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("处理退费申请失败: " + e.getMessage());
        }
    }

    /**
     * 计算可退费金额
     * 
     * @param paymentId 收费记录ID
     * @param refundType 退费类型
     * @param refundItems 退费项目
     * @return 可退费金额信息
     */
    @Override
    public Map<String, Object> calculateRefundAmount(Long paymentId, Integer refundType, List<Map<String, Object>> refundItems)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(paymentId);
            if (paymentRecord == null) {
                result.put("error", "收费记录不存在");
                return result;
            }
            
            // 获取收费明细
            PaymentDetail queryDetail = new PaymentDetail();
            queryDetail.setPaymentId(paymentId);
            List<PaymentDetail> paymentDetails = paymentDetailMapper.selectPaymentDetailList(queryDetail);
            
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            List<Map<String, Object>> refundDetailList = new ArrayList<>();
            
            if (refundType == 1) {
                // 全额退费
                for (PaymentDetail detail : paymentDetails) {
                    Map<String, Object> refundDetailInfo = calculateDetailRefundAmount(detail, detail.getPaymentAmount());
                    refundDetailList.add(refundDetailInfo);
                    totalRefundAmount = totalRefundAmount.add((BigDecimal) refundDetailInfo.get("refundAmount"));
                }
            } else {
                // 部分退费
                for (Map<String, Object> item : refundItems) {
                    Long detailId = Long.valueOf(item.get("detailId").toString());
                    BigDecimal refundAmount = new BigDecimal(item.get("refundAmount").toString());
                    
                    PaymentDetail detail = paymentDetails.stream()
                        .filter(d -> d.getId().equals(detailId))
                        .findFirst()
                        .orElse(null);
                    
                    if (detail != null) {
                        Map<String, Object> refundDetailInfo = calculateDetailRefundAmount(detail, refundAmount);
                        refundDetailList.add(refundDetailInfo);
                        totalRefundAmount = totalRefundAmount.add(refundAmount);
                    }
                }
            }
            
            result.put("totalRefundAmount", totalRefundAmount);
            result.put("refundDetailList", refundDetailList);
            
        } catch (Exception e) {
            System.err.println("计算退费金额失败: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "计算失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取收费记录的退费信息
     * 
     * @param paymentId 收费记录ID
     * @return 退费信息
     */
    @Override
    public Map<String, Object> getPaymentRefundInfo(Long paymentId)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取收费记录
            PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(paymentId);
            if (paymentRecord == null) {
                result.put("error", "收费记录不存在");
                return result;
            }
            
            // 获取收费明细
            PaymentDetail queryDetail = new PaymentDetail();
            queryDetail.setPaymentId(paymentId);
            List<PaymentDetail> paymentDetails = paymentDetailMapper.selectPaymentDetailList(queryDetail);
            
            // 获取已退费记录
            List<RefundRecord> refundRecords = selectRefundRecordsByOriginalPaymentId(paymentId);
            
            // 计算已退费金额
            BigDecimal totalRefundedAmount = refundRecords.stream()
                .filter(r -> r.getStatus() == 1) // 只计算已完成的退费
                .map(RefundRecord::getTotalRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 计算可退费金额
            BigDecimal totalPaymentAmount = paymentRecord.getPaymentAmount();
            BigDecimal availableRefundAmount = totalPaymentAmount.subtract(totalRefundedAmount);
            
            result.put("paymentRecord", paymentRecord);
            result.put("paymentDetails", paymentDetails);
            result.put("refundRecords", refundRecords);
            result.put("totalPaymentAmount", totalPaymentAmount);
            result.put("totalRefundedAmount", totalRefundedAmount);
            result.put("availableRefundAmount", availableRefundAmount);
            result.put("canRefund", availableRefundAmount.compareTo(BigDecimal.ZERO) > 0);
            
        } catch (Exception e) {
            System.err.println("获取退费信息失败: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "获取失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 生成退费单号
     */
    private String generateRefundNumber() {
        return "RF" + DateUtils.dateTimeNow("yyyyMMddHHmmss") + String.format("%03d", (int)(Math.random() * 1000));
    }

    /**
     * 计算退费金额和明细
     */
    private void calculateRefundAmountAndDetails(RefundRecord refundRecord, PaymentRecord originalPayment, Map<String, Object> refundData) {
        try {
            // 获取原收费明细
            PaymentDetail queryDetail = new PaymentDetail();
            queryDetail.setPaymentId(originalPayment.getId());
            List<PaymentDetail> originalDetails = paymentDetailMapper.selectPaymentDetailList(queryDetail);

            // 获取退费项目列表
            List<Map<String, Object>> refundItems = (List<Map<String, Object>>) refundData.get("refundItems");
            if (refundItems == null || refundItems.isEmpty()) {
                throw new RuntimeException("退费项目不能为空");
            }

            // 初始化退费金额统计
            BigDecimal totalRefundAmount = BigDecimal.ZERO;
            BigDecimal propertyFeeAmount = BigDecimal.ZERO;
            BigDecimal parkingFeeAmount = BigDecimal.ZERO;
            BigDecimal sanitationFeeAmount = BigDecimal.ZERO;
            BigDecimal elevatorFeeAmount = BigDecimal.ZERO;
            BigDecimal lateFeeAmount = BigDecimal.ZERO;

            // 创建退费明细列表
            List<RefundDetail> refundDetailList = new ArrayList<>();

            // 处理每个退费项目
            for (Map<String, Object> refundItem : refundItems) {
                Long detailId = Long.valueOf(refundItem.get("detailId").toString());
                BigDecimal refundAmount = new BigDecimal(refundItem.get("refundAmount").toString());

                // 查找对应的原收费明细
                PaymentDetail originalDetail = originalDetails.stream()
                    .filter(detail -> detail.getId().equals(detailId))
                    .findFirst()
                    .orElse(null);

                if (originalDetail == null) {
                    continue;
                }

                // 验证退费金额不超过原支付金额
                if (refundAmount.compareTo(originalDetail.getPaymentAmount()) > 0) {
                    throw new RuntimeException("退费金额不能超过原支付金额");
                }

                // 创建退费明细
                RefundDetail refundDetail = createRefundDetail(originalDetail, refundAmount);
                refundDetailList.add(refundDetail);

                // 累计总退费金额
                totalRefundAmount = totalRefundAmount.add(refundAmount);

                // 按费用类型累计金额
                switch (originalDetail.getFeeType()) {
                    case 1: // 物业费
                        propertyFeeAmount = propertyFeeAmount.add(refundAmount);
                        break;
                    case 2: // 停车费
                        parkingFeeAmount = parkingFeeAmount.add(refundAmount);
                        break;
                    case 3: // 卫生费
                        sanitationFeeAmount = sanitationFeeAmount.add(refundAmount);
                        break;
                    case 4: // 电梯费
                        elevatorFeeAmount = elevatorFeeAmount.add(refundAmount);
                        break;
                    case 5: // 滞纳金
                        lateFeeAmount = lateFeeAmount.add(refundAmount);
                        break;
                }
            }

            // 设置退费记录的金额信息
            refundRecord.setTotalRefundAmount(totalRefundAmount);
            refundRecord.setPropertyFeeAmount(propertyFeeAmount);
            refundRecord.setParkingFeeAmount(parkingFeeAmount);
            refundRecord.setSanitationFeeAmount(sanitationFeeAmount);
            refundRecord.setElevatorFeeAmount(elevatorFeeAmount);
            refundRecord.setLateFeeAmount(lateFeeAmount);

            // 设置退费明细列表
            refundRecord.setRefundDetailList(refundDetailList);

        } catch (Exception e) {
            System.err.println("计算退费金额和明细失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("计算退费金额失败: " + e.getMessage());
        }
    }

    /**
     * 计算明细退费金额
     */
    private Map<String, Object> calculateDetailRefundAmount(PaymentDetail detail, BigDecimal refundAmount) {
        Map<String, Object> result = new HashMap<>();
        
        // 计算退费周期
        if (detail.getPeriodStart() != null && detail.getPeriodEnd() != null && detail.getPaymentMonths() != null) {
            LocalDate periodStart = detail.getPeriodStart().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            LocalDate periodEnd = detail.getPeriodEnd().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            
            // 计算剩余天数
            long totalDays = ChronoUnit.DAYS.between(periodStart, periodEnd) + 1;
            long remainingDays = ChronoUnit.DAYS.between(currentDate, periodEnd);
            
            if (remainingDays > 0) {
                // 按比例计算退费金额
                BigDecimal dailyAmount = detail.getPaymentAmount().divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP);
                BigDecimal calculatedRefundAmount = dailyAmount.multiply(new BigDecimal(remainingDays)).setScale(2, RoundingMode.DOWN);
                
                result.put("calculatedRefundAmount", calculatedRefundAmount);
                result.put("remainingDays", remainingDays);
                result.put("refundPeriodStart", currentDate);
                result.put("refundPeriodEnd", periodEnd);
            }
        }
        
        result.put("refundAmount", refundAmount);
        result.put("originalAmount", detail.getPaymentAmount());
        result.put("feeType", detail.getFeeType());
        result.put("feeName", detail.getFeeName());
        
        return result;
    }

    /**
     * 创建退费明细
     *
     * @param originalDetail 原收费明细
     * @param refundAmount 退费金额
     * @return 退费明细
     */
    private RefundDetail createRefundDetail(PaymentDetail originalDetail, BigDecimal refundAmount) {
        RefundDetail refundDetail = new RefundDetail();

        refundDetail.setOriginalPaymentDetailId(originalDetail.getId());
        refundDetail.setFeeType(originalDetail.getFeeType());
        refundDetail.setFeeName(originalDetail.getFeeName());
        refundDetail.setOriginalPaymentAmount(originalDetail.getPaymentAmount());
        refundDetail.setRefundAmount(refundAmount);

        // 设置原缴费周期
        refundDetail.setOriginalPeriodStart(originalDetail.getPeriodStart());
        refundDetail.setOriginalPeriodEnd(originalDetail.getPeriodEnd());

        // 计算退费周期和剩余周期
        calculateRefundPeriod(refundDetail, originalDetail, refundAmount);

        return refundDetail;
    }

    /**
     * 计算退费周期
     *
     * @param refundDetail 退费明细
     * @param originalDetail 原收费明细
     * @param refundAmount 退费金额
     */
    private void calculateRefundPeriod(RefundDetail refundDetail, PaymentDetail originalDetail, BigDecimal refundAmount) {
        try {
            if (originalDetail.getPeriodStart() == null || originalDetail.getPeriodEnd() == null) {
                return;
            }

            LocalDate periodStart = originalDetail.getPeriodStart().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            LocalDate periodEnd = originalDetail.getPeriodEnd().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();

            // 计算总天数和退费比例
            long totalDays = ChronoUnit.DAYS.between(periodStart, periodEnd) + 1;
            BigDecimal refundRatio = refundAmount.divide(originalDetail.getPaymentAmount(), 4, RoundingMode.HALF_UP);

            if (currentDate.isBefore(periodEnd)) {
                // 如果当前时间在缴费周期内，从当前时间开始退费
                long remainingDays = ChronoUnit.DAYS.between(currentDate, periodEnd) + 1;
                long refundDays = Math.round(remainingDays * refundRatio.doubleValue());

                refundDetail.setRefundPeriodStart(java.sql.Date.valueOf(currentDate));
                refundDetail.setRefundPeriodEnd(java.sql.Date.valueOf(currentDate.plusDays(refundDays - 1)));
                refundDetail.setRefundDays(refundDays);

                // 剩余周期（如果有）
                if (refundDays < remainingDays) {
                    refundDetail.setRemainingPeriodStart(java.sql.Date.valueOf(currentDate.plusDays(refundDays)));
                    refundDetail.setRemainingPeriodEnd(java.sql.Date.valueOf(periodEnd));
                }
            } else {
                // 如果缴费周期已过，按比例计算退费天数
                long refundDays = Math.round(totalDays * refundRatio.doubleValue());
                refundDetail.setRefundDays(refundDays);
                refundDetail.setRefundPeriodStart(originalDetail.getPeriodStart());
                refundDetail.setRefundPeriodEnd(java.sql.Date.valueOf(periodStart.plusDays(refundDays - 1)));

                // 剩余周期
                if (refundDays < totalDays) {
                    refundDetail.setRemainingPeriodStart(java.sql.Date.valueOf(periodStart.plusDays(refundDays)));
                    refundDetail.setRemainingPeriodEnd(originalDetail.getPeriodEnd());
                }
            }

            // 计算退费月数
            if (originalDetail.getPaymentMonths() != null) {
                BigDecimal refundMonths = new BigDecimal(originalDetail.getPaymentMonths()).multiply(refundRatio);
                refundDetail.setRefundMonths(refundMonths.longValue());
            }

        } catch (Exception e) {
            System.err.println("计算退费周期失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
