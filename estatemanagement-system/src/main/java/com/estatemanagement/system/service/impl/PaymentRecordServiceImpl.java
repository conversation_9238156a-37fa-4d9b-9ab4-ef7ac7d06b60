package com.estatemanagement.system.service.impl;

import com.estatemanagement.common.utils.DateUtils;
import com.estatemanagement.system.domain.*;
import com.estatemanagement.system.enums.PaymentAuditStatus;
import com.estatemanagement.system.mapper.*;
import com.estatemanagement.system.service.IFeeCalculationService;
import com.estatemanagement.system.service.IPaymentRecordService;
import com.estatemanagement.system.utils.SanitationFeeCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * 收费记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-14
 */
@Slf4j
@Service
public class PaymentRecordServiceImpl implements IPaymentRecordService {
    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Autowired
    private CommunityBillMapper communityBillMapper;

    @Autowired
    private OwnerMangementMapper ownerMangementMapper;

    @Autowired
    private PaymentDetailMapper paymentDetailMapper;

    @Autowired
    private ParkingInfoMapper parkingInfoMapper;

    @Autowired
    private CommunityMangementMapper communityMangementMapper;

    @Autowired
    private IFeeCalculationService feeCalculationService;

    @Autowired
    private com.estatemanagement.system.mapper.SysUserMapper sysUserMapper;

    /**
     * 查询收费记录
     *
     * @param id 收费记录主键
     * @return 收费记录
     */
    @Override
    public PaymentRecord selectPaymentRecordById(Long id) {
        return paymentRecordMapper.selectPaymentRecordById(id);
    }

    /**
     * 查询收费记录列表
     *
     * @param paymentRecord 收费记录
     * @return 收费记录
     */
    @Override
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord) {
        return paymentRecordMapper.selectPaymentRecordList(paymentRecord);
    }

    /**
     * 查询业主的未完全支付账单，用于收费管理
     *
     * @param communityId 小区ID（可选）
     * @param ownerId     业主ID（可选）
     * @return 账单列表
     */
    @Override
    public List<Map<String, Object>> selectUnpaidBillsForPayment(Long communityId, Long ownerId) {
        return paymentRecordMapper.selectUnpaidBillsForPayment(communityId, ownerId);
    }

    /**
     * 查询指定账单的收费记录及明细
     *
     * @param billId 账单ID
     * @return 收费记录及明细列表
     */
    @Override
    public List<Map<String, Object>> selectPaymentRecordsByBillId(Long billId) {
        return paymentRecordMapper.selectPaymentRecordsByBillId(billId);
    }

    /**
     * 执行收费操作
     *
     * @param paymentData 收费数据
     * @return 收费记录ID
     */
    @Override
    @Transactional
    public Long processPayment(Map<String, Object> paymentData) {
        // 解析收费数据
        String paymentMethod = paymentData.get("paymentMethod").toString();
        String paymentType = paymentData.get("paymentType").toString(); // full, partial, advance
        Long communityId = Long.valueOf(paymentData.get("communityId").toString());
        Long ownerId = Long.valueOf(paymentData.get("ownerId").toString());

        // 获取账单信息（可能为空，用于预交费）
        CommunityBill bill = null;
        if (paymentData.get("billId") != null) {
            Long billId = Long.valueOf(paymentData.get("billId").toString());
            bill = communityBillMapper.selectCommunityBillById(billId);
            if (bill == null) {
                throw new RuntimeException("账单不存在");
            }
        }

        // 创建收费记录
        PaymentRecord paymentRecord = new PaymentRecord();
        if (bill != null) {
            paymentRecord.setBillId(bill.getId());
        }
        paymentRecord.setReceiptNumber(generateReceiptNumber(communityId));
        paymentRecord.setPaymentMethod(paymentMethod);
        paymentRecord.setPaymentDate(DateUtils.getNowDate());
        paymentRecord.setOperatorId(Long.valueOf(paymentData.get("operatorId").toString()));
        paymentRecord.setCommunityId(communityId);
        paymentRecord.setOwnerId(ownerId);

        BigDecimal totalPaymentAmount = BigDecimal.ZERO;

        // 处理不同类型的收费
        if ("full".equals(paymentType)) {
            // 全额支付（必须有账单）
            if (bill == null) {
                throw new RuntimeException("全额支付必须选择账单");
            }
            totalPaymentAmount = processFullPayment(paymentRecord, bill, paymentData);
        } else if ("advance".equals(paymentType)) {
            // 预交费用（可以没有账单）
            totalPaymentAmount = processAdvancePaymentWithoutBill(paymentRecord, paymentData);
        } else if ("mixed".equals(paymentType)) {
            // 部分支付（可以没有账单）
            totalPaymentAmount = processMixedPayment(paymentRecord, paymentData);
        }

        paymentRecord.setPaymentAmount(totalPaymentAmount);
        paymentRecord.setDueAmount(totalPaymentAmount); // 设置应缴金额等于实收金额
        paymentRecord.setAuditStatus(PaymentAuditStatus.PENDING.getCode()); // 设置默认审核状态为待审核
        paymentRecord.setCreateTime(DateUtils.getNowDate());

        // 插入收费记录
        paymentRecordMapper.insertPaymentRecord(paymentRecord);

        // 生成收费明细记录
        generatePaymentDetailsWithoutBill(paymentRecord, paymentData, paymentType);

        // 如果有账单，更新账单状态
        if (bill != null) {
            updateBillPaymentStatus(bill);
        }

        return paymentRecord.getId();
    }

    /**
     * 处理全额支付
     * @param paymentRecord  支付记录
     * @param bill           账单
     * @param paymentData    支付数据
     * @return
     */
    private BigDecimal processFullPayment(PaymentRecord paymentRecord, CommunityBill bill, Map<String, Object> paymentData) {
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 在计算未支付金额前，先重新计算各项费用的已支付金额，确保使用最新数据
        updateFeePaymentStatus(bill, 1); // 物业费
        updateFeePaymentStatus(bill, 2); // 停车费
        updateFeePaymentStatus(bill, 3); // 卫生费
        updateFeePaymentStatus(bill, 4); // 电梯费
        updateFeePaymentStatus(bill, 5); // 滞纳金

        // 设置各费用金额（使用更新后的已支付金额）
        if (bill.getPropertyFee() != null && bill.getPropertyFee().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal unpaidPropertyFee = bill.getPropertyFee().subtract(bill.getPropertyPaidAmount() != null ? bill.getPropertyPaidAmount() : BigDecimal.ZERO);
            if (unpaidPropertyFee.compareTo(BigDecimal.ZERO) > 0) {
                paymentRecord.setPropertyFeeAmount(unpaidPropertyFee);
                totalAmount = totalAmount.add(unpaidPropertyFee);
            }
        }

        if (bill.getParkingFee() != null && bill.getParkingFee().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal unpaidParkingFee = bill.getParkingFee().subtract(bill.getParkingPaidAmount() != null ? bill.getParkingPaidAmount() : BigDecimal.ZERO);
            if (unpaidParkingFee.compareTo(BigDecimal.ZERO) > 0) {
                paymentRecord.setParkingFeeAmount(unpaidParkingFee);
                totalAmount = totalAmount.add(unpaidParkingFee);
            }
        }

        if (bill.getSanitationFee() != null && bill.getSanitationFee().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal unpaidSanitationFee = bill.getSanitationFee().subtract(bill.getSanitationPaidAmount() != null ? bill.getSanitationPaidAmount() : BigDecimal.ZERO);
            if (unpaidSanitationFee.compareTo(BigDecimal.ZERO) > 0) {
                paymentRecord.setSanitationFeeAmount(unpaidSanitationFee);
                totalAmount = totalAmount.add(unpaidSanitationFee);
            }
        }

        if (bill.getElevatorFee() != null && bill.getElevatorFee().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal unpaidElevatorFee = bill.getElevatorFee().subtract(bill.getElevatorPaidAmount() != null ? bill.getElevatorPaidAmount() : BigDecimal.ZERO);
            if (unpaidElevatorFee.compareTo(BigDecimal.ZERO) > 0) {
                paymentRecord.setElevatorFeeAmount(unpaidElevatorFee);
                totalAmount = totalAmount.add(unpaidElevatorFee);
            }
        }


        paymentRecord.setIsPartial(0);
        paymentRecord.setIsAdvance(0);

        return totalAmount;
    }

    /**
     * 处理混合支付
     */
    private BigDecimal processMixedPayment(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        try {
            List<Map<String, Object>> mixedFeeDetails = (List<Map<String, Object>>) paymentData.get("mixedFeeDetails");
            if (mixedFeeDetails == null || mixedFeeDetails.isEmpty()) {
                throw new RuntimeException("混合支付费用明细不能为空");
            }

            BigDecimal totalAmount = BigDecimal.ZERO;
            StringBuilder plateNumbers = new StringBuilder();
            boolean hasParking = false;

            // 累计所有费用类型的金额，并收集车牌号信息
            for (Map<String, Object> feeDetail : mixedFeeDetails) {
                BigDecimal feeAmount = new BigDecimal(feeDetail.get("totalAmount").toString());
                int feeType = Integer.parseInt(feeDetail.get("feeType").toString());

                totalAmount = totalAmount.add(feeAmount);

                // 如果是停车费，收集车牌号信息
                if (feeType == 2) {
                    hasParking = true;
                    String plateNumber = (String) feeDetail.get("plateNumber");
                    if (plateNumber != null && !plateNumber.isEmpty()) {
                        if (plateNumbers.length() > 0) {
                            plateNumbers.append(",");
                        }
                        plateNumbers.append(plateNumber);
                    }
                }

                // 根据费用类型设置对应的金额字段
                switch (feeType) {
                    case 1: // 物业费
                        paymentRecord.setPropertyFeeAmount(feeAmount);
                        break;
                    case 2: // 停车费
                        paymentRecord.setParkingFeeAmount(feeAmount);
                        break;
                    case 3: // 卫生费
                        paymentRecord.setSanitationFeeAmount(feeAmount);
                        break;
                    case 4: // 电梯费
                        paymentRecord.setElevatorFeeAmount(feeAmount);
                        break;
                }
            }


            // 设置支付记录标识
            paymentRecord.setIsPartial(0);
            paymentRecord.setIsAdvance(0);
            paymentRecord.setFeeType(0L); // 混合支付设置为0

            return totalAmount;
        } catch (Exception e) {
            System.err.println("处理混合支付失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("混合支付处理失败: " + e.getMessage());
        }
    }


    /**
     * 处理预交费用（无账单）
     */
    private BigDecimal processAdvancePaymentWithoutBill(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        // 检查是否为新的多费用类型预交费用
        if (paymentData.containsKey("advanceFeeTypes") && paymentData.containsKey("advanceFeeDetails")) {
            return processMultiTypeAdvancePayment(paymentRecord, paymentData);
        }

        // 兼容旧的单费用类型预交费用
        if (!paymentData.containsKey("feeType")) {
            throw new RuntimeException("缺少费用类型参数");
        }
        if (!paymentData.containsKey("advanceMonths")) {
            throw new RuntimeException("缺少预交月数参数");
        }

        String feeType = paymentData.get("feeType").toString();
        Integer advanceMonths = Integer.valueOf(paymentData.get("advanceMonths").toString());

        BigDecimal paymentAmount = BigDecimal.ZERO;
        paymentRecord.setFeeType(Long.valueOf(feeType));
        paymentRecord.setPaymentMonths(Long.valueOf(advanceMonths));
        paymentRecord.setIsPartial(0);
        paymentRecord.setIsAdvance(1);

        // 根据费用类型计算预交金额（需要查询小区配置）
        if ("1".equals(feeType)) { // 物业费
            paymentAmount = calculateAdvanceFeeAmountFromCommunity(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), 1, advanceMonths);
            paymentRecord.setPropertyFeeAmount(paymentAmount);

            // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
            // updateOwnerFeeEndDate(paymentRecord.getOwnerId(), 1, advanceMonths);
        } else if ("2".equals(feeType)) { // 停车费
            paymentAmount = calculateAdvanceFeeAmountFromCommunity(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), 2, advanceMonths);
            paymentRecord.setParkingFeeAmount(paymentAmount);
        } else if ("3".equals(feeType)) { // 卫生费
            paymentAmount = calculateAdvanceFeeAmountFromCommunity(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), 3, advanceMonths);
            paymentRecord.setSanitationFeeAmount(paymentAmount);

            // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
            // updateOwnerFeeEndDate(paymentRecord.getOwnerId(), 3, advanceMonths);
        }

        return paymentAmount;
    }

    /**
     * 处理多费用类型预交费用
     */
    private BigDecimal processMultiTypeAdvancePayment(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        try {
            List<Map<String, Object>> advanceFeeDetails = (List<Map<String, Object>>) paymentData.get("advanceFeeDetails");
            if (advanceFeeDetails == null || advanceFeeDetails.isEmpty()) {
                throw new RuntimeException("预交费用明细不能为空");
            }

            BigDecimal totalAmount = BigDecimal.ZERO;
            Integer advanceMonths = Integer.valueOf(paymentData.get("advanceMonths").toString());

            // 累计所有费用类型的金额
            for (Map<String, Object> feeDetail : advanceFeeDetails) {
                BigDecimal feeAmount = new BigDecimal(feeDetail.get("totalAmount").toString());
                Integer feeType = Integer.valueOf(feeDetail.get("feeType").toString());

                totalAmount = totalAmount.add(feeAmount);

                // 根据费用类型设置对应的金额字段
                switch (feeType) {
                    case 1: // 物业费
                        paymentRecord.setPropertyFeeAmount(feeAmount);
                        // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
                        // updateOwnerFeeEndDate(paymentRecord.getOwnerId(), 1, advanceMonths);
                        break;
                    case 2: // 停车费
                        paymentRecord.setParkingFeeAmount(feeAmount);
                        break;
                    case 3: // 卫生费
                        paymentRecord.setSanitationFeeAmount(feeAmount);
                        // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
                        // updateOwnerFeeEndDate(paymentRecord.getOwnerId(), 3, advanceMonths);
                        break;
                    case 4: // 电梯费
                        paymentRecord.setElevatorFeeAmount(feeAmount);
                        break;
                }
            }

            // 设置支付记录标识
            paymentRecord.setPaymentMonths(Long.valueOf(advanceMonths));
            paymentRecord.setIsPartial(0);
            paymentRecord.setIsAdvance(1);
            paymentRecord.setFeeType(0L); // 多费用类型设置为0

            return totalAmount;
        } catch (Exception e) {
            System.err.println("处理多费用类型预交费用失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("预交费用处理失败: " + e.getMessage());
        }
    }

    /**
     * 根据小区配置计算预交费用金额
     */
    private BigDecimal calculateAdvanceFeeAmountFromCommunity(Long communityId, Long ownerId, int feeType, int months) {
        try {
            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
            // 获取业主信息
            OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);

            if (community == null || owner == null) {
                return BigDecimal.ZERO;
            }

            BigDecimal monthlyFee = BigDecimal.ZERO;

            // 使用统一的费用计算服务计算预交费用
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = feeCalculationService.calculateEndDateByMonths(startDate, months);

            FeeCalculationResult feeResult = null;

            if (feeType == 1) { // 物业费
                if (community.getCommunityPrice() != null && owner.getHouseArea() != null) {
                    feeResult = feeCalculationService.calculatePropertyFee(startDate, endDate, community.getCommunityPrice(), owner.getHouseArea());
                }
            } else if (feeType == 2) { // 停车费
                // 查询业主的停车位信息
                ParkingInfo queryParking = new ParkingInfo();
                queryParking.setOwnerId(ownerId);
                queryParking.setStatus(1);
                List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);

                if (!parkingList.isEmpty()) {
                    // 根据停车位的三证合一状态从小区配置中获取停车费
                    BigDecimal monthlyParkingFee = null;
                    ParkingInfo parking = parkingList.get(0);

                    if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                        // 三证合一停车费（使用原业主停车费字段）
                        monthlyParkingFee = community.getOwnerParkingFee();
                    } else {
                        // 非三证合一停车费（使用原租客停车费字段）
                        monthlyParkingFee = community.getTenantParkingFee();
                    }

                    if (monthlyParkingFee != null && monthlyParkingFee.compareTo(BigDecimal.ZERO) > 0) {
                        feeResult = feeCalculationService.calculateParkingFee(startDate, endDate, monthlyParkingFee);
                    }
                }
            } else if (feeType == 3) { // 卫生费
                if (owner.getHouseArea() != null) {
                    BigDecimal sanitationFeeRate = getSanitationFeeRate(community, owner.getHouseArea());
                    if (sanitationFeeRate != null && sanitationFeeRate.compareTo(BigDecimal.ZERO) > 0) {
                        feeResult = feeCalculationService.calculateSanitationFee(startDate, endDate, sanitationFeeRate);
                    }
                }
            }

            return feeResult != null ? feeResult.getTotalFee() : BigDecimal.ZERO;

        } catch (Exception e) {
            System.err.println("计算预交费用失败: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据房屋面积获取卫生费标准
     */
    private BigDecimal getSanitationFeeRate(CommunityMangement community, BigDecimal houseArea) {
        return SanitationFeeCalculator.calculateSanitationFee(community, houseArea);
    }

    /**
     * 获取月停车费（直接从小区配置读取）
     */
    private BigDecimal getMonthlyParkingFeeFromBill(CommunityBill bill) {
        try {
            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(bill.getCommunityId());
            if (community == null) {
                return BigDecimal.ZERO;
            }

            // 查询该业主的停车位信息，优先使用具体的停车位配置
            ParkingInfo queryParking = new ParkingInfo();
            queryParking.setOwnerId(bill.getOwnerId());
            queryParking.setStatus(1);
            List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);

            if (!parkingList.isEmpty()) {
                ParkingInfo parking = parkingList.get(0);
                // 根据三证合一状态从小区配置中获取停车费
                if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                    // 三证合一停车费
                    return community.getOwnerParkingFee() != null ? community.getOwnerParkingFee() : BigDecimal.ZERO;
                } else {
                    // 非三证合一停车费
                    return community.getTenantParkingFee() != null ? community.getTenantParkingFee() : BigDecimal.ZERO;
                }
            } else {
                // 如果没有停车位信息，使用默认的业主停车费
                return community.getOwnerParkingFee() != null ? community.getOwnerParkingFee() : BigDecimal.ZERO;
            }
        } catch (Exception e) {
            System.err.println("获取月停车费失败: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ZERO;
        }
    }


    /**
     * 更新账单支付状态
     */
    private void updateBillPaymentStatus(CommunityBill bill) {
        // 重新计算各项费用的支付状态
        updateFeePaymentStatus(bill, 1); // 物业费
        updateFeePaymentStatus(bill, 2); // 停车费
        updateFeePaymentStatus(bill, 3); // 卫生费
        updateFeePaymentStatus(bill, 4); // 电梯费


        // 计算总的已支付金额
        BigDecimal totalPaidAmount = BigDecimal.ZERO;
        if (bill.getPropertyPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getPropertyPaidAmount());
        if (bill.getParkingPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getParkingPaidAmount());
        if (bill.getSanitationPaidAmount() != null)
            totalPaidAmount = totalPaidAmount.add(bill.getSanitationPaidAmount());
        if (bill.getElevatorPaidAmount() != null) totalPaidAmount = totalPaidAmount.add(bill.getElevatorPaidAmount());

        bill.setPaidAmount(totalPaidAmount);

        // 确定总的支付状态
        if (totalPaidAmount.compareTo(BigDecimal.ZERO) == 0) {
            bill.setPaymentStatus(0); // 未支付
        } else if (totalPaidAmount.compareTo(bill.getTotalAmount()) >= 0) {
            bill.setPaymentStatus(2); // 已支付
        } else {
            bill.setPaymentStatus(1); // 部分支付
        }

        communityBillMapper.updateCommunityBill(bill);
    }

    /**
     * 更新单项费用的支付状态
     */
    private void updateFeePaymentStatus(CommunityBill bill, int feeType) {
        // 通过payment_detail表计算该费用类型的总支付金额
        BigDecimal totalPaid = BigDecimal.ZERO;

        // 查询该账单下指定费用类型的所有支付明细
        List<Map<String, Object>> paymentDetails = paymentDetailMapper.selectPaymentDetailsByBillAndFeeType(bill.getId(), feeType);

        for (Map<String, Object> detail : paymentDetails) {
            BigDecimal amount = (BigDecimal) detail.get("payment_amount");
            if (amount != null) {
                totalPaid = totalPaid.add(amount);
            }
        }

        // 更新账单中的已支付金额和支付状态
        BigDecimal totalFee = BigDecimal.ZERO;
        if (feeType == 1 && bill.getPropertyFee() != null) {
            totalFee = bill.getPropertyFee();
            bill.setPropertyPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setPropertyPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setPropertyPaymentStatus(2);
            } else {
                bill.setPropertyPaymentStatus(1);
            }
        } else if (feeType == 2 && bill.getParkingFee() != null) {
            totalFee = bill.getParkingFee();
            bill.setParkingPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setParkingPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setParkingPaymentStatus(2);
            } else {
                bill.setParkingPaymentStatus(1);
            }
        } else if (feeType == 3 && bill.getSanitationFee() != null) {
            totalFee = bill.getSanitationFee();
            bill.setSanitationPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setSanitationPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setSanitationPaymentStatus(2);
            } else {
                bill.setSanitationPaymentStatus(1);
            }
        } else if (feeType == 4 && bill.getElevatorFee() != null) {
            totalFee = bill.getElevatorFee();
            bill.setElevatorPaidAmount(totalPaid);
            if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
                bill.setElevatorPaymentStatus(0);
            } else if (totalPaid.compareTo(totalFee) >= 0) {
                bill.setElevatorPaymentStatus(2);
            } else {
                bill.setElevatorPaymentStatus(1);
            }
        }
    }

    /**
     * 生成收据编号
     */
    private String generateReceiptNumber(Long communityId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        String prefix = String.format("%03d%s", communityId, dateStr);

        // 查询当天该小区已生成的收据数量
        PaymentRecord queryRecord = new PaymentRecord();
        queryRecord.setCommunityId(communityId);
        List<PaymentRecord> todayRecords = paymentRecordMapper.selectPaymentRecordList(queryRecord);

        // 过滤当天的记录
        long todayCount = todayRecords.stream().filter(record -> record.getCreateTime() != null && sdf.format(record.getCreateTime()).equals(dateStr)).count();

        // 生成序号（从001开始）
        String sequence = String.format("%03d", todayCount + 1);

        return prefix + sequence;
    }

    /**
     * 新增收费记录
     *
     * @param paymentRecord 收费记录
     * @return 结果
     */
    @Override
    public int insertPaymentRecord(PaymentRecord paymentRecord) {
        paymentRecord.setCreateTime(DateUtils.getNowDate());
        return paymentRecordMapper.insertPaymentRecord(paymentRecord);
    }

    /**
     * 修改收费记录
     *
     * @param paymentRecord 收费记录
     * @return 结果
     */
    @Override
    public int updatePaymentRecord(PaymentRecord paymentRecord) {
        return paymentRecordMapper.updatePaymentRecord(paymentRecord);
    }

    /**
     * 批量删除收费记录
     *
     * @param ids 需要删除的收费记录主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordByIds(Long[] ids) {
        return paymentRecordMapper.deletePaymentRecordByIds(ids);
    }

    /**
     * 删除收费记录信息
     *
     * @param id 收费记录主键
     * @return 结果
     */
    @Override
    public int deletePaymentRecordById(Long id) {
        return paymentRecordMapper.deletePaymentRecordById(id);
    }

    /**
     * 计算预交费用金额
     *
     * @param communityId 小区ID
     * @param ownerId     业主ID
     * @param feeType     费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months      预交月数
     * @return 预交费用金额
     */
    @Override
    public BigDecimal calculateAdvanceFeeAmount(Long communityId, Long ownerId, Integer feeType, Integer months) {
        return calculateAdvanceFeeAmountFromCommunity(communityId, ownerId, feeType, months);
    }

    /**
     * 根据收费记录ID查询收费明细
     *
     * @param paymentId 收费记录ID
     * @return 收费明细集合
     */
    @Override
    public List<Map<String, Object>> selectPaymentDetailsByPaymentId(Long paymentId) {
        return paymentDetailMapper.selectPaymentDetailsByPaymentId(paymentId);
    }

    /**
     * 查询收费记录详情（包含明细）用于打印
     *
     * @param paymentId 收费记录ID
     * @return 包含收费记录和明细的Map对象
     */
    @Override
    public Map<String, Object> selectPaymentRecordWithDetails(Long paymentId) {
        Map<String, Object> result = new HashMap<>();

        // 查询收费记录信息
        PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(paymentId);
        if (paymentRecord == null) {
            return result;
        }

        // 获取业主信息
        OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(paymentRecord.getOwnerId());
        // 获取小区信息
        CommunityMangement community = communityMangementMapper.selectCommunityMangementById(paymentRecord.getCommunityId());
        // 获取操作人信息
        String operatorName = "系统";
        if (paymentRecord.getOperatorId() != null) {
            try {
                com.estatemanagement.common.core.domain.entity.SysUser sysUser = sysUserMapper.selectUserById(paymentRecord.getOperatorId());
                if (sysUser != null) {
                    operatorName = sysUser.getNickName(); // 使用用户昵称作为操作员姓名
                }
            } catch (Exception e) {
                operatorName = "系统";
            }
        }

        // 构建收费记录基本信息
        result.put("id", paymentRecord.getId());
        result.put("receiptNumber", paymentRecord.getReceiptNumber());
        result.put("paymentAmount", paymentRecord.getPaymentAmount());
        result.put("paymentDate", paymentRecord.getPaymentDate());
        result.put("paymentMethod", paymentRecord.getPaymentMethod());
        result.put("billId", paymentRecord.getBillId());
        result.put("isAdvance", paymentRecord.getIsAdvance());
        result.put("isPartial", paymentRecord.getIsPartial());
        result.put("remark", paymentRecord.getRemark());

        // 添加各种费用金额
        result.put("propertyFeeAmount", paymentRecord.getPropertyFeeAmount());
        result.put("parkingFeeAmount", paymentRecord.getParkingFeeAmount());
        result.put("sanitationFeeAmount", paymentRecord.getSanitationFeeAmount());
        result.put("elevatorFeeAmount", paymentRecord.getElevatorFeeAmount());
        result.put("lateFeeAmount", paymentRecord.getLateFeeAmount());

        // 添加业主和小区信息
        if (owner != null) {
            result.put("ownerName", owner.getOwnerName());
            result.put("buildingNumber", owner.getBuildingNumber());
            result.put("houseNumber", owner.getHouseNumber());
        }
        if (community != null) {
            result.put("communityName", community.getCommunityName());
        }

        // 添加操作人信息
        result.put("operatorName", operatorName);

        // 查询收费明细
        List<Map<String, Object>> details = paymentDetailMapper.selectPaymentDetailsByPaymentId(paymentId);
        result.put("details", details);

        return result;
    }

    /**
     * 生成收费明细记录（支持无账单）
     */
    private void generatePaymentDetailsWithoutBill(PaymentRecord paymentRecord, Map<String, Object> paymentData, String paymentType) {
        try {
            if ("advance".equals(paymentType)) {
                // 预交费用，检查是否为新的多费用类型格式
                if (paymentData.containsKey("advanceFeeDetails")) {
                    // 新的多费用类型预交费用
                    generateAdvancePaymentDetails(paymentRecord, paymentData);
                } else if (paymentData.containsKey("feeType")) {
                    // 兼容旧的单费用类型预交费用
                    int feeType = Integer.parseInt(paymentData.get("feeType").toString());
                    BigDecimal amount = BigDecimal.ZERO;
                    String feeName = "";

                    if (feeType == 1) {
                        amount = paymentRecord.getPropertyFeeAmount();
                        feeName = "物业费";
                    } else if (feeType == 2) {
                        amount = paymentRecord.getParkingFeeAmount();
                        feeName = "停车费";
                    } else if (feeType == 3) {
                        amount = paymentRecord.getSanitationFeeAmount();
                        feeName = "卫生费";
                    }

                    generatePaymentDetailForFeeTypeWithoutBill(paymentRecord, feeType, amount, feeName, paymentData);
                } else {
                    throw new RuntimeException("预交费用缺少必要的费用类型信息");
                }
            } else if ("full".equals(paymentType)) {
                // 全额支付，为每种费用类型生成明细
                generateFullPaymentDetails(paymentRecord, paymentData);
            } else if ("mixed".equals(paymentType)) {
                // 混合支付，生成多种费用类型的明细
                generateMixedPaymentDetails(paymentRecord, paymentData);
            } else {
                throw new RuntimeException("不支持的支付类型: " + paymentType);
            }
        } catch (Exception e) {
            System.err.println("生成收费明细失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成预交费用的收费明细（多费用类型）
     */
    private void generateAdvancePaymentDetails(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        try {
            List<Map<String, Object>> advanceFeeDetails = (List<Map<String, Object>>) paymentData.get("advanceFeeDetails");
            if (advanceFeeDetails == null || advanceFeeDetails.isEmpty()) {
                throw new RuntimeException("预交费用明细不能为空");
            }

            Integer advanceMonths = Integer.valueOf(paymentData.get("advanceMonths").toString());

            for (Map<String, Object> feeDetail : advanceFeeDetails) {
                PaymentDetail detail = new PaymentDetail();
                detail.setPaymentId(paymentRecord.getId());

                Integer feeType = Integer.valueOf(feeDetail.get("feeType").toString());
                BigDecimal totalAmount = new BigDecimal(feeDetail.get("totalAmount").toString());
                String feeTypeName = feeDetail.get("feeTypeName").toString();

                detail.setFeeType(feeType);
                detail.setFeeName(feeTypeName);
                detail.setPaymentAmount(totalAmount);
                detail.setPaymentMonths(Long.valueOf(advanceMonths));
                detail.setIsAdvance(1);

                // 如果是停车费，设置车牌号
                if (feeType == 2 && feeDetail.get("plateNumber") != null) {
                    detail.setPlateNumber(feeDetail.get("plateNumber").toString());
                }

                // 计算缴费周期
                calculatePaymentPeriod(detail, paymentRecord.getOwnerId(), feeType, advanceMonths,
                                     feeDetail.get("plateNumber") != null ? feeDetail.get("plateNumber").toString() : null);

                paymentDetailMapper.insertPaymentDetail(detail);
            }
        } catch (Exception e) {
            System.err.println("生成预交费用明细失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成预交费用明细失败: " + e.getMessage());
        }
    }

    /**
     * 生成混合支付的收费明细
     */
    private void generateMixedPaymentDetails(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        try {
            List<Map<String, Object>> mixedFeeDetails = (List<Map<String, Object>>) paymentData.get("mixedFeeDetails");
            if (mixedFeeDetails == null || mixedFeeDetails.isEmpty()) {
                throw new RuntimeException("混合支付费用明细不能为空");
            }

            for (Map<String, Object> feeDetail : mixedFeeDetails) {
                Integer feeType = Integer.valueOf(feeDetail.get("feeType").toString());
                BigDecimal totalAmount = new BigDecimal(feeDetail.get("totalAmount").toString());
                String feeTypeName = feeDetail.get("feeTypeName").toString();
                Integer months = Integer.valueOf(feeDetail.get("months").toString());
                String startDate = feeDetail.get("startDate").toString();
                String endDate = feeDetail.get("endDate").toString();

                // 创建收费明细
                PaymentDetail detail = new PaymentDetail();
                detail.setPaymentId(paymentRecord.getId());
                detail.setFeeType(feeType);
                detail.setFeeName(feeTypeName);
                detail.setPaymentAmount(totalAmount);
                detail.setPaymentMonths(Long.valueOf(months));
                detail.setPeriodStart(java.sql.Date.valueOf(LocalDate.parse(startDate)));
                detail.setPeriodEnd(java.sql.Date.valueOf(LocalDate.parse(endDate)));

                // 如果是停车费，设置车牌号（从费用明细中获取）
                if (feeType == 2 && feeDetail.get("plateNumber") != null) {
                    detail.setPlateNumber(feeDetail.get("plateNumber").toString());
                }

                paymentDetailMapper.insertPaymentDetail(detail);

                // 更新收费记录中对应的费用金额
                updatePaymentRecordFeeAmount(paymentRecord, feeType, totalAmount);

                // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
                // updateFeeEndDatesForMixedPayment(paymentRecord.getOwnerId(), feeType, detail, feeDetail);
            }
        } catch (Exception e) {
            System.err.println("生成混合支付收费明细失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("生成混合支付收费明细失败: " + e.getMessage());
        }
    }

    /**
     * 更新收费记录中对应费用类型的金额
     */
    private void updatePaymentRecordFeeAmount(PaymentRecord paymentRecord, Integer feeType, BigDecimal amount) {
        switch (feeType) {
            case 1: // 物业费
                paymentRecord.setPropertyFeeAmount(amount);
                break;
            case 2: // 停车费
                paymentRecord.setParkingFeeAmount(amount);
                break;
            case 3: // 卫生费
                paymentRecord.setSanitationFeeAmount(amount);
                break;
            case 4: // 电梯费
                paymentRecord.setElevatorFeeAmount(amount);
                break;
            case 5: // 滞纳金
                paymentRecord.setLateFeeAmount(amount);
                break;
        }
    }

    /**
     * 生成全额支付的收费明细
     */
    private void generateFullPaymentDetails(PaymentRecord paymentRecord, Map<String, Object> paymentData) {
        try {
            // 为每种有金额的费用类型生成明细
            if (paymentRecord.getPropertyFeeAmount() != null && paymentRecord.getPropertyFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                generatePaymentDetailForBillPayment(paymentRecord, 1, paymentRecord.getPropertyFeeAmount(), "物业费", paymentData);
            }

            if (paymentRecord.getParkingFeeAmount() != null && paymentRecord.getParkingFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 停车费需要特殊处理：为每辆车生成单独的收费明细
                generateParkingFeeDetailsForFullPayment(paymentRecord, paymentRecord.getParkingFeeAmount(), paymentData);
            }

            if (paymentRecord.getSanitationFeeAmount() != null && paymentRecord.getSanitationFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                generatePaymentDetailForBillPayment(paymentRecord, 3, paymentRecord.getSanitationFeeAmount(), "卫生费", paymentData);
            }

            if (paymentRecord.getElevatorFeeAmount() != null && paymentRecord.getElevatorFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
                generatePaymentDetailForBillPayment(paymentRecord, 4, paymentRecord.getElevatorFeeAmount(), "电梯费", paymentData);
            }
        } catch (Exception e) {
            System.err.println("生成全额支付收费明细失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 为账单支付生成收费明细（包含到期时间更新）
     * @param paymentRecord
     * @param feeType
     * @param amount
     * @param feeName
     * @param paymentData
     */
    private void generatePaymentDetailForBillPayment(PaymentRecord paymentRecord, Integer feeType, BigDecimal amount, String feeName, Map<String, Object> paymentData) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        PaymentDetail detail = new PaymentDetail();
        detail.setPaymentId(paymentRecord.getId());
        detail.setFeeType(feeType);
        detail.setFeeName(feeName);
        detail.setPaymentAmount(amount);
        detail.setIsAdvance(paymentRecord.getIsAdvance());
        detail.setCreateTime(DateUtils.getNowDate());

        // 如果是停车费，设置车牌号
        if (feeType == 2 && paymentData != null && paymentData.get("plateNumber") != null) {
            detail.setPlateNumber(paymentData.get("plateNumber").toString());
        }

        // 计算支付的月数和时间范围
        calculatePaymentPeriodForBillPayment(paymentRecord, detail, feeType, amount, paymentData);

        paymentDetailMapper.insertPaymentDetail(detail);

    }

    /**
     * 为特定费用类型生成收费明细（无账单）
     */
    private void generatePaymentDetailForFeeTypeWithoutBill(PaymentRecord paymentRecord, Integer feeType, BigDecimal amount, String feeName, Map<String, Object> paymentData) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        PaymentDetail detail = new PaymentDetail();
        detail.setPaymentId(paymentRecord.getId());
        detail.setFeeType(feeType);
        detail.setFeeName(feeName);
        detail.setPaymentAmount(amount);
        detail.setIsAdvance(paymentRecord.getIsAdvance());
        detail.setCreateTime(DateUtils.getNowDate());

        // 预交费用：从当前费用到期日期的下一天开始计算
        int advanceMonths = Integer.parseInt(paymentData.get("advanceMonths").toString());
        detail.setPaymentMonths((long) advanceMonths);

        // 获取当前费用的到期日期
        LocalDate currentEndDate = getCurrentFeeEndDate(paymentRecord.getOwnerId(), feeType, paymentData);
        LocalDate periodStart = currentEndDate.plusDays(1);
        LocalDate periodEnd = periodStart.plusMonths(advanceMonths).minusDays(1);

        detail.setPeriodStart(java.sql.Date.valueOf(periodStart));
        detail.setPeriodEnd(java.sql.Date.valueOf(periodEnd));

        // 如果是停车费，设置车牌号
        if (feeType == 2 && paymentData.get("plateNumber") != null) {
            detail.setPlateNumber(paymentData.get("plateNumber").toString());
        }

        paymentDetailMapper.insertPaymentDetail(detail);

        // 注释：收费时不再直接更新到期时间，改为在审核通过后更新
        // updateFeeEndDates(paymentRecord.getOwnerId(), feeType, detail, paymentData);
    }

    /**
     * 计算账单支付的时间范围和月数
     */
    private void calculatePaymentPeriodForBillPayment(PaymentRecord paymentRecord, PaymentDetail detail, Integer feeType, BigDecimal amount, Map<String, Object> paymentData) {
        try {
            // 检查是否为部分支付
            if (paymentRecord.getIsPartial() != null && paymentRecord.getIsPartial() == 1) {
                // 部分支付：使用前端传递的月数和从账单周期开始的逻辑
                Integer paymentMonths = Integer.valueOf(paymentData.get("paymentMonths").toString());
                detail.setPaymentMonths(Long.valueOf(paymentMonths));

                // 获取缴费周期详细信息
                String plateNumber = paymentData.get("plateNumber") != null ? paymentData.get("plateNumber").toString() : null;
                Map<String, Object> feeDetails = calculatePartialFeeDetails(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), paymentRecord.getBillId(), feeType, paymentMonths, plateNumber);

                if (!feeDetails.containsKey("error")) {
                    String startDateStr = feeDetails.get("startDate").toString();
                    String endDateStr = feeDetails.get("endDate").toString();

                    LocalDate periodStart = LocalDate.parse(startDateStr);
                    LocalDate periodEnd = LocalDate.parse(endDateStr);

                    detail.setPeriodStart(java.sql.Date.valueOf(periodStart));
                    detail.setPeriodEnd(java.sql.Date.valueOf(periodEnd));
                } else {
                    // 如果计算失败，使用默认逻辑
                    useBillBasedPeriodCalculation(paymentRecord, detail, feeType, paymentData, paymentMonths);
                }
            } else {
                // 全额支付：使用基于账单的周期计算逻辑
                useBillBasedPeriodCalculation(paymentRecord, detail, feeType, paymentData, null);
            }

        } catch (Exception e) {
            System.err.println("计算账单支付时间范围失败: " + e.getMessage());
            e.printStackTrace();
            // 设置默认值
            detail.setPaymentMonths(1L);
            LocalDate now = LocalDate.now();
            detail.setPeriodStart(java.sql.Date.valueOf(now));
            detail.setPeriodEnd(java.sql.Date.valueOf(now.plusMonths(1).minusDays(1)));
        }
    }

    /**
     * 使用基于账单的缴费周期计算逻辑
     */
    private void useBillBasedPeriodCalculation(PaymentRecord paymentRecord, PaymentDetail detail, Integer feeType, Map<String, Object> paymentData, Integer specifiedMonths) {
        try {
            // 获取账单信息
            CommunityBill bill = communityBillMapper.selectCommunityBillById(paymentRecord.getBillId());
            if (bill == null) {
                throw new RuntimeException("账单不存在");
            }

            // 账单的周期时间
            LocalDate billStartDate = bill.getBillPeriodStart().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            LocalDate billEndDate = bill.getBillPeriodEnd().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

            // 确定缴费周期开始时间：优先从最后的缴费记录开始，否则从账单周期开始时间算起
            LocalDate paymentStartDate = getLastPaymentEndDateForBillAndFeeType(paymentRecord.getBillId(), feeType);
            if (paymentStartDate == null) {
                // 没有历史缴费记录，从账单周期开始时间算起
                paymentStartDate = billStartDate;
            } else {
                // 有历史缴费记录，从最后缴费结束时间的下一天开始
                paymentStartDate = paymentStartDate.plusDays(1);
            }

            LocalDate paymentEndDate;

            // 检查是否为全额支付（非部分支付且支付金额等于账单剩余金额）
            if (isFullPaymentForBill(paymentRecord, bill, feeType, detail.getPaymentAmount())) {
                // 全额支付：缴费到账单结束时间
                paymentEndDate = billEndDate;

                // 计算实际缴费月数（基于时间周期）
                TimeCalculationResult timeResult = feeCalculationService.calculateTimeDifference(paymentStartDate, paymentEndDate);
                detail.setPaymentMonths((long) timeResult.getFullMonths());

            } else {
                // 部分支付或其他情况：按月数计算
                Integer paymentMonths = specifiedMonths;
                if (paymentMonths == null) {
                    paymentMonths = calculateMonthsFromAmount(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), feeType, detail.getPaymentAmount(), paymentData);
                }
                detail.setPaymentMonths(Long.valueOf(paymentMonths));

                // 计算缴费结束时间，但不能超过账单结束时间
                LocalDate calculatedEndDate = feeCalculationService.calculateEndDateByMonths(paymentStartDate, paymentMonths);
                paymentEndDate = calculatedEndDate.isAfter(billEndDate) ? billEndDate : calculatedEndDate;
            }

            detail.setPeriodStart(java.sql.Date.valueOf(paymentStartDate));
            detail.setPeriodEnd(java.sql.Date.valueOf(paymentEndDate));

        } catch (Exception e) {
            System.err.println("基于账单的缴费周期计算失败: " + e.getMessage());
            e.printStackTrace();
            // 降级到默认逻辑
            useDefaultPeriodCalculation(paymentRecord, detail, feeType, paymentData, specifiedMonths);
        }
    }

    /**
     * 使用默认的缴费周期计算逻辑（用于预交费等非账单支付）
     */
    private void useDefaultPeriodCalculation(PaymentRecord paymentRecord, PaymentDetail detail, Integer feeType, Map<String, Object> paymentData, Integer specifiedMonths) {
        // 获取当前费用的到期日期
        LocalDate currentEndDate = getCurrentFeeEndDate(paymentRecord.getOwnerId(), feeType, paymentData);

        // 计算支付的月数
        Integer paymentMonths = specifiedMonths;
        if (paymentMonths == null) {
            paymentMonths = calculateMonthsFromAmount(paymentRecord.getCommunityId(), paymentRecord.getOwnerId(), feeType, detail.getPaymentAmount(), paymentData);
        }

        detail.setPaymentMonths(Long.valueOf(paymentMonths));

        // 设置支付期间
        LocalDate periodStart = currentEndDate.plusDays(1);
        LocalDate periodEnd = periodStart.plusMonths(paymentMonths).minusDays(1);

        detail.setPeriodStart(java.sql.Date.valueOf(periodStart));
        detail.setPeriodEnd(java.sql.Date.valueOf(periodEnd));
    }

    /**
     * 判断是否为账单的全额支付
     *
     * @param paymentRecord 支付记录
     * @param bill          账单
     * @param feeType       费用类型
     * @param paymentAmount 支付金额
     * @return 是否为全额支付
     */
    private boolean isFullPaymentForBill(PaymentRecord paymentRecord, CommunityBill bill, Integer feeType, BigDecimal paymentAmount) {
        try {
            // 获取账单中对应费用类型的金额
            BigDecimal billFeeAmount = getBillFeeAmountByType(bill, feeType);
            if (billFeeAmount == null || billFeeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }

            // 获取该费用类型已支付的金额
            BigDecimal paidAmount = getPaidAmountForBillAndFeeType(paymentRecord.getBillId(), feeType);

            // 计算剩余未支付金额
            BigDecimal remainingAmount = billFeeAmount.subtract(paidAmount);

            // 判断当前支付金额是否等于剩余金额（允许小的误差）
            BigDecimal difference = paymentAmount.subtract(remainingAmount).abs();
            return difference.compareTo(new BigDecimal("0.01")) <= 0;

        } catch (Exception e) {
            System.err.println("判断是否全额支付失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据费用类型获取账单中的费用金额
     */
    private BigDecimal getBillFeeAmountByType(CommunityBill bill, Integer feeType) {
        switch (feeType) {
            case 1: // 物业费
                return bill.getPropertyFee();
            case 2: // 停车费
                return bill.getParkingFee();
            case 3: // 卫生费
                return bill.getSanitationFee();
            case 4: // 电梯费
                return bill.getElevatorFee();

            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取指定账单和费用类型的已支付金额
     */
    private BigDecimal getPaidAmountForBillAndFeeType(Long billId, Integer feeType) {
        try {
            // 查询该账单相关的所有缴费记录
            PaymentRecord queryRecord = new PaymentRecord();
            queryRecord.setBillId(billId);
            List<PaymentRecord> paymentRecords = paymentRecordMapper.selectPaymentRecordList(queryRecord);

            BigDecimal totalPaidAmount = BigDecimal.ZERO;

            // 遍历所有缴费记录，累计对应费用类型的已支付金额
            for (PaymentRecord record : paymentRecords) {
                PaymentDetail queryDetail = new PaymentDetail();
                queryDetail.setPaymentId(record.getId());
                queryDetail.setFeeType(feeType);

                List<PaymentDetail> details = paymentDetailMapper.selectPaymentDetailList(queryDetail);

                for (PaymentDetail detail : details) {
                    if (detail.getPaymentAmount() != null) {
                        totalPaidAmount = totalPaidAmount.add(detail.getPaymentAmount());
                    }
                }
            }

            return totalPaidAmount;
        } catch (Exception e) {
            System.err.println("获取已支付金额失败: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据支付金额计算支付月数
     * 使用二分查找法找到最接近的月数，确保计算结果与前端显示一致
     */
    private Integer calculateMonthsFromAmount(Long communityId, Long ownerId, Integer feeType, BigDecimal amount, Map<String, Object> paymentData) {
        try {
            // 使用二分查找法找到最接近的月数
            int minMonths = 1;
            int maxMonths = 24; // 最大查找24个月
            int bestMonths = 1;
            BigDecimal bestDifference = amount; // 初始化为最大差值

            for (int months = minMonths; months <= maxMonths; months++) {
                // 使用与前端相同的计算逻辑
                BigDecimal calculatedAmount = calculateAdvanceFeeAmountFromCommunity(communityId, ownerId, feeType, months);

                if (calculatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // 计算差值
                BigDecimal difference = amount.subtract(calculatedAmount).abs();

                // 如果找到完全匹配的金额，直接返回
                if (calculatedAmount.compareTo(amount) == 0) {
                    return months;
                }

                // 如果当前月数的计算金额更接近目标金额，更新最佳月数
                if (difference.compareTo(bestDifference) < 0) {
                    bestDifference = difference;
                    bestMonths = months;
                }

                // 如果计算金额已经超过目标金额，可以停止查找
                if (calculatedAmount.compareTo(amount) > 0) {
                    break;
                }
            }

            return bestMonths;
        } catch (Exception e) {
            System.err.println("根据金额计算月数失败: " + e.getMessage());
            e.printStackTrace();
            return 1;
        }
    }

    /**
     * 获取当前费用的到期日期
     */
    private LocalDate getCurrentFeeEndDate(Long ownerId, Integer feeType, Map<String, Object> paymentData) {
        LocalDate currentEndDate = LocalDate.now();

        try {
            if (feeType == 1) {
                // 物业费
                OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);
                if (owner != null && owner.getCommunityPriceEnddate() != null) {
                    currentEndDate = owner.getCommunityPriceEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
            } else if (feeType == 2) {
                // 停车费
                String plateNumber = paymentData != null ? (String) paymentData.get("plateNumber") : null;
                ParkingInfo queryParking = new ParkingInfo();
                queryParking.setOwnerId(ownerId);
                if (plateNumber != null && !plateNumber.isEmpty()) {
                    queryParking.setPlateNumber(plateNumber);
                }
                queryParking.setStatus(1);

                List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);
                if (!parkingList.isEmpty() && parkingList.get(0).getEndDate() != null) {
                    currentEndDate = parkingList.get(0).getEndDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
            } else if (feeType == 3) {
                // 卫生费
                OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);
                if (owner != null && owner.getPublicSanitationFeeEnddate() != null) {
                    currentEndDate = owner.getPublicSanitationFeeEnddate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
            }
        } catch (Exception e) {
            System.err.println("获取费用到期日期失败: " + e.getMessage());
            e.printStackTrace();
        }

        return currentEndDate;
    }

    /**
     * 获取业主某个费用类型的最后缴费记录结束时间（用于混合支付）
     *
     * @param ownerId     业主ID
     * @param feeType     费用类型
     * @param plateNumber 车牌号（停车费时需要）
     * @return 最后缴费记录结束时间，如果没有记录则返回账单周期开始时间的前一天
     */
    private LocalDate getLastPaymentEndDateForOwnerAndFeeType(Long ownerId, Integer feeType, String plateNumber) {
        try {
            // 首先查询收费明细中的最后缴费记录结束时间
            java.util.Date lastPaymentEndDate = paymentDetailMapper.selectLastPaymentEndDateByOwnerAndFeeType(ownerId, feeType, plateNumber);

            if (lastPaymentEndDate != null) {
                // 如果找到了缴费记录，返回该记录的结束时间
                return lastPaymentEndDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            } else {
                // 如果没有找到缴费记录，则查找业主最新账单的周期开始时间
                LocalDate billStartDate = getLatestBillStartDateForOwner(ownerId);
                if (billStartDate != null) {
                    // 返回账单周期开始时间的前一天，这样 plusDays(1) 后就是账单周期开始时间
                    return billStartDate.minusDays(1);
                } else {
                    // 如果没有账单，则使用当前费用到期时间
                    Map<String, Object> paymentData = new HashMap<>();
                    if (plateNumber != null) {
                        paymentData.put("plateNumber", plateNumber);
                    }
                    return getCurrentFeeEndDate(ownerId, feeType, paymentData);
                }
            }
        } catch (Exception e) {
            System.err.println("获取业主费用类型最后缴费记录结束时间失败: " + e.getMessage());
            e.printStackTrace();
            // 出错时降级到当前费用到期时间
            Map<String, Object> paymentData = new HashMap<>();
            if (plateNumber != null) {
                paymentData.put("plateNumber", plateNumber);
            }
            return getCurrentFeeEndDate(ownerId, feeType, paymentData);
        }
    }

    /**
     * 获取业主最新账单的周期开始时间
     *
     * @param ownerId 业主ID
     * @return 最新账单的周期开始时间，如果没有账单则返回null
     */
    private LocalDate getLatestBillStartDateForOwner(Long ownerId) {
        try {
            // 查询业主最新的账单
            CommunityBill queryBill = new CommunityBill();
            queryBill.setOwnerId(ownerId);
            List<CommunityBill> bills = communityBillMapper.selectCommunityBillList(queryBill);

            if (!bills.isEmpty()) {
                // 按账单周期结束时间降序排序，获取最新的账单
                CommunityBill latestBill = bills.stream()
                        .filter(bill -> bill.getBillPeriodStart() != null)
                        .max((b1, b2) -> {
                            if (b1.getBillPeriodEnd() == null && b2.getBillPeriodEnd() == null) return 0;
                            if (b1.getBillPeriodEnd() == null) return -1;
                            if (b2.getBillPeriodEnd() == null) return 1;
                            return b1.getBillPeriodEnd().compareTo(b2.getBillPeriodEnd());
                        })
                        .orElse(null);

                if (latestBill != null && latestBill.getBillPeriodStart() != null) {
                    return latestBill.getBillPeriodStart().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                }
            }

            return null;
        } catch (Exception e) {
            System.err.println("获取业主最新账单周期开始时间失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 更新业主费用到期时间到指定日期（用于账单支付）
     */
    private void updateOwnerFeeEndDateToBillPayment(Long ownerId, int feeType, LocalDate newEndDate) {
        try {
            OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);
            if (owner != null) {
                if (feeType == 1) { // 物业费
                    owner.setCommunityPriceEnddate(java.sql.Date.valueOf(newEndDate));
                } else if (feeType == 3) { // 卫生费
                    owner.setPublicSanitationFeeEnddate(java.sql.Date.valueOf(newEndDate));
                }

                ownerMangementMapper.updateOwnerMangement(owner);
                System.out.println("更新业主费用到期时间 - 业主ID: " + ownerId + ", 费用类型: " + feeType + ", 新到期时间: " + newEndDate);
            }
        } catch (Exception e) {
            System.err.println("更新业主费用到期时间到指定日期失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新停车费到期时间到指定日期（用于账单支付）
     */
    private void updateParkingFeeEndDateToBillPayment(Long ownerId, String plateNumber, LocalDate newEndDate) {
        try {
            System.out.println("开始更新停车费到期时间 - 业主ID: " + ownerId + ", 车牌号: " + plateNumber + ", 新到期时间: " + newEndDate);

            ParkingInfo queryParking = new ParkingInfo();
            queryParking.setOwnerId(ownerId);
            if (plateNumber != null && !plateNumber.isEmpty()) {
                queryParking.setPlateNumber(plateNumber);
            }
            queryParking.setStatus(1); // 有效状态

            List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);
            System.out.println("查询到停车记录数量: " + parkingList.size());

            if (parkingList.isEmpty()) {
                System.out.println("未找到匹配的停车记录 - 业主ID: " + ownerId + ", 车牌号: " + plateNumber);
                return;
            }

            for (ParkingInfo parking : parkingList) {
                System.out.println("更新停车记录 - ID: " + parking.getId() + ", 原到期时间: " + parking.getEndDate());

                parking.setEndDate(java.sql.Date.valueOf(newEndDate));
                parking.setUpdateTime(DateUtils.getNowDate());

                int updateResult = parkingInfoMapper.updateParkingInfo(parking);

                if (updateResult > 0) {
                    System.out.println("停车费到期时间更新成功 - 停车记录ID: " + parking.getId() + ", 业主ID: " + ownerId + ", 车牌号: " + plateNumber + ", 新到期时间: " + newEndDate);
                } else {
                    System.err.println("停车费到期时间更新失败 - 停车记录ID: " + parking.getId());
                }
            }
        } catch (Exception e) {
            System.err.println("更新停车费到期时间到指定日期失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 计算部分支付费用金额
     *
     * @param communityId 小区ID
     * @param ownerId     业主ID
     * @param billId      账单ID（可选）
     * @param feeType     费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months      支付月数
     * @param plateNumber 车牌号（停车费时需要）
     * @return 部分支付费用金额
     */
    @Override
    public BigDecimal calculatePartialFeeAmount(Long communityId, Long ownerId, Long billId, Integer feeType, Integer months, String plateNumber) {
        try {
            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
            // 获取业主信息
            OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);

            if (community == null || owner == null) {
                System.out.println("部分支付计算错误: 小区或业主信息为空");
                return BigDecimal.ZERO;
            }

            BigDecimal monthlyFee = BigDecimal.ZERO;

            if (feeType == 1) { // 物业费
                // 物业费 = 单价 × 面积
                if (community.getCommunityPrice() != null && owner.getHouseArea() != null) {
                    monthlyFee = community.getCommunityPrice().multiply(owner.getHouseArea());
                }
            } else if (feeType == 2) { // 停车费
                // 停车费：需要根据车牌号查询停车位的三证合一状态
                if (plateNumber != null && !plateNumber.isEmpty()) {
                    ParkingInfo queryParking = new ParkingInfo();
                    queryParking.setOwnerId(ownerId);
                    queryParking.setPlateNumber(plateNumber);
                    queryParking.setStatus(1);

                    List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);
                    if (!parkingList.isEmpty()) {
                        ParkingInfo parking = parkingList.get(0);
                        if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                            // 三证合一停车费
                            monthlyFee = community.getOwnerParkingFee();
                        } else {
                            // 非三证合一停车费
                            monthlyFee = community.getTenantParkingFee();
                        }
                    }
                } else if (billId != null) {
                    // 如果没有指定车牌号但有账单ID，从账单估算
                    CommunityBill bill = communityBillMapper.selectCommunityBillById(billId);
                    if (bill != null) {
                        monthlyFee = getMonthlyParkingFeeFromBill(bill);
                    }
                }
            } else if (feeType == 3) { // 卫生费
                // 卫生费：根据房屋面积确定
                System.out.println("=== 卫生费计算调试信息 ===");
                System.out.println("业主ID: " + ownerId);
                System.out.println("小区ID: " + communityId);
                System.out.println("房屋面积: " + (owner.getHouseArea() != null ? owner.getHouseArea() : "null"));

                if (owner.getHouseArea() != null) {
                    monthlyFee = getSanitationFeeRate(community, owner.getHouseArea());
                    System.out.println("计算得到的月费用: " + monthlyFee);
                } else {
                    System.out.println("房屋面积为null，无法计算卫生费");
                }
                System.out.println("=== 卫生费计算调试信息结束 ===");
            }

            // 计算指定月数的费用
            BigDecimal result = monthlyFee.multiply(new BigDecimal(months));

            // 保留2位小数，舍去分以下位数
            return result.setScale(0, RoundingMode.DOWN);
        } catch (Exception e) {
            System.err.println("计算部分支付费用失败: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算部分支付的详细信息（包括缴费周期）
     *
     * @param communityId 小区ID
     * @param ownerId     业主ID
     * @param billId      账单ID（必填）
     * @param feeType     费用类型（1-物业费，2-停车费，3-卫生费）
     * @param months      支付月数
     * @param plateNumber 车牌号（停车费时需要）
     * @return 包含金额、开始时间、结束时间的Map
     */
    @Override
    public Map<String, Object> calculatePartialFeeDetails(Long communityId, Long ownerId, Long billId, Integer feeType, Integer months, String plateNumber) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取账单信息
            CommunityBill bill = communityBillMapper.selectCommunityBillById(billId);
            if (bill == null) {
                result.put("error", "账单不存在");
                return result;
            }

            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
            // 获取业主信息
            OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);

            if (community == null || owner == null) {
                result.put("error", "小区或业主信息不存在");
                return result;
            }

            // 计算月费用
            BigDecimal monthlyFee = BigDecimal.ZERO;
            if (feeType == 1) { // 物业费
                if (community.getCommunityPrice() != null && owner.getHouseArea() != null) {
                    monthlyFee = community.getCommunityPrice().multiply(owner.getHouseArea());
                }
            } else if (feeType == 2) { // 停车费
                if (plateNumber != null && !plateNumber.isEmpty()) {
                    ParkingInfo queryParking = new ParkingInfo();
                    queryParking.setOwnerId(ownerId);
                    queryParking.setPlateNumber(plateNumber);
                    queryParking.setStatus(1);

                    List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);
                    if (!parkingList.isEmpty()) {
                        ParkingInfo parking = parkingList.get(0);
                        if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                            monthlyFee = community.getOwnerParkingFee();
                        } else {
                            monthlyFee = community.getTenantParkingFee();
                        }
                    }
                } else {
                    monthlyFee = getMonthlyParkingFeeFromBill(bill);
                }
            } else if (feeType == 3) { // 卫生费
                if (owner.getHouseArea() != null) {
                    monthlyFee = getSanitationFeeRate(community, owner.getHouseArea());
                }
            }

            // 确定缴费周期开始时间：优先从最后的缴费记录开始，否则从账单周期开始时间算起
            LocalDate paymentStartDate = getLastPaymentEndDateForBillAndFeeType(billId, feeType);
            if (paymentStartDate == null) {
                // 没有历史缴费记录，从账单周期开始时间算起
                paymentStartDate = bill.getBillPeriodStart().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            } else {
                // 有历史缴费记录，从最后缴费结束时间的下一天开始
                paymentStartDate = paymentStartDate.plusDays(1);
            }

            // 计算缴费结束时间（从开始时间算起N个月）
            LocalDate paymentEndDate = feeCalculationService.calculateEndDateByMonths(paymentStartDate, months);

            // 计算总费用
            BigDecimal totalFee = monthlyFee.multiply(new BigDecimal(months));
            totalFee = totalFee.setScale(0, RoundingMode.DOWN);

            // 返回结果
            result.put("amount", totalFee);
            result.put("startDate", paymentStartDate.toString());
            result.put("endDate", paymentEndDate.toString());
            result.put("monthlyFee", monthlyFee);
            result.put("months", months);
            result.put("description", String.format("从%s开始缴费%d个月，至%s", paymentStartDate, months, paymentEndDate));

        } catch (Exception e) {
            System.err.println("计算部分支付详细信息失败: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "计算失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取指定账单和费用类型的最后缴费结束时间（用于账单支付）
     *
     * @param billId  账单ID
     * @param feeType 费用类型
     * @return 最后缴费结束时间，如果没有记录则返回null
     */
    private LocalDate getLastPaymentEndDateForBillAndFeeType(Long billId, Integer feeType) {
        try {
            // 查询该账单相关的所有缴费记录
            PaymentRecord queryRecord = new PaymentRecord();
            queryRecord.setBillId(billId);
            List<PaymentRecord> paymentRecords = paymentRecordMapper.selectPaymentRecordList(queryRecord);

            LocalDate lastEndDate = null;

            // 遍历所有缴费记录，查找对应费用类型的最后缴费结束时间
            for (PaymentRecord record : paymentRecords) {
                PaymentDetail queryDetail = new PaymentDetail();
                queryDetail.setPaymentId(record.getId());
                queryDetail.setFeeType(feeType);

                List<PaymentDetail> details = paymentDetailMapper.selectPaymentDetailList(queryDetail);

                for (PaymentDetail detail : details) {
                    if (detail.getPeriodEnd() != null) {
                        LocalDate endDate = detail.getPeriodEnd().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

                        if (lastEndDate == null || endDate.isAfter(lastEndDate)) {
                            lastEndDate = endDate;
                        }
                    }
                }
            }

            return lastEndDate;
        } catch (Exception e) {
            System.err.println("获取最后缴费结束时间失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 计算混合支付金额
     *
     * @param params 计算参数
     * @return 计算结果
     */
    @Override
    public Map<String, Object> calculateMixedPaymentAmount(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();

        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            Long ownerId = Long.valueOf(params.get("ownerId").toString());
            Integer months = Integer.valueOf(params.get("months").toString());
            List<String> feeTypes = (List<String>) params.get("feeTypes");
            String plateNumber = params.get("plateNumber") != null ? params.get("plateNumber").toString() : null;
            List<String> selectedParkingSpaces = (List<String>) params.get("selectedParkingSpaces");

            if (feeTypes == null || feeTypes.isEmpty()) {
                result.put("error", "请选择费用类型");
                return result;
            }

            // 获取小区信息
            CommunityMangement community = communityMangementMapper.selectCommunityMangementById(communityId);
            if (community == null) {
                result.put("error", "小区信息不存在");
                return result;
            }

            // 获取业主信息
            OwnerMangement owner = ownerMangementMapper.selectOwnerMangementById(ownerId);
            if (owner == null) {
                result.put("error", "业主信息不存在");
                return result;
            }

            BigDecimal totalAmount = BigDecimal.ZERO;
            List<Map<String, Object>> feeDetails = new ArrayList<>();

            // 计算每种费用类型的金额
            for (String feeTypeStr : feeTypes) {
                Integer feeType = Integer.valueOf(feeTypeStr);

                // 如果是停车费且选择了多个车位，需要分别计算
                if (feeType == 2 && selectedParkingSpaces != null && !selectedParkingSpaces.isEmpty()) {
                    for (String selectedPlateNumber : selectedParkingSpaces) {
                        Map<String, Object> feeDetail = calculateSingleFeeForMixed(community, owner, feeType, months, selectedPlateNumber);

                        if (feeDetail.containsKey("error")) {
                            continue; // 跳过计算失败的费用类型
                        }

                        feeDetails.add(feeDetail);
                        BigDecimal feeAmount = (BigDecimal) feeDetail.get("totalAmount");
                        totalAmount = totalAmount.add(feeAmount);
                    }
                } else {
                    Map<String, Object> feeDetail = calculateSingleFeeForMixed(community, owner, feeType, months, plateNumber);

                    if (feeDetail.containsKey("error")) {
                        continue; // 跳过计算失败的费用类型
                    }

                    feeDetails.add(feeDetail);
                    BigDecimal feeAmount = (BigDecimal) feeDetail.get("totalAmount");
                    totalAmount = totalAmount.add(feeAmount);
                }
            }

            result.put("totalAmount", totalAmount);
            result.put("feeDetails", feeDetails);
            result.put("months", months);

        } catch (Exception e) {
            System.err.println("计算混合支付金额失败: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "计算失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 计算混合支付中单个费用类型的金额
     */
    private Map<String, Object> calculateSingleFeeForMixed(CommunityMangement community, OwnerMangement owner, Integer feeType, Integer months, String plateNumber) {
        Map<String, Object> result = new HashMap<>();

        try {
            BigDecimal monthlyFee = BigDecimal.ZERO;
            String feeTypeName = "";
            LocalDate startDate = LocalDate.now();
            LocalDate endDate;

            // 根据费用类型获取月费用和计算规则信息
            switch (feeType) {
                case 1: // 物业费
                    if (community.getCommunityPrice() != null && owner.getHouseArea() != null) {
                        monthlyFee = community.getCommunityPrice().multiply(owner.getHouseArea());
                        result.put("unitPrice", community.getCommunityPrice());
                        result.put("area", owner.getHouseArea());
                    }
                    feeTypeName = "物业费";
                    break;
                case 2: // 停车费
                    monthlyFee = getParkingFeeForOwner(community, owner.getId(), plateNumber);
                    feeTypeName = "停车费";
                    // 添加车牌号信息
                    result.put("plateNumber", plateNumber);
                    // 获取停车位信息
                    ParkingInfo parkingSpace = getParkingSpaceInfo(owner.getId(), plateNumber);
                    if (parkingSpace != null) {
                        result.put("parkingType", parkingSpace.getIsThreeCertificates());
                        result.put("spaceNumber", parkingSpace.getSpaceNumber());
                    }
                    break;
                case 3: // 卫生费
                    monthlyFee = getSanitationFeeRate(community, owner.getHouseArea());
                    feeTypeName = "卫生费";

                    // 设置卫生费计算规则信息
                    result.put("area", owner.getHouseArea());
                    String ruleDescription = SanitationFeeCalculator.getSanitationFeeRuleDescription(community, owner.getHouseArea());
                    result.put("sanitationFeeRule", ruleDescription);

                    // 判断卫生费类型
                    Integer sanitationFeeType = community.getSanitationFeeType();
                    if (sanitationFeeType != null) {
                        result.put("sanitationFeeType", sanitationFeeType);
                        if (sanitationFeeType == 1) {
                            // 固定费用
                            result.put("feeAmount", community.getFixedSanitationFee());
                        } else {
                            // 阶梯费用，从规则描述中提取范围信息
                            if (ruleDescription.contains("阶梯费用:")) {
                                String rangeInfo = ruleDescription.substring(ruleDescription.indexOf("阶梯费用:") + 5).trim();
                                result.put("areaRange", rangeInfo);
                            }
                        }
                    }
                    break;
                case 4: // 电梯费
                    monthlyFee = community.getElevatorFee() != null ? community.getElevatorFee() : BigDecimal.ZERO;
                    feeTypeName = "电梯费";
                    break;
                default:
                    result.put("error", "不支持的费用类型: " + feeType);
                    return result;
            }

            if (monthlyFee.compareTo(BigDecimal.ZERO) <= 0) {
                result.put("error", feeTypeName + "月费用未配置或为0");
                return result;
            }

            Map<String, Object> paymentData = new HashMap<>();
            if (plateNumber != null) {
                paymentData.put("plateNumber", plateNumber);
            }

            // 获取该费用类型的最后缴费结束时间，优先从收费明细中查询
            LocalDate lastEndDate = getLastPaymentEndDateForOwnerAndFeeType(owner.getId(), feeType, plateNumber);
            startDate = lastEndDate.plusDays(1);

            // 计算结束时间
            endDate = feeCalculationService.calculateEndDateByMonths(startDate, months);

            // 计算总金额
            BigDecimal totalAmount = monthlyFee.multiply(new BigDecimal(months));

            result.put("feeType", feeType);
            result.put("feeTypeName", feeTypeName);
            result.put("monthlyFee", monthlyFee);
            result.put("months", months);
            result.put("totalAmount", totalAmount);
            result.put("startDate", startDate.toString());
            result.put("endDate", endDate.toString());

        } catch (Exception e) {
            System.err.println("计算单个费用类型失败: " + e.getMessage());
            e.printStackTrace();
            result.put("error", "计算失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取业主的停车费（根据停车位信息）
     */
    private BigDecimal getParkingFeeForOwner(CommunityMangement community, Long ownerId, String plateNumber) {
        try {
            // 查询业主的停车位信息
            ParkingInfo queryParking = new ParkingInfo();
            queryParking.setOwnerId(ownerId);
            if (plateNumber != null) {
                queryParking.setPlateNumber(plateNumber);
            }
            queryParking.setStatus(1);
            List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);

            if (!parkingList.isEmpty()) {
                ParkingInfo parking = parkingList.get(0);
                // 根据三证合一状态选择费用
                if (parking.getIsThreeCertificates() != null && parking.getIsThreeCertificates() == 1) {
                    return community.getOwnerParkingFee() != null ? community.getOwnerParkingFee() : BigDecimal.ZERO;
                } else {
                    return community.getTenantParkingFee() != null ? community.getTenantParkingFee() : BigDecimal.ZERO;
                }
            } else {
                // 没有停车位信息，使用默认的业主停车费
                return community.getOwnerParkingFee() != null ? community.getOwnerParkingFee() : BigDecimal.ZERO;
            }
        } catch (Exception e) {
            System.err.println("获取停车费失败: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 审核收费记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditPaymentRecord(Long paymentId, Integer auditStatus, String auditComment, Long auditorId) {
        try {
            // 验证审核状态
            if (!PaymentAuditStatus.isApproved(auditStatus) && !PaymentAuditStatus.isRejected(auditStatus)) {
                throw new RuntimeException("无效的审核状态");
            }

            // 查询收费记录
            PaymentRecord paymentRecord = paymentRecordMapper.selectPaymentRecordById(paymentId);
            if (paymentRecord == null) {
                throw new RuntimeException("收费记录不存在");
            }

            // 检查是否已经审核过
            if (!PaymentAuditStatus.isPending(paymentRecord.getAuditStatus())) {
                throw new RuntimeException("该记录已经审核过，不能重复审核");
            }

            // 更新审核信息
            paymentRecord.setAuditStatus(auditStatus);
            paymentRecord.setAuditorId(auditorId);
            paymentRecord.setAuditTime(new Date());
            paymentRecord.setAuditComment(auditComment);

            int result = paymentRecordMapper.updatePaymentRecord(paymentRecord);

            // 如果审核通过，更新业主相关到期时间
            if (PaymentAuditStatus.isApproved(auditStatus)) {
                updateFeeEndDatesAfterAudit(paymentRecord);
            }

            return result > 0;

        } catch (Exception e) {
            System.err.println("审核收费记录失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("审核失败: " + e.getMessage());
        }
    }

    /**
     * 批量审核收费记录
     */
    @Override
    @Transactional
    public boolean batchAuditPaymentRecords(Long[] paymentIds, Integer auditStatus, String auditComment, Long auditorId) {
        try {
            if (paymentIds == null || paymentIds.length == 0) {
                throw new RuntimeException("请选择要审核的记录");
            }

            boolean allSuccess = true;
            for (Long paymentId : paymentIds) {
                try {
                    auditPaymentRecord(paymentId, auditStatus, auditComment, auditorId);
                } catch (Exception e) {
                    System.err.println("审核记录ID " + paymentId + " 失败: " + e.getMessage());
                    allSuccess = false;
                }
            }

            return allSuccess;
        } catch (Exception e) {
            System.err.println("批量审核收费记录失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("批量审核失败: " + e.getMessage());
        }
    }

    /**
     * 查询待审核的收费记录列表
     */
    @Override
    public List<PaymentRecord> selectPendingAuditPaymentRecords(PaymentRecord paymentRecord) {
        // 设置审核状态为待审核
        paymentRecord.setAuditStatus(PaymentAuditStatus.PENDING.getCode());
        return paymentRecordMapper.selectPaymentRecordList(paymentRecord);
    }

    /**
     * 获取收费记录审核统计信息
     */
    @Override
    public Map<String, Object> getPaymentAuditStatistics(Long communityId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 构建查询条件
            PaymentRecord queryRecord = new PaymentRecord();
            if (communityId != null) {
                queryRecord.setCommunityId(communityId);
            }

            // 统计各种审核状态的记录数量
            queryRecord.setAuditStatus(PaymentAuditStatus.PENDING.getCode());
            int pendingCount = paymentRecordMapper.selectPaymentRecordList(queryRecord).size();

            queryRecord.setAuditStatus(PaymentAuditStatus.APPROVED.getCode());
            int approvedCount = paymentRecordMapper.selectPaymentRecordList(queryRecord).size();

            queryRecord.setAuditStatus(PaymentAuditStatus.REJECTED.getCode());
            int rejectedCount = paymentRecordMapper.selectPaymentRecordList(queryRecord).size();

            int totalCount = pendingCount + approvedCount + rejectedCount;

            statistics.put("totalCount", totalCount);
            statistics.put("pendingCount", pendingCount);
            statistics.put("approvedCount", approvedCount);
            statistics.put("rejectedCount", rejectedCount);

            // 计算审核通过率
            if (totalCount > 0) {
                double approvalRate = (double) approvedCount / totalCount * 100;
                statistics.put("approvalRate", Math.round(approvalRate * 100.0) / 100.0);
            } else {
                statistics.put("approvalRate", 0.0);
            }

        } catch (Exception e) {
            System.err.println("获取审核统计信息失败: " + e.getMessage());
            e.printStackTrace();
            statistics.put("error", "获取统计信息失败: " + e.getMessage());
        }

        return statistics;
    }

    /**
     * 计算预交费用的缴费周期
     */
    private void calculatePaymentPeriod(PaymentDetail detail, Long ownerId, Integer feeType, Integer months, String plateNumber) {
        try {
            Date startDate = new Date(); // 预交费用从当前时间开始
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            // 计算结束时间（加上指定月数）
            calendar.add(Calendar.MONTH, months);
            Date endDate = calendar.getTime();

            detail.setPeriodStart(startDate);
            detail.setPeriodEnd(endDate);

        } catch (Exception e) {
            System.err.println("计算缴费周期失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取停车位信息
     */
    private ParkingInfo getParkingSpaceInfo(Long ownerId, String plateNumber) {
        try {
            ParkingInfo querySpace = new ParkingInfo();
            querySpace.setOwnerId(ownerId);
            querySpace.setPlateNumber(plateNumber);

            List<ParkingInfo> parkingSpaces = parkingInfoMapper.selectParkingInfoList(querySpace);
            return parkingSpaces.isEmpty() ? null : parkingSpaces.get(0);
        } catch (Exception e) {
            System.err.println("获取停车位信息失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 审核通过后更新业主相关到期时间
     */
    private void updateFeeEndDatesAfterAudit(PaymentRecord paymentRecord) {
        try {
            System.out.println("开始更新到期时间 - 收费记录ID: " + paymentRecord.getId() + ", 业主ID: " + paymentRecord.getOwnerId());

            // 查询该收费记录的所有明细
            List<Map<String, Object>> paymentDetails = paymentDetailMapper.selectPaymentDetailsByPaymentId(paymentRecord.getId());
            System.out.println("查询到收费明细数量: " + paymentDetails.size());

            for (Map<String, Object> detail : paymentDetails) {
                System.out.println("处理收费明细: " + detail);

                Integer feeType = (Integer) detail.get("fee_type");
                String plateNumber = (String) detail.get("plate_number");
                Date periodEnd = (Date) detail.get("period_end");

                System.out.println("明细信息 - 费用类型: " + feeType + ", 车牌号: " + plateNumber + ", 结束时间: " + periodEnd);

                if (feeType == 1) {
                    // 物业费：使用支付明细的结束时间作为新的到期时间
                    if (periodEnd != null) {
                        LocalDate newEndDate = convertDateToLocalDate(periodEnd);
                        System.out.println("更新物业费到期时间: " + newEndDate);
                        updateOwnerFeeEndDateToBillPayment(paymentRecord.getOwnerId(), 1, newEndDate);
                    } else {
                        System.out.println("物业费明细的结束时间为空，跳过更新");
                    }
                } else if (feeType == 2) {
                    // 停车费：使用支付明细的结束时间作为新的到期时间
                    if (periodEnd != null && plateNumber != null) {
                        LocalDate newEndDate = convertDateToLocalDate(periodEnd);
                        System.out.println("更新停车费到期时间 - 车牌: " + plateNumber + ", 新到期时间: " + newEndDate);
                        updateParkingFeeEndDateToBillPayment(paymentRecord.getOwnerId(), plateNumber, newEndDate);
                    } else {
                        System.out.println("停车费明细信息不完整 - 结束时间: " + periodEnd + ", 车牌号: " + plateNumber + ", 跳过更新");
                    }
                } else if (feeType == 3) {
                    // 卫生费：使用支付明细的结束时间作为新的到期时间
                    if (periodEnd != null) {
                        LocalDate newEndDate = convertDateToLocalDate(periodEnd);
                        System.out.println("更新卫生费到期时间: " + newEndDate);
                        updateOwnerFeeEndDateToBillPayment(paymentRecord.getOwnerId(), 3, newEndDate);
                    } else {
                        System.out.println("卫生费明细的结束时间为空，跳过更新");
                    }
                } else {
                    System.out.println("未知的费用类型: " + feeType + ", 跳过处理");
                }
            }

            System.out.println("审核通过后更新到期时间完成 - 收费记录ID: " + paymentRecord.getId() + ", 业主ID: " + paymentRecord.getOwnerId());

        } catch (Exception e) {
            System.err.println("审核通过后更新到期时间失败: " + e.getMessage());
            e.printStackTrace();
            // 不抛出异常，避免影响审核流程
        }
    }

    /**
     * 将Date转换为LocalDate的辅助方法
     */
    private LocalDate convertDateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }

        // 处理java.sql.Date和java.util.Date
        if (date instanceof java.sql.Date) {
            return ((java.sql.Date) date).toLocalDate();
        } else {
            return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        }
    }
}
