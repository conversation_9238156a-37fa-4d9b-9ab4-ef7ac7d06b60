package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.CommunityBillDetail;

/**
 * 小区账单明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface CommunityBillDetailMapper 
{
    /**
     * 查询小区账单明细
     * 
     * @param id 小区账单明细主键
     * @return 小区账单明细
     */
    public CommunityBillDetail selectCommunityBillDetailById(Long id);

    /**
     * 查询小区账单明细列表
     * 
     * @param communityBillDetail 小区账单明细
     * @return 小区账单明细集合
     */
    public List<CommunityBillDetail> selectCommunityBillDetailList(CommunityBillDetail communityBillDetail);

    /**
     * 新增小区账单明细
     * 
     * @param communityBillDetail 小区账单明细
     * @return 结果
     */
    public int insertCommunityBillDetail(CommunityBillDetail communityBillDetail);

    /**
     * 修改小区账单明细
     * 
     * @param communityBillDetail 小区账单明细
     * @return 结果
     */
    public int updateCommunityBillDetail(CommunityBillDetail communityBillDetail);

    /**
     * 删除小区账单明细
     * 
     * @param id 小区账单明细主键
     * @return 结果
     */
    public int deleteCommunityBillDetailById(Long id);

    /**
     * 批量删除小区账单明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunityBillDetailByIds(Long[] ids);
}
