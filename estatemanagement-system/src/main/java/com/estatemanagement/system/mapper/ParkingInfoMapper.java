package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.ParkingInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 停车费信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface ParkingInfoMapper 
{
    /**
     * 查询停车费信息
     * 
     * @param id 停车费信息主键
     * @return 停车费信息
     */
    public ParkingInfo selectParkingInfoById(Long id);

    /**
     * 查询停车费信息列表
     *
     * @param parkingInfo 停车费信息
     * @return 停车费信息集合
     */
    public List<ParkingInfo> selectParkingInfoList(ParkingInfo parkingInfo);

    /**
     * 查询车位管理列表（显示所有车位，包括空车位）
     *
     * @param parkingInfo 停车费信息
     * @return 停车费信息集合
     */
    public List<ParkingInfo> selectParkingManagementList(ParkingInfo parkingInfo);

    /**
     * 新增停车费信息
     * 
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    public int insertParkingInfo(ParkingInfo parkingInfo);

    /**
     * 修改停车费信息
     *
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    public int updateParkingInfo(ParkingInfo parkingInfo);

    /**
     * 强制更新停车费信息（包括null值字段）
     * 用于车位释放等需要将字段设置为null的场景
     *
     * @param parkingInfo 停车费信息
     * @return 结果
     */
    public int forceUpdateParkingInfo(ParkingInfo parkingInfo);

    /**
     * 删除停车费信息
     * 
     * @param id 停车费信息主键
     * @return 结果
     */
    public int deleteParkingInfoById(Long id);

    /**
     * 批量删除停车费信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteParkingInfoByIds(Long[] ids);

    /**
     * 根据小区ID查询有效停车记录（有车牌号和车位号的记录）
     *
     * @param communityId 小区ID
     * @return 有效停车记录集合
     */
    public List<ParkingInfo> selectValidParkingByCommunity(@Param("communityId") Long communityId);
}
