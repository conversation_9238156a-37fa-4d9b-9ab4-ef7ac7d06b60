package com.estatemanagement.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.estatemanagement.system.domain.RefundRecord;

/**
 * 退费记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RefundRecordMapper 
{
    /**
     * 查询退费记录
     * 
     * @param id 退费记录主键
     * @return 退费记录
     */
    public RefundRecord selectRefundRecordById(Long id);

    /**
     * 查询退费记录列表
     * 
     * @param refundRecord 退费记录
     * @return 退费记录集合
     */
    public List<RefundRecord> selectRefundRecordList(RefundRecord refundRecord);

    /**
     * 查询退费记录列表（包含关联信息）
     * 
     * @param refundRecord 退费记录
     * @return 退费记录集合
     */
    public List<Map<String, Object>> selectRefundRecordListWithDetails(RefundRecord refundRecord);

    /**
     * 根据原收费记录ID查询退费记录
     * 
     * @param originalPaymentId 原收费记录ID
     * @return 退费记录集合
     */
    public List<RefundRecord> selectRefundRecordsByOriginalPaymentId(@Param("originalPaymentId") Long originalPaymentId);

    /**
     * 根据退费单号查询退费记录
     * 
     * @param refundNumber 退费单号
     * @return 退费记录
     */
    public RefundRecord selectRefundRecordByNumber(@Param("refundNumber") String refundNumber);

    /**
     * 统计退费金额
     * 
     * @param params 查询参数
     * @return 统计结果
     */
    public Map<String, Object> selectRefundStatistics(Map<String, Object> params);

    /**
     * 新增退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    public int insertRefundRecord(RefundRecord refundRecord);

    /**
     * 修改退费记录
     * 
     * @param refundRecord 退费记录
     * @return 结果
     */
    public int updateRefundRecord(RefundRecord refundRecord);

    /**
     * 删除退费记录
     * 
     * @param id 退费记录主键
     * @return 结果
     */
    public int deleteRefundRecordById(Long id);

    /**
     * 批量删除退费记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRefundRecordByIds(Long[] ids);

    /**
     * 审核退费记录
     * 
     * @param id 退费记录ID
     * @param status 审核状态
     * @param auditUserId 审核人ID
     * @param auditUserName 审核人姓名
     * @param auditRemark 审核备注
     * @return 结果
     */
    public int auditRefundRecord(@Param("id") Long id, 
                                @Param("status") Integer status,
                                @Param("auditUserId") Long auditUserId,
                                @Param("auditUserName") String auditUserName,
                                @Param("auditRemark") String auditRemark);
}
