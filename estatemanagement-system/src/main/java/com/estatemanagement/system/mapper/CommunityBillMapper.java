package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.CommunityBill;

/**
 * 小区账单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface CommunityBillMapper 
{
    /**
     * 查询小区账单
     * 
     * @param id 小区账单主键
     * @return 小区账单
     */
    public CommunityBill selectCommunityBillById(Long id);

    /**
     * 查询小区账单列表
     *
     * @param communityBill 小区账单
     * @return 小区账单集合
     */
    public List<CommunityBill> selectCommunityBillList(CommunityBill communityBill);

    /**
     * 查询小区账单列表（用于修复，不使用实时计算）
     *
     * @return 小区账单集合
     */
    public List<CommunityBill> selectCommunityBillListForFix();

    /**
     * 新增小区账单
     * 
     * @param communityBill 小区账单
     * @return 结果
     */
    public int insertCommunityBill(CommunityBill communityBill);

    /**
     * 修改小区账单
     * 
     * @param communityBill 小区账单
     * @return 结果
     */
    public int updateCommunityBill(CommunityBill communityBill);

    /**
     * 删除小区账单
     * 
     * @param id 小区账单主键
     * @return 结果
     */
    public int deleteCommunityBillById(Long id);

    /**
     * 批量删除小区账单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunityBillByIds(Long[] ids);
}
