package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.CommunitySummaryBill;

/**
 * 小区汇总账单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface CommunitySummaryBillMapper 
{
    /**
     * 查询小区汇总账单
     * 
     * @param id 小区汇总账单主键
     * @return 小区汇总账单
     */
    public CommunitySummaryBill selectCommunitySummaryBillById(Long id);

    /**
     * 查询小区汇总账单列表
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 小区汇总账单集合
     */
    public List<CommunitySummaryBill> selectCommunitySummaryBillList(CommunitySummaryBill communitySummaryBill);

    /**
     * 新增小区汇总账单
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    public int insertCommunitySummaryBill(CommunitySummaryBill communitySummaryBill);

    /**
     * 修改小区汇总账单
     * 
     * @param communitySummaryBill 小区汇总账单
     * @return 结果
     */
    public int updateCommunitySummaryBill(CommunitySummaryBill communitySummaryBill);

    /**
     * 删除小区汇总账单
     * 
     * @param id 小区汇总账单主键
     * @return 结果
     */
    public int deleteCommunitySummaryBillById(Long id);

    /**
     * 批量删除小区汇总账单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommunitySummaryBillByIds(Long[] ids);
}
