package com.estatemanagement.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.estatemanagement.system.domain.PaymentDetail;

/**
 * 收费明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface PaymentDetailMapper 
{
    /**
     * 查询收费明细
     * 
     * @param id 收费明细主键
     * @return 收费明细
     */
    public PaymentDetail selectPaymentDetailById(Long id);

    /**
     * 查询收费明细列表
     * 
     * @param paymentDetail 收费明细
     * @return 收费明细集合
     */
    public List<PaymentDetail> selectPaymentDetailList(PaymentDetail paymentDetail);

    /**
     * 根据账单ID和费用类型查询收费明细
     * 
     * @param billId 账单ID
     * @param feeType 费用类型
     * @return 收费明细集合
     */
    public List<Map<String, Object>> selectPaymentDetailsByBillAndFeeType(@Param("billId") Long billId, @Param("feeType") int feeType);

    /**
     * 根据收费记录ID查询收费明细
     * 
     * @param paymentId 收费记录ID
     * @return 收费明细集合
     */
    public List<Map<String, Object>> selectPaymentDetailsByPaymentId(@Param("paymentId") Long paymentId);

    /**
     * 新增收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    public int insertPaymentDetail(PaymentDetail paymentDetail);

    /**
     * 修改收费明细
     * 
     * @param paymentDetail 收费明细
     * @return 结果
     */
    public int updatePaymentDetail(PaymentDetail paymentDetail);

    /**
     * 删除收费明细
     * 
     * @param id 收费明细主键
     * @return 结果
     */
    public int deletePaymentDetailById(Long id);

    /**
     * 批量删除收费明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentDetailByIds(Long[] ids);

    /**
     * 查询业主某个费用类型的最后缴费记录结束时间
     *
     * @param ownerId 业主ID
     * @param feeType 费用类型
     * @param plateNumber 车牌号（停车费时需要）
     * @return 最后缴费记录结束时间
     */
    public java.util.Date selectLastPaymentEndDateByOwnerAndFeeType(@Param("ownerId") Long ownerId, @Param("feeType") Integer feeType, @Param("plateNumber") String plateNumber);
}
