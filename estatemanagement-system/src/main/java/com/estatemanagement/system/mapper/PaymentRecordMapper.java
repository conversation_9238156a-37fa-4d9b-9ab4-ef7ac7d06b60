package com.estatemanagement.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import com.estatemanagement.system.domain.PaymentRecord;

/**
 * 收费记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface PaymentRecordMapper 
{
    /**
     * 查询收费记录
     * 
     * @param id 收费记录主键
     * @return 收费记录
     */
    public PaymentRecord selectPaymentRecordById(Long id);

    /**
     * 查询收费记录列表
     * 
     * @param paymentRecord 收费记录
     * @return 收费记录集合
     */
    public List<PaymentRecord> selectPaymentRecordList(PaymentRecord paymentRecord);

    /**
     * 查询业主的未完全支付账单，用于收费管理
     * 
     * @param communityId 小区ID（可选）
     * @param ownerId 业主ID（可选）
     * @return 账单列表
     */
    public List<Map<String, Object>> selectUnpaidBillsForPayment(@Param("communityId") Long communityId, @Param("ownerId") Long ownerId);

    /**
     * 查询指定账单的收费记录及明细
     * 
     * @param billId 账单ID
     * @return 收费记录及明细列表
     */
    public List<Map<String, Object>> selectPaymentRecordsByBillId(@Param("billId") Long billId);

    /**
     * 新增收费记录
     * 
     * @param paymentRecord 收费记录
     * @return 结果
     */
    public int insertPaymentRecord(PaymentRecord paymentRecord);

    /**
     * 修改收费记录
     * 
     * @param paymentRecord 收费记录
     * @return 结果
     */
    public int updatePaymentRecord(PaymentRecord paymentRecord);

    /**
     * 删除收费记录
     * 
     * @param id 收费记录主键
     * @return 结果
     */
    public int deletePaymentRecordById(Long id);

    /**
     * 批量删除收费记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePaymentRecordByIds(Long[] ids);
}
