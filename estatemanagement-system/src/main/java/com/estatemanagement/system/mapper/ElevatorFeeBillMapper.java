package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.ElevatorFeeBill;

/**
 * 电梯费Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface ElevatorFeeBillMapper 
{
    /**
     * 查询电梯费
     * 
     * @param id 电梯费主键
     * @return 电梯费
     */
    public ElevatorFeeBill selectElevatorFeeBillById(Long id);

    /**
     * 查询电梯费列表
     * 
     * @param elevatorFeeBill 电梯费
     * @return 电梯费集合
     */
    public List<ElevatorFeeBill> selectElevatorFeeBillList(ElevatorFeeBill elevatorFeeBill);

    /**
     * 新增电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    public int insertElevatorFeeBill(ElevatorFeeBill elevatorFeeBill);

    /**
     * 修改电梯费
     * 
     * @param elevatorFeeBill 电梯费
     * @return 结果
     */
    public int updateElevatorFeeBill(ElevatorFeeBill elevatorFeeBill);

    /**
     * 删除电梯费
     * 
     * @param id 电梯费主键
     * @return 结果
     */
    public int deleteElevatorFeeBillById(Long id);

    /**
     * 批量删除电梯费
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteElevatorFeeBillByIds(Long[] ids);
}
