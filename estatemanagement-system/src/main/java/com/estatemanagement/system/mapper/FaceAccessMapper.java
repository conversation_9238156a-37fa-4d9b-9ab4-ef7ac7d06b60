package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.FaceAccess;

/**
 * 人脸识别开通记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface FaceAccessMapper 
{
    /**
     * 查询人脸识别开通记录
     * 
     * @param id 人脸识别开通记录主键
     * @return 人脸识别开通记录
     */
    public FaceAccess selectFaceAccessById(Long id);

    /**
     * 查询人脸识别开通记录列表
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 人脸识别开通记录集合
     */
    public List<FaceAccess> selectFaceAccessList(FaceAccess faceAccess);

    /**
     * 新增人脸识别开通记录
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 结果
     */
    public int insertFaceAccess(FaceAccess faceAccess);

    /**
     * 修改人脸识别开通记录
     * 
     * @param faceAccess 人脸识别开通记录
     * @return 结果
     */
    public int updateFaceAccess(FaceAccess faceAccess);

    /**
     * 删除人脸识别开通记录
     * 
     * @param id 人脸识别开通记录主键
     * @return 结果
     */
    public int deleteFaceAccessById(Long id);

    /**
     * 批量删除人脸识别开通记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFaceAccessByIds(Long[] ids);
}
