package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.ModificationRequest;

/**
 * 修改申请工单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface ModificationRequestMapper 
{
    /**
     * 查询修改申请工单
     * 
     * @param id 修改申请工单主键
     * @return 修改申请工单
     */
    public ModificationRequest selectModificationRequestById(Long id);

    /**
     * 查询修改申请工单列表
     * 
     * @param modificationRequest 修改申请工单
     * @return 修改申请工单集合
     */
    public List<ModificationRequest> selectModificationRequestList(ModificationRequest modificationRequest);

    /**
     * 新增修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    public int insertModificationRequest(ModificationRequest modificationRequest);

    /**
     * 修改修改申请工单
     * 
     * @param modificationRequest 修改申请工单
     * @return 结果
     */
    public int updateModificationRequest(ModificationRequest modificationRequest);

    /**
     * 删除修改申请工单
     * 
     * @param id 修改申请工单主键
     * @return 结果
     */
    public int deleteModificationRequestById(Long id);

    /**
     * 批量删除修改申请工单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteModificationRequestByIds(Long[] ids);
}
