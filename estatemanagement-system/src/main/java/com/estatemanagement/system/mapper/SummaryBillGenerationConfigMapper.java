package com.estatemanagement.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.estatemanagement.system.domain.SummaryBillGenerationConfig;

/**
 * 汇总账单生成配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface SummaryBillGenerationConfigMapper
{
    /**
     * 查询汇总账单生成配置
     * 
     * @param id 汇总账单生成配置主键
     * @return 汇总账单生成配置
     */
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigById(Long id);

    /**
     * 根据小区ID查询汇总账单生成配置
     * 
     * @param communityId 小区ID
     * @return 汇总账单生成配置
     */
    public SummaryBillGenerationConfig selectSummaryBillGenerationConfigByCommunityId(Long communityId);

    /**
     * 查询汇总账单生成配置列表
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 汇总账单生成配置集合
     */
    public List<SummaryBillGenerationConfig> selectSummaryBillGenerationConfigList(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 新增汇总账单生成配置
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    public int insertSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 修改汇总账单生成配置
     * 
     * @param summaryBillGenerationConfig 汇总账单生成配置
     * @return 结果
     */
    public int updateSummaryBillGenerationConfig(SummaryBillGenerationConfig summaryBillGenerationConfig);

    /**
     * 删除汇总账单生成配置
     * 
     * @param id 汇总账单生成配置主键
     * @return 结果
     */
    public int deleteSummaryBillGenerationConfigById(Long id);

    /**
     * 批量删除汇总账单生成配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSummaryBillGenerationConfigByIds(Long[] ids);
}
