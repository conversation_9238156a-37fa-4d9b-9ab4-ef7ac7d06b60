package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.BillGenerationConfig;

/**
 * 账单生成配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface BillGenerationConfigMapper 
{
    /**
     * 查询账单生成配置
     * 
     * @param id 账单生成配置主键
     * @return 账单生成配置
     */
    public BillGenerationConfig selectBillGenerationConfigById(Long id);

    /**
     * 查询账单生成配置列表
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 账单生成配置集合
     */
    public List<BillGenerationConfig> selectBillGenerationConfigList(BillGenerationConfig billGenerationConfig);

    /**
     * 根据小区ID查询账单生成配置
     * 
     * @param communityId 小区ID
     * @return 账单生成配置
     */
    public BillGenerationConfig selectBillGenerationConfigByCommunityId(Long communityId);

    /**
     * 新增账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    public int insertBillGenerationConfig(BillGenerationConfig billGenerationConfig);

    /**
     * 修改账单生成配置
     * 
     * @param billGenerationConfig 账单生成配置
     * @return 结果
     */
    public int updateBillGenerationConfig(BillGenerationConfig billGenerationConfig);

    /**
     * 删除账单生成配置
     * 
     * @param id 账单生成配置主键
     * @return 结果
     */
    public int deleteBillGenerationConfigById(Long id);

    /**
     * 批量删除账单生成配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBillGenerationConfigByIds(Long[] ids);
}
