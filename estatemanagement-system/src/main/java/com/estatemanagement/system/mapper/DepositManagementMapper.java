package com.estatemanagement.system.mapper;

import java.util.List;
import com.estatemanagement.system.domain.DepositManagement;

/**
 * 押金管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface DepositManagementMapper 
{
    /**
     * 查询押金管理
     * 
     * @param id 押金管理主键
     * @return 押金管理
     */
    public DepositManagement selectDepositManagementById(Long id);

    /**
     * 查询押金管理列表
     * 
     * @param depositManagement 押金管理
     * @return 押金管理集合
     */
    public List<DepositManagement> selectDepositManagementList(DepositManagement depositManagement);

    /**
     * 新增押金管理
     * 
     * @param depositManagement 押金管理
     * @return 结果
     */
    public int insertDepositManagement(DepositManagement depositManagement);

    /**
     * 修改押金管理
     * 
     * @param depositManagement 押金管理
     * @return 结果
     */
    public int updateDepositManagement(DepositManagement depositManagement);

    /**
     * 删除押金管理
     * 
     * @param id 押金管理主键
     * @return 结果
     */
    public int deleteDepositManagementById(Long id);

    /**
     * 批量删除押金管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDepositManagementByIds(Long[] ids);
}
