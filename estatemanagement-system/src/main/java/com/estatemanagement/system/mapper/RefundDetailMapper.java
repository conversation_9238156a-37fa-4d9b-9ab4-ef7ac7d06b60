package com.estatemanagement.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.estatemanagement.system.domain.RefundDetail;

/**
 * 退费明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface RefundDetailMapper 
{
    /**
     * 查询退费明细
     * 
     * @param id 退费明细主键
     * @return 退费明细
     */
    public RefundDetail selectRefundDetailById(Long id);

    /**
     * 查询退费明细列表
     * 
     * @param refundDetail 退费明细
     * @return 退费明细集合
     */
    public List<RefundDetail> selectRefundDetailList(RefundDetail refundDetail);

    /**
     * 根据退费记录ID查询退费明细
     * 
     * @param refundId 退费记录ID
     * @return 退费明细集合
     */
    public List<Map<String, Object>> selectRefundDetailsByRefundId(@Param("refundId") Long refundId);

    /**
     * 根据原收费明细ID查询退费明细
     * 
     * @param originalPaymentDetailId 原收费明细ID
     * @return 退费明细集合
     */
    public List<RefundDetail> selectRefundDetailsByOriginalPaymentDetailId(@Param("originalPaymentDetailId") Long originalPaymentDetailId);

    /**
     * 新增退费明细
     * 
     * @param refundDetail 退费明细
     * @return 结果
     */
    public int insertRefundDetail(RefundDetail refundDetail);

    /**
     * 修改退费明细
     * 
     * @param refundDetail 退费明细
     * @return 结果
     */
    public int updateRefundDetail(RefundDetail refundDetail);

    /**
     * 删除退费明细
     * 
     * @param id 退费明细主键
     * @return 结果
     */
    public int deleteRefundDetailById(Long id);

    /**
     * 批量删除退费明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRefundDetailByIds(Long[] ids);

    /**
     * 根据退费记录ID删除退费明细
     * 
     * @param refundId 退费记录ID
     * @return 结果
     */
    public int deleteRefundDetailByRefundId(@Param("refundId") Long refundId);
}
