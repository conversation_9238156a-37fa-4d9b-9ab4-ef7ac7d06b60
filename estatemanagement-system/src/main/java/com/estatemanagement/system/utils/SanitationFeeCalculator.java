package com.estatemanagement.system.utils;

import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.domain.TieredFeeConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 卫生费计算工具类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
public class SanitationFeeCalculator {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据小区配置和房屋面积计算卫生费
     *
     * @param community 小区信息
     * @param houseArea 房屋面积
     * @return 卫生费金额
     */
    public static BigDecimal calculateSanitationFee(CommunityMangement community, BigDecimal houseArea) {
        if (community == null) {
            return BigDecimal.ZERO;
        }

        Integer feeType = community.getSanitationFeeType();
        if (feeType == null) {
            feeType = 2; // 默认为阶梯费用
        }

        if (feeType == 1) {
            // 固定费用
            return calculateFixedFee(community);
        } else {
            if (houseArea == null) {
                log.error("houseArea is null");
                return BigDecimal.ZERO;
            }
            // 阶梯费用
            return calculateTieredFee(community, houseArea);
        }
    }

    /**
     * 计算固定费用
     */
    private static BigDecimal calculateFixedFee(CommunityMangement community) {
        BigDecimal fixedFee = community.getFixedSanitationFee();
        return fixedFee != null ? fixedFee : BigDecimal.ZERO;
    }

    /**
     * 计算阶梯费用
     */
    private static BigDecimal calculateTieredFee(CommunityMangement community, BigDecimal houseArea) {
        // 使用JSON配置
        String tieredConfig = community.getTieredFeeConfig();
        if (tieredConfig != null && !tieredConfig.trim().isEmpty()) {
            return calculateTieredFeeFromJson(tieredConfig, houseArea);
        }

        // 如果没有配置，返回0
        log.warn("小区 {} 未配置阶梯费用", community.getCommunityName());
        return BigDecimal.ZERO;
    }

    /**
     * 从JSON配置计算阶梯费用
     */
    private static BigDecimal calculateTieredFeeFromJson(String tieredConfig, BigDecimal houseArea) {
        try {
            TieredFeeConfig config = objectMapper.readValue(tieredConfig, TieredFeeConfig.class);
            return config.getFeeByArea(houseArea);
        } catch (Exception e) {
            log.error("解析阶梯费用配置失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    /**
     * 获取卫生费计算规则描述
     *
     * @param community 小区信息
     * @param houseArea 房屋面积
     * @return 计算规则描述
     */
    public static String getSanitationFeeRuleDescription(CommunityMangement community, BigDecimal houseArea) {
        if (community == null || houseArea == null) {
            return "未知规则";
        }

        Integer feeType = community.getSanitationFeeType();
        if (feeType == null) {
            feeType = 2; // 默认为阶梯费用
        }

        if (feeType == 1) {
            // 固定费用
            BigDecimal fixedFee = community.getFixedSanitationFee();
            return "固定费用: " + (fixedFee != null ? fixedFee : "0") + "元/月";
        } else {
            // 阶梯费用
            return getTieredFeeRuleDescription(community, houseArea);
        }
    }

    /**
     * 获取阶梯费用规则描述
     */
    private static String getTieredFeeRuleDescription(CommunityMangement community, BigDecimal houseArea) {
        // 使用JSON配置
        String tieredConfig = community.getTieredFeeConfig();
        if (tieredConfig != null && !tieredConfig.trim().isEmpty()) {
            return getTieredFeeRuleFromJson(tieredConfig, houseArea);
        }

        // 如果没有配置，返回默认描述
        return "阶梯费用: 未配置 (" + houseArea + "㎡)";
    }

    /**
     * 从JSON配置获取阶梯费用规则描述
     */
    private static String getTieredFeeRuleFromJson(String tieredConfig, BigDecimal houseArea) {
        try {
            TieredFeeConfig config = objectMapper.readValue(tieredConfig, TieredFeeConfig.class);
            TieredFeeConfig.TieredFeeRange range = config.getRangeByArea(houseArea);
            if (range != null) {
                return "阶梯费用: " + range.getRangeText() + " (" + houseArea + "㎡)";
            }
        } catch (Exception e) {
            log.error("解析阶梯费用配置失败: {}", e.getMessage());
        }
        return "阶梯费用: 未知范围 (" + houseArea + "㎡)";
    }
}
