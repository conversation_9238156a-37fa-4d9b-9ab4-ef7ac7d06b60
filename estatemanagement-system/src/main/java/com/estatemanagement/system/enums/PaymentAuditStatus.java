package com.estatemanagement.system.enums;

/**
 * 收费记录审核状态枚举
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public enum PaymentAuditStatus {
    
    /** 待审核 */
    PENDING(0, "待审核"),
    
    /** 通过审核 */
    APPROVED(1, "通过审核"),
    
    /** 审核拒绝 */
    REJECTED(2, "审核拒绝");
    
    private final Integer code;
    private final String description;
    
    PaymentAuditStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 状态代码
     * @return 对应的枚举值
     */
    public static PaymentAuditStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (PaymentAuditStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return null;
    }
    
    /**
     * 根据代码获取描述
     * 
     * @param code 状态代码
     * @return 状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        PaymentAuditStatus status = getByCode(code);
        return status != null ? status.getDescription() : "未知状态";
    }
    
    /**
     * 判断是否为待审核状态
     * 
     * @param code 状态代码
     * @return 是否为待审核状态
     */
    public static boolean isPending(Integer code) {
        return PENDING.getCode().equals(code);
    }
    
    /**
     * 判断是否为通过审核状态
     * 
     * @param code 状态代码
     * @return 是否为通过审核状态
     */
    public static boolean isApproved(Integer code) {
        return APPROVED.getCode().equals(code);
    }
    
    /**
     * 判断是否为审核拒绝状态
     * 
     * @param code 状态代码
     * @return 是否为审核拒绝状态
     */
    public static boolean isRejected(Integer code) {
        return REJECTED.getCode().equals(code);
    }
}
