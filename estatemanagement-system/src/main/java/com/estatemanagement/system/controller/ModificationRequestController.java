package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.ModificationRequest;
import com.estatemanagement.system.service.IModificationRequestService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 修改申请工单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/modificationRequest")
public class ModificationRequestController extends BaseController
{
    @Autowired
    private IModificationRequestService modificationRequestService;

    /**
     * 查询修改申请工单列表
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:list')")
    @GetMapping("/list")
    public TableDataInfo list(ModificationRequest modificationRequest)
    {
        startPage();
        List<ModificationRequest> list = modificationRequestService.selectModificationRequestList(modificationRequest);
        return getDataTable(list);
    }

    /**
     * 导出修改申请工单列表
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:export')")
    @Log(title = "修改申请工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModificationRequest modificationRequest)
    {
        List<ModificationRequest> list = modificationRequestService.selectModificationRequestList(modificationRequest);
        ExcelUtil<ModificationRequest> util = new ExcelUtil<ModificationRequest>(ModificationRequest.class);
        util.exportExcel(response, list, "修改申请工单数据");
    }

    /**
     * 获取修改申请工单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(modificationRequestService.selectModificationRequestById(id));
    }

    /**
     * 新增修改申请工单
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:add')")
    @Log(title = "修改申请工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ModificationRequest modificationRequest)
    {
        modificationRequest.setCreateBy(getUsername());
        return toAjax(modificationRequestService.insertModificationRequest(modificationRequest));
    }

    /**
     * 修改修改申请工单
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:edit')")
    @Log(title = "修改申请工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ModificationRequest modificationRequest)
    {
        modificationRequest.setUpdateBy(getUsername());
        return toAjax(modificationRequestService.updateModificationRequest(modificationRequest));
    }

    /**
     * 删除修改申请工单
     */
    @PreAuthorize("@ss.hasPermi('system:modificationRequest:remove')")
    @Log(title = "修改申请工单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(modificationRequestService.deleteModificationRequestByIds(ids));
    }
}
