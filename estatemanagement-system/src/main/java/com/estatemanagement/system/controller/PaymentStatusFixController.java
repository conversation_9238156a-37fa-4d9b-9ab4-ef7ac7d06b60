package com.estatemanagement.system.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.service.impl.PaymentStatusFixServiceImpl;

/**
 * 支付状态修复Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/system/paymentStatusFix")
public class PaymentStatusFixController extends BaseController
{
    @Autowired
    private PaymentStatusFixServiceImpl paymentStatusFixService;

    /**
     * 修复所有账单的支付状态
     */
    @PreAuthorize("@ss.hasPermi('system:paymentStatusFix:fix')")
    @Log(title = "支付状态修复", businessType = BusinessType.UPDATE)
    @PostMapping("/fixAll")
    public AjaxResult fixAllPaymentStatus()
    {
        try {
            int fixedCount = paymentStatusFixService.fixAllBillPaymentStatus();
            return success("支付状态修复完成，共修复 " + fixedCount + " 条账单记录");
        } catch (Exception e) {
            return error("支付状态修复失败：" + e.getMessage());
        }
    }
}
