package com.estatemanagement.system.controller;

import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.system.domain.FeeCalculationResult;
import com.estatemanagement.system.domain.TimeCalculationResult;
import com.estatemanagement.system.service.IFeeCalculationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 费用计算控制器
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/system/feeCalculation")
public class FeeCalculationController extends BaseController {
    
    @Autowired
    private IFeeCalculationService feeCalculationService;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 计算时间差
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateTime")
    public AjaxResult calculateTime(@RequestBody Map<String, String> params) {
        try {
            String startDateStr = params.get("startDate");
            String endDateStr = params.get("endDate");
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            TimeCalculationResult result = feeCalculationService.calculateTimeDifference(startDate, endDate);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算费用
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateFee")
    public AjaxResult calculateFee(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            BigDecimal monthlyFee = new BigDecimal(params.get("monthlyFee").toString());
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            FeeCalculationResult result = feeCalculationService.calculateFee(startDate, endDate, monthlyFee);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算物业费
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculatePropertyFee")
    public AjaxResult calculatePropertyFee(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            BigDecimal unitPrice = new BigDecimal(params.get("unitPrice").toString());
            BigDecimal area = new BigDecimal(params.get("area").toString());
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            FeeCalculationResult result = feeCalculationService.calculatePropertyFee(
                startDate, endDate, unitPrice, area);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算停车费
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateParkingFee")
    public AjaxResult calculateParkingFee(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            BigDecimal monthlyParkingFee = new BigDecimal(params.get("monthlyParkingFee").toString());
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            FeeCalculationResult result = feeCalculationService.calculateParkingFee(
                startDate, endDate, monthlyParkingFee);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算卫生费
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateSanitationFee")
    public AjaxResult calculateSanitationFee(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            BigDecimal monthlySanitationFee = new BigDecimal(params.get("monthlySanitationFee").toString());
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            FeeCalculationResult result = feeCalculationService.calculateSanitationFee(
                startDate, endDate, monthlySanitationFee);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 计算电梯费
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateElevatorFee")
    public AjaxResult calculateElevatorFee(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            String endDateStr = (String) params.get("endDate");
            BigDecimal monthlyElevatorFee = new BigDecimal(params.get("monthlyElevatorFee").toString());
            
            if (startDateStr == null || endDateStr == null) {
                return error("开始日期和结束日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);
            
            FeeCalculationResult result = feeCalculationService.calculateElevatorFee(
                startDate, endDate, monthlyElevatorFee);
            return success(result);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据月数计算结束日期
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @PostMapping("/calculateEndDate")
    public AjaxResult calculateEndDate(@RequestBody Map<String, Object> params) {
        try {
            String startDateStr = (String) params.get("startDate");
            Integer months = Integer.valueOf(params.get("months").toString());
            
            if (startDateStr == null) {
                return error("开始日期不能为空");
            }
            
            LocalDate startDate = LocalDate.parse(startDateStr, DATE_FORMATTER);
            LocalDate endDate = feeCalculationService.calculateEndDateByMonths(startDate, months);
            
            return success().put("endDate", endDate.format(DATE_FORMATTER));
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取指定月份的天数
     */
    @PreAuthorize("@ss.hasPermi('system:feeCalculation:calculate')")
    @GetMapping("/getDaysInMonth")
    public AjaxResult getDaysInMonth(@RequestParam int year, @RequestParam int month) {
        try {
            int days = feeCalculationService.getDaysInMonth(year, month);
            return success().put("days", days);
            
        } catch (Exception e) {
            return error("计算失败：" + e.getMessage());
        }
    }
}
