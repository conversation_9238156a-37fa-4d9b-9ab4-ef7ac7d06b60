package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.PaymentDetail;
import com.estatemanagement.system.service.IPaymentDetailService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 收费明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/paymentDetail")
public class PaymentDetailController extends BaseController
{
    @Autowired
    private IPaymentDetailService paymentDetailService;

    /**
     * 查询收费明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentDetail paymentDetail)
    {
        startPage();
        List<PaymentDetail> list = paymentDetailService.selectPaymentDetailList(paymentDetail);
        return getDataTable(list);
    }

    /**
     * 导出收费明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:export')")
    @Log(title = "收费明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentDetail paymentDetail)
    {
        List<PaymentDetail> list = paymentDetailService.selectPaymentDetailList(paymentDetail);
        ExcelUtil<PaymentDetail> util = new ExcelUtil<PaymentDetail>(PaymentDetail.class);
        util.exportExcel(response, list, "收费明细数据");
    }

    /**
     * 获取收费明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(paymentDetailService.selectPaymentDetailById(id));
    }

    /**
     * 新增收费明细
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:add')")
    @Log(title = "收费明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaymentDetail paymentDetail)
    {
        return toAjax(paymentDetailService.insertPaymentDetail(paymentDetail));
    }

    /**
     * 修改收费明细
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:edit')")
    @Log(title = "收费明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaymentDetail paymentDetail)
    {
        return toAjax(paymentDetailService.updatePaymentDetail(paymentDetail));
    }

    /**
     * 删除收费明细
     */
    @PreAuthorize("@ss.hasPermi('system:paymentDetail:remove')")
    @Log(title = "收费明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(paymentDetailService.deletePaymentDetailByIds(ids));
    }
}
