package com.estatemanagement.system.controller;

import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.core.page.TableDataInfo;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.utils.SecurityUtils;
import com.estatemanagement.system.domain.PaymentRecord;
import com.estatemanagement.system.domain.BillGenerationConfig;
import com.estatemanagement.system.enums.PaymentAuditStatus;
import com.estatemanagement.system.service.IPaymentRecordService;
import com.estatemanagement.system.service.IBillGenerationConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 收费记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/paymentRecord")
public class PaymentRecordController extends BaseController
{
    @Autowired
    private IPaymentRecordService paymentRecordService;

    @Autowired
    private IBillGenerationConfigService billGenerationConfigService;

    /**
     * 查询收费记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentRecord paymentRecord)
    {
        startPage();
        List<PaymentRecord> list = paymentRecordService.selectPaymentRecordList(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 查询未完全支付的账单，用于收费管理
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:list')")
    @GetMapping("/unpaidBills")
    public AjaxResult getUnpaidBills(@RequestParam(required = false) Long communityId, 
                                   @RequestParam(required = false) Long ownerId)
    {
        List<Map<String, Object>> bills = paymentRecordService.selectUnpaidBillsForPayment(communityId, ownerId);
        return success(bills);
    }

    /**
     * 导出收费记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:export')")
    @Log(title = "收费记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentRecord paymentRecord)
    {
        List<PaymentRecord> list = paymentRecordService.selectPaymentRecordList(paymentRecord);
        ExcelUtil<PaymentRecord> util = new ExcelUtil<PaymentRecord>(PaymentRecord.class);
        util.exportExcel(response, list, "收费记录数据");
    }

    /**
     * 获取收费记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(paymentRecordService.selectPaymentRecordById(id));
    }

    /**
     * 执行收费操作
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:add')")
    @Log(title = "收费记录", businessType = BusinessType.INSERT)
    @PostMapping("/payment")
    public AjaxResult processPayment(@RequestBody Map<String, Object> paymentData)
    {
        // 添加操作人ID
        paymentData.put("operatorId", getUserId());
        Long paymentRecordId = paymentRecordService.processPayment(paymentData);
        return success().put("data", paymentRecordId).put("msg", "收费成功");
    }

    /**
     * 新增收费记录
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:add')")
    @Log(title = "收费记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaymentRecord paymentRecord)
    {
        paymentRecord.setCreateBy(getUsername());
        paymentRecord.setOperatorId(getUserId());
        return toAjax(paymentRecordService.insertPaymentRecord(paymentRecord));
    }

    /**
     * 修改收费记录
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:edit')")
    @Log(title = "收费记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaymentRecord paymentRecord)
    {
        paymentRecord.setUpdateBy(getUsername());
        return toAjax(paymentRecordService.updatePaymentRecord(paymentRecord));
    }

    /**
     * 删除收费记录
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:remove')")
    @Log(title = "收费记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(paymentRecordService.deletePaymentRecordByIds(ids));
    }

    /**
     * 查询指定账单的收费记录及明细
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping("/bill/{billId}")
    public AjaxResult getBillPaymentRecords(@PathVariable("billId") Long billId)
    {
        List<Map<String, Object>> records = paymentRecordService.selectPaymentRecordsByBillId(billId);
        return success(records);
    }

    /**
     * 计算预交费用金额
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping("/calculateAdvanceFee")
    public AjaxResult calculateAdvanceFee(@RequestParam Long communityId, 
                                        @RequestParam Long ownerId,
                                        @RequestParam Integer feeType,
                                        @RequestParam Integer months)
    {
        java.math.BigDecimal amount = paymentRecordService.calculateAdvanceFeeAmount(communityId, ownerId, feeType, months);
        return success().put("amount", amount);
    }

    /**
     * 获取收费记录的明细信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping("/details/{paymentId}")
    public AjaxResult getPaymentDetails(@PathVariable("paymentId") Long paymentId)
    {
        List<Map<String, Object>> details = paymentRecordService.selectPaymentDetailsByPaymentId(paymentId);
        return success(details);
    }

    /**
     * 查询收费记录详情（包含明细）用于打印
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping("/withDetails/{paymentId}")
    public AjaxResult getPaymentRecordWithDetails(@PathVariable("paymentId") Long paymentId)
    {
        Map<String, Object> result = paymentRecordService.selectPaymentRecordWithDetails(paymentId);
        return success(result);
    }

    /**
     * 手动生成账单
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:add')")
    @Log(title = "手动生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateBills")
    public AjaxResult generateBills(@RequestBody Map<String, Object> params)
    {
        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            String generateType = params.get("generateType").toString();

            String taskId = billGenerationConfigService.manualGenerateBills(communityId, generateType);
            return success("账单生成任务已启动").put("taskId", taskId);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 查询账单生成配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:list')")
    @GetMapping("/billConfig/list")
    public AjaxResult getBillConfigList(BillGenerationConfig billGenerationConfig)
    {
        List<BillGenerationConfig> list = billGenerationConfigService.selectBillGenerationConfigList(billGenerationConfig);
        return success(list);
    }

    /**
     * 根据小区ID查询账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @GetMapping("/billConfig/community/{communityId}")
    public AjaxResult getBillConfigByCommunityId(@PathVariable("communityId") Long communityId)
    {
        BillGenerationConfig config = billGenerationConfigService.selectBillGenerationConfigByCommunityId(communityId);
        return success(config);
    }

    /**
     * 新增账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:add')")
    @Log(title = "账单生成配置", businessType = BusinessType.INSERT)
    @PostMapping("/billConfig")
    public AjaxResult addBillConfig(@RequestBody BillGenerationConfig billGenerationConfig)
    {
        billGenerationConfig.setCreateBy(getUsername());
        return toAjax(billGenerationConfigService.insertBillGenerationConfig(billGenerationConfig));
    }

    /**
     * 修改账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:edit')")
    @Log(title = "账单生成配置", businessType = BusinessType.UPDATE)
    @PutMapping("/billConfig")
    public AjaxResult editBillConfig(@RequestBody BillGenerationConfig billGenerationConfig)
    {
        billGenerationConfig.setUpdateBy(getUsername());
        return toAjax(billGenerationConfigService.updateBillGenerationConfig(billGenerationConfig));
    }

    /**
     * 启用或停用账单自动生成
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:edit')")
    @Log(title = "账单自动生成", businessType = BusinessType.UPDATE)
    @PostMapping("/billConfig/toggle")
    public AjaxResult toggleAutoGeneration(@RequestBody Map<String, Object> params)
    {
        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            Integer status = Integer.valueOf(params.get("status").toString());

            billGenerationConfigService.toggleAutoGeneration(communityId, status);
            return success(status == 1 ? "已启用自动生成" : "已停用自动生成");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 计算部分支付费用金额
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @PostMapping("/calculatePartialFee")
    public AjaxResult calculatePartialFee(@RequestBody Map<String, Object> params) {
        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            Long ownerId = Long.valueOf(params.get("ownerId").toString());
            Long billId = params.get("billId") != null ? Long.valueOf(params.get("billId").toString()) : null;
            Integer feeType = Integer.valueOf(params.get("feeType").toString());
            Integer months = Integer.valueOf(params.get("months").toString());
            String plateNumber = params.get("plateNumber") != null ? params.get("plateNumber").toString() : null;

            java.math.BigDecimal amount = paymentRecordService.calculatePartialFeeAmount(communityId, ownerId, billId, feeType, months, plateNumber);

            return success(amount);
        } catch (Exception e) {
            return error("计算部分支付费用失败：" + e.getMessage());
        }
    }

    /**
     * 计算部分支付的详细信息（包括缴费周期）
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @PostMapping("/calculatePartialFeeDetails")
    public AjaxResult calculatePartialFeeDetails(@RequestBody Map<String, Object> params) {
        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            Long ownerId = Long.valueOf(params.get("ownerId").toString());
            Long billId = Long.valueOf(params.get("billId").toString());
            Integer feeType = Integer.valueOf(params.get("feeType").toString());
            Integer months = Integer.valueOf(params.get("months").toString());
            String plateNumber = params.get("plateNumber") != null ? params.get("plateNumber").toString() : null;

            Map<String, Object> details = paymentRecordService.calculatePartialFeeDetails(communityId, ownerId, billId, feeType, months, plateNumber);

            if (details.containsKey("error")) {
                return error(details.get("error").toString());
            }

            return success(details);
        } catch (Exception e) {
            logger.error("计算部分支付详细信息失败：{}", e.getMessage());
            return error("计算部分支付详细信息失败：" + e.getMessage());
        }
    }

    /**
     * 计算混合支付金额
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:query')")
    @PostMapping("/calculateMixedAmount")
    public AjaxResult calculateMixedAmount(@RequestBody Map<String, Object> params) {
        try {
            Map<String, Object> result = paymentRecordService.calculateMixedPaymentAmount(params);
            return success(result);
        } catch (Exception e) {
            return error("计算混合支付金额失败：" + e.getMessage());
        }
    }

    /**
     * 审核收费记录
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:audit')")
    @Log(title = "收费记录审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult auditPaymentRecord(@RequestBody Map<String, Object> params) {
        try {
            Long paymentId = Long.valueOf(params.get("paymentId").toString());
            Integer auditStatus = Integer.valueOf(params.get("auditStatus").toString());
            String auditComment = params.get("auditComment") != null ? params.get("auditComment").toString() : "";
            Long auditorId = SecurityUtils.getUserId();

            boolean result = paymentRecordService.auditPaymentRecord(paymentId, auditStatus, auditComment, auditorId);
            return result ? success("审核成功") : error("审核失败");
        } catch (Exception e) {
            return error("审核失败：" + e.getMessage());
        }
    }

    /**
     * 批量审核收费记录
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:audit')")
    @Log(title = "批量审核收费记录", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAudit")
    public AjaxResult batchAuditPaymentRecords(@RequestBody Map<String, Object> params) {
        try {
            Object[] paymentIdObjs = (Object[]) params.get("paymentIds");
            Long[] paymentIds = new Long[paymentIdObjs.length];
            for (int i = 0; i < paymentIdObjs.length; i++) {
                paymentIds[i] = Long.valueOf(paymentIdObjs[i].toString());
            }

            Integer auditStatus = Integer.valueOf(params.get("auditStatus").toString());
            String auditComment = params.get("auditComment") != null ? params.get("auditComment").toString() : "";
            Long auditorId = SecurityUtils.getUserId();

            boolean result = paymentRecordService.batchAuditPaymentRecords(paymentIds, auditStatus, auditComment, auditorId);
            return result ? success("批量审核成功") : error("部分记录审核失败");
        } catch (Exception e) {
            return error("批量审核失败：" + e.getMessage());
        }
    }

    /**
     * 查询待审核的收费记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:list')")
    @GetMapping("/pendingAudit")
    public TableDataInfo pendingAuditList(PaymentRecord paymentRecord) {
        startPage();
        List<PaymentRecord> list = paymentRecordService.selectPendingAuditPaymentRecords(paymentRecord);
        return getDataTable(list);
    }

    /**
     * 获取收费记录审核统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:paymentRecord:list')")
    @GetMapping("/auditStatistics")
    public AjaxResult getAuditStatistics(@RequestParam(required = false) Long communityId) {
        try {
            Map<String, Object> statistics = paymentRecordService.getPaymentAuditStatistics(communityId);
            return success(statistics);
        } catch (Exception e) {
            return error("获取审核统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取审核状态选项
     */
    @GetMapping("/auditStatusOptions")
    public AjaxResult getAuditStatusOptions() {
        Map<String, Object> options = new java.util.HashMap<>();
        options.put("PENDING", PaymentAuditStatus.PENDING.getCode());
        options.put("APPROVED", PaymentAuditStatus.APPROVED.getCode());
        options.put("REJECTED", PaymentAuditStatus.REJECTED.getCode());
        return success(options);
    }
}
