package com.estatemanagement.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.RefundRecord;
import com.estatemanagement.system.service.IRefundRecordService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 退费记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/system/refundRecord")
public class RefundRecordController extends BaseController
{
    @Autowired
    private IRefundRecordService refundRecordService;

    /**
     * 查询退费记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(RefundRecord refundRecord)
    {
        startPage();
        List<Map<String, Object>> list = refundRecordService.selectRefundRecordListWithDetails(refundRecord);
        return getDataTable(list);
    }

    /**
     * 导出退费记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:export')")
    @Log(title = "退费记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RefundRecord refundRecord)
    {
        List<RefundRecord> list = refundRecordService.selectRefundRecordList(refundRecord);
        ExcelUtil<RefundRecord> util = new ExcelUtil<RefundRecord>(RefundRecord.class);
        util.exportExcel(response, list, "退费记录数据");
    }

    /**
     * 获取退费记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(refundRecordService.selectRefundRecordById(id));
    }

    /**
     * 新增退费记录
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:add')")
    @Log(title = "退费记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RefundRecord refundRecord)
    {
        return toAjax(refundRecordService.insertRefundRecord(refundRecord));
    }

    /**
     * 修改退费记录
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:edit')")
    @Log(title = "退费记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RefundRecord refundRecord)
    {
        return toAjax(refundRecordService.updateRefundRecord(refundRecord));
    }

    /**
     * 删除退费记录
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:remove')")
    @Log(title = "退费记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(refundRecordService.deleteRefundRecordByIds(ids));
    }

    /**
     * 处理退费申请
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:add')")
    @Log(title = "退费申请", businessType = BusinessType.INSERT)
    @PostMapping("/processRefund")
    public AjaxResult processRefund(@RequestBody Map<String, Object> refundData)
    {
        try {
            Long refundId = refundRecordService.processRefund(refundData);
            return success("退费申请处理成功");
        } catch (Exception e) {
            return error("退费申请处理失败：" + e.getMessage());
        }
    }

    /**
     * 计算可退费金额
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:query')")
    @PostMapping("/calculateRefundAmount")
    public AjaxResult calculateRefundAmount(@RequestBody Map<String, Object> params)
    {
        try {
            Long paymentId = Long.valueOf(params.get("paymentId").toString());
            Integer refundType = Integer.valueOf(params.get("refundType").toString());
            List<Map<String, Object>> refundItems = (List<Map<String, Object>>) params.get("refundItems");

            Map<String, Object> result = refundRecordService.calculateRefundAmount(paymentId, refundType, refundItems);

            if (result.containsKey("error")) {
                return error(result.get("error").toString());
            }

            return success(result);
        } catch (Exception e) {
            return error("计算退费金额失败：" + e.getMessage());
        }
    }

    /**
     * 获取收费记录的退费信息
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:query')")
    @GetMapping("/paymentRefundInfo/{paymentId}")
    public AjaxResult getPaymentRefundInfo(@PathVariable("paymentId") Long paymentId)
    {
        try {
            Map<String, Object> result = refundRecordService.getPaymentRefundInfo(paymentId);

            if (result.containsKey("error")) {
                return error(result.get("error").toString());
            }

            return success(result);
        } catch (Exception e) {
            return error("获取退费信息失败：" + e.getMessage());
        }
    }

    /**
     * 审核退费记录
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:audit')")
    @Log(title = "退费审核", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public AjaxResult auditRefund(@RequestBody Map<String, Object> auditData)
    {
        try {
            Long id = Long.valueOf(auditData.get("id").toString());
            Integer status = Integer.valueOf(auditData.get("status").toString());
            Long auditUserId = Long.valueOf(auditData.get("auditUserId").toString());
            String auditUserName = auditData.get("auditUserName").toString();
            String auditRemark = auditData.get("auditRemark").toString();

            int result = refundRecordService.auditRefundRecord(id, status, auditUserId, auditUserName, auditRemark);
            return toAjax(result);
        } catch (Exception e) {
            return error("审核失败：" + e.getMessage());
        }
    }

    /**
     * 统计退费金额
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:query')")
    @PostMapping("/statistics")
    public AjaxResult getRefundStatistics(@RequestBody Map<String, Object> params)
    {
        try {
            Map<String, Object> result = refundRecordService.selectRefundStatistics(params);
            return success(result);
        } catch (Exception e) {
            return error("统计失败：" + e.getMessage());
        }
    }

    /**
     * 根据原收费记录ID查询退费记录
     */
    @PreAuthorize("@ss.hasPermi('system:refundRecord:query')")
    @GetMapping("/byOriginalPayment/{originalPaymentId}")
    public AjaxResult getRefundsByOriginalPayment(@PathVariable("originalPaymentId") Long originalPaymentId)
    {
        try {
            List<RefundRecord> refunds = refundRecordService.selectRefundRecordsByOriginalPaymentId(originalPaymentId);
            return success(refunds);
        } catch (Exception e) {
            return error("查询失败：" + e.getMessage());
        }
    }
}
