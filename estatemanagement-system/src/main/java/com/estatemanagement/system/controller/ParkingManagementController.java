package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.ParkingInfo;
import com.estatemanagement.system.service.IParkingInfoService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 车位管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/system/parkingManagement")
public class ParkingManagementController extends BaseController
{
    @Autowired
    private IParkingInfoService parkingInfoService;

    /**
     * 查询车位管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:list')")
    @GetMapping("/list")
    public TableDataInfo list(ParkingInfo parkingInfo)
    {
        startPage();
        List<ParkingInfo> list = parkingInfoService.selectParkingManagementList(parkingInfo);
        return getDataTable(list);
    }

    /**
     * 导出车位管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:export')")
    @Log(title = "车位管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ParkingInfo parkingInfo)
    {
        List<ParkingInfo> list = parkingInfoService.selectParkingInfoList(parkingInfo);
        ExcelUtil<ParkingInfo> util = new ExcelUtil<ParkingInfo>(ParkingInfo.class);
        util.exportExcel(response, list, "车位管理数据");
    }

    /**
     * 获取车位管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(parkingInfoService.selectParkingInfoById(id));
    }

    /**
     * 新增车位管理
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:add')")
    @Log(title = "车位管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ParkingInfo parkingInfo)
    {
        parkingInfo.setCreateBy(getUsername());
        return toAjax(parkingInfoService.insertParkingInfo(parkingInfo));
    }

    /**
     * 修改车位管理
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:edit')")
    @Log(title = "车位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ParkingInfo parkingInfo)
    {
        parkingInfo.setUpdateBy(getUsername());
        return toAjax(parkingInfoService.updateParkingInfo(parkingInfo));
    }

    /**
     * 删除车位管理
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:remove')")
    @Log(title = "车位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(parkingInfoService.deleteParkingInfoByIds(ids));
    }



    /**
     * 释放车位
     * 将车位恢复为空闲状态，清空所有相关的业主、租客、车牌等信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:release')")
    @Log(title = "车位释放", businessType = BusinessType.UPDATE)
    @PostMapping("/release/{id}")
    public AjaxResult releaseParking(@PathVariable Long id)
    {
        try {
            ParkingInfo parkingInfo = parkingInfoService.selectParkingInfoById(id);
            if (parkingInfo == null) {
                return error("车位信息不存在");
            }

            // 记录释放前的信息用于日志
            String beforeInfo = String.format("车位释放前信息：车位号=%s, 业主ID=%s, 租客ID=%s, 车牌号=%s, 状态=%s",
                parkingInfo.getSpaceNumber(),
                parkingInfo.getOwnerId(),
                parkingInfo.getTenantId(),
                parkingInfo.getPlateNumber(),
                parkingInfo.getStatus());
            System.out.println(beforeInfo);

            // 执行车位释放操作
            int result = parkingInfoService.forceUpdateParkingInfo(parkingInfo);

            if (result > 0) {
                String afterInfo = String.format("车位释放成功：车位号=%s 已恢复为空闲状态", parkingInfo.getSpaceNumber());
                System.out.println(afterInfo);
                return success("车位释放成功");
            } else {
                return error("车位释放失败");
            }

        } catch (Exception e) {
            System.err.println("车位释放异常：" + e.getMessage());
            e.printStackTrace();
            return error("车位释放失败：" + e.getMessage());
        }
    }

    /**
     * 查询可用车位
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:list')")
    @GetMapping("/available")
    public TableDataInfo getAvailableParking(ParkingInfo parkingInfo)
    {
        startPage();
        parkingInfo.setStatus(0); // 查询无效状态的车位（可用车位）
        List<ParkingInfo> list = parkingInfoService.selectParkingInfoList(parkingInfo);
        return getDataTable(list);
    }

    /**
     * 批量创建车位
     */
    @PreAuthorize("@ss.hasPermi('system:parkingManagement:add')")
    @Log(title = "批量创建车位", businessType = BusinessType.INSERT)
    @PostMapping("/batchCreate")
    public AjaxResult batchCreateParking(@RequestBody BatchCreateParkingRequest request)
    {
        try {
            int count = parkingInfoService.batchCreateParkingSpaces(request);
            return AjaxResult.success("成功创建 " + count + " 个车位", count);
        } catch (Exception e) {
            return error("批量创建失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建车位请求参数
     */
    public static class BatchCreateParkingRequest {
        private Long communityId;
        private String prefix;
        private Integer startNum;
        private Integer endNum;
        private Integer numLength;

        // getters and setters
        public Long getCommunityId() { return communityId; }
        public void setCommunityId(Long communityId) { this.communityId = communityId; }
        public String getPrefix() { return prefix; }
        public void setPrefix(String prefix) { this.prefix = prefix; }
        public Integer getStartNum() { return startNum; }
        public void setStartNum(Integer startNum) { this.startNum = startNum; }
        public Integer getEndNum() { return endNum; }
        public void setEndNum(Integer endNum) { this.endNum = endNum; }
        public Integer getNumLength() { return numLength; }
        public void setNumLength(Integer numLength) { this.numLength = numLength; }
    }


}
