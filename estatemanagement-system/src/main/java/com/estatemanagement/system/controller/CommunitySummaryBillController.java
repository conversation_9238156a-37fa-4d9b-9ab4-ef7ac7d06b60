package com.estatemanagement.system.controller;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.constants.SummaryBillConstants;
import com.estatemanagement.system.domain.CommunitySummaryBill;
import com.estatemanagement.system.service.ICommunitySummaryBillService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;
import java.util.Date;

/**
 * 小区汇总账单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/communitySummaryBill")
public class CommunitySummaryBillController extends BaseController
{
    @Autowired
    private ICommunitySummaryBillService communitySummaryBillService;

    /**
     * 查询小区汇总账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunitySummaryBill communitySummaryBill)
    {
        startPage();
        List<CommunitySummaryBill> list = communitySummaryBillService.selectCommunitySummaryBillList(communitySummaryBill);
        return getDataTable(list);
    }

    /**
     * 导出小区汇总账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:export')")
    @Log(title = "小区汇总账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunitySummaryBill communitySummaryBill)
    {
        List<CommunitySummaryBill> list = communitySummaryBillService.selectCommunitySummaryBillList(communitySummaryBill);
        ExcelUtil<CommunitySummaryBill> util = new ExcelUtil<CommunitySummaryBill>(CommunitySummaryBill.class);
        util.exportExcel(response, list, "小区汇总账单数据");
    }

    /**
     * 获取小区汇总账单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communitySummaryBillService.selectCommunitySummaryBillById(id));
    }

    /**
     * 新增小区汇总账单
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:add')")
    @Log(title = "小区汇总账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunitySummaryBill communitySummaryBill)
    {
        communitySummaryBill.setCreateBy(getUsername());
        return toAjax(communitySummaryBillService.insertCommunitySummaryBill(communitySummaryBill));
    }

    /**
     * 修改小区汇总账单
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:edit')")
    @Log(title = "小区汇总账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunitySummaryBill communitySummaryBill)
    {
        communitySummaryBill.setUpdateBy(getUsername());
        return toAjax(communitySummaryBillService.updateCommunitySummaryBill(communitySummaryBill));
    }

    /**
     * 删除小区汇总账单
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:remove')")
    @Log(title = "小区汇总账单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communitySummaryBillService.deleteCommunitySummaryBillByIds(ids));
    }

    /**
     * 财务确认收钱
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:edit')")
    @Log(title = "小区汇总账单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirmReceive")
    public AjaxResult confirmReceive(@RequestBody CommunitySummaryBill communitySummaryBill)
    {
        communitySummaryBill.setReceivedBy(getUsername());
        communitySummaryBill.setReceivedDate(new Date());
        communitySummaryBill.setReceiveStatus(1); // 设置为已确认收钱

        // 计算收钱差额
        if (communitySummaryBill.getActualAmount() != null && communitySummaryBill.getActualReceivedAmount() != null) {
            java.math.BigDecimal difference = communitySummaryBill.getActualAmount().subtract(communitySummaryBill.getActualReceivedAmount());
            communitySummaryBill.setReceiveDifference(difference);
        }

        return toAjax(communitySummaryBillService.updateCommunitySummaryBill(communitySummaryBill));
    }

    /**
     * 手动生成汇总账单
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:add')")
    @Log(title = "生成汇总账单", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateSummaryBills(@RequestBody Map<String, Object> params)
    {
        try {
            // 解析参数
            Long communityId = null;
            if (params.get("communityId") != null && !"".equals(params.get("communityId").toString())) {
                communityId = Long.valueOf(params.get("communityId").toString());
            }

            String startDateStr = params.get("startDate").toString();
            String endDateStr = params.get("endDate").toString();

            SimpleDateFormat sdf = new SimpleDateFormat(SummaryBillConstants.DateFormat.DATE_STANDARD);
            Date startDate = sdf.parse(startDateStr);
            Date endDate = sdf.parse(endDateStr);

            // 验证日期范围
            if (startDate.after(endDate)) {
                return error("开始日期不能晚于结束日期");
            }

            String taskId = communitySummaryBillService.generateSummaryBills(communityId, startDate, endDate);
            AjaxResult result = success("汇总账单生成任务已启动");
            result.put("taskId", taskId);
            return result;
        } catch (ParseException e) {
            return error("日期格式错误");
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 财务确认
     */
    @PreAuthorize("@ss.hasPermi('system:communitySummaryBill:edit')")
    @Log(title = "财务确认", businessType = BusinessType.UPDATE)
    @PostMapping("/financeConfirm")
    public AjaxResult financeConfirm(@RequestBody Map<String, Object> params)
    {
        try {
            Long id = Long.valueOf(params.get("id").toString());
            String financeConfirmDate = params.get("financeConfirmDate").toString();
            String financeConfirmBy = params.get("financeConfirmBy").toString();
            String financeRemark = params.get("financeRemark") != null ? params.get("financeRemark").toString() : "";

            SimpleDateFormat sdf = new SimpleDateFormat(SummaryBillConstants.DateFormat.DATE_STANDARD);
            Date confirmDate = sdf.parse(financeConfirmDate);

            CommunitySummaryBill summaryBill = new CommunitySummaryBill();
            summaryBill.setId(id);
            summaryBill.setFinanceConfirm(SummaryBillConstants.FinanceConfirmStatus.CONFIRMED);
            summaryBill.setFinanceConfirmDate(confirmDate);
            summaryBill.setFinanceConfirmBy(financeConfirmBy);
            summaryBill.setRemark(financeRemark);

            return toAjax(communitySummaryBillService.updateCommunitySummaryBill(summaryBill));
        } catch (Exception e) {
            return error("财务确认失败: " + e.getMessage());
        }
    }
}
