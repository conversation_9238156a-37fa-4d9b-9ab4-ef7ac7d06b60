package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.DepositManagement;
import com.estatemanagement.system.service.IDepositManagementService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 押金管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/system/depositManagement")
public class DepositManagementController extends BaseController
{
    @Autowired
    private IDepositManagementService depositManagementService;

    /**
     * 查询押金管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:list')")
    @GetMapping("/list")
    public TableDataInfo list(DepositManagement depositManagement)
    {
        startPage();
        List<DepositManagement> list = depositManagementService.selectDepositManagementList(depositManagement);
        return getDataTable(list);
    }

    /**
     * 导出押金管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:export')")
    @Log(title = "押金管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DepositManagement depositManagement)
    {
        List<DepositManagement> list = depositManagementService.selectDepositManagementList(depositManagement);
        ExcelUtil<DepositManagement> util = new ExcelUtil<DepositManagement>(DepositManagement.class);
        util.exportExcel(response, list, "押金管理数据");
    }

    /**
     * 获取押金管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(depositManagementService.selectDepositManagementById(id));
    }

    /**
     * 新增押金管理
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:add')")
    @Log(title = "押金管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DepositManagement depositManagement)
    {
        depositManagement.setCreateBy(getUsername());
        depositManagement.setCollectUserId(getUserId());
        depositManagement.setCollectUserName(getUsername());
        return toAjax(depositManagementService.insertDepositManagement(depositManagement));
    }

    /**
     * 修改押金管理
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:edit')")
    @Log(title = "押金管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DepositManagement depositManagement)
    {
        depositManagement.setUpdateBy(getUsername());
        return toAjax(depositManagementService.updateDepositManagement(depositManagement));
    }

    /**
     * 删除押金管理
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:remove')")
    @Log(title = "押金管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(depositManagementService.deleteDepositManagementByIds(ids));
    }

    /**
     * 退还押金
     */
    @PreAuthorize("@ss.hasPermi('system:depositManagement:refund')")
    @Log(title = "押金退还", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public AjaxResult refund(@RequestBody DepositManagement depositManagement)
    {
        try {
            depositManagement.setRefundUserId(getUserId());
            depositManagement.setRefundUserName(getUsername());
            return toAjax(depositManagementService.refundDeposit(depositManagement));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
