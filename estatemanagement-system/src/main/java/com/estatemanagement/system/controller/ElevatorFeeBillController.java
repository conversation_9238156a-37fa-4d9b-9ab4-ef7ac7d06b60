package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.ElevatorFeeBill;
import com.estatemanagement.system.service.IElevatorFeeBillService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 电梯费Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/elevatorFeeBill")
public class ElevatorFeeBillController extends BaseController
{
    @Autowired
    private IElevatorFeeBillService elevatorFeeBillService;

    /**
     * 查询电梯费列表
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:list')")
    @GetMapping("/list")
    public TableDataInfo list(ElevatorFeeBill elevatorFeeBill)
    {
        startPage();
        List<ElevatorFeeBill> list = elevatorFeeBillService.selectElevatorFeeBillList(elevatorFeeBill);
        return getDataTable(list);
    }

    /**
     * 导出电梯费列表
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:export')")
    @Log(title = "电梯费", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ElevatorFeeBill elevatorFeeBill)
    {
        List<ElevatorFeeBill> list = elevatorFeeBillService.selectElevatorFeeBillList(elevatorFeeBill);
        ExcelUtil<ElevatorFeeBill> util = new ExcelUtil<ElevatorFeeBill>(ElevatorFeeBill.class);
        util.exportExcel(response, list, "电梯费数据");
    }

    /**
     * 获取电梯费详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(elevatorFeeBillService.selectElevatorFeeBillById(id));
    }

    /**
     * 新增电梯费
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:add')")
    @Log(title = "电梯费", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ElevatorFeeBill elevatorFeeBill)
    {
        elevatorFeeBill.setCreateBy(getUsername());
        return toAjax(elevatorFeeBillService.insertElevatorFeeBill(elevatorFeeBill));
    }

    /**
     * 修改电梯费
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:edit')")
    @Log(title = "电梯费", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ElevatorFeeBill elevatorFeeBill)
    {
        elevatorFeeBill.setUpdateBy(getUsername());
        return toAjax(elevatorFeeBillService.updateElevatorFeeBill(elevatorFeeBill));
    }

    /**
     * 删除电梯费
     */
    @PreAuthorize("@ss.hasPermi('system:elevatorFeeBill:remove')")
    @Log(title = "电梯费", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(elevatorFeeBillService.deleteElevatorFeeBillByIds(ids));
    }
}
