package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.CommunityMangement;
import com.estatemanagement.system.service.ICommunityMangementService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 小区管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequestMapping("/system/communityMangement")
public class CommunityMangementController extends BaseController
{
    @Autowired
    private ICommunityMangementService communityMangementService;

    /**
     * 查询小区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityMangement communityMangement)
    {
        startPage();
        List<CommunityMangement> list = communityMangementService.selectCommunityMangementList(communityMangement);
        return getDataTable(list);
    }

    /**
     * 导出小区管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:export')")
    @Log(title = "小区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityMangement communityMangement)
    {
        List<CommunityMangement> list = communityMangementService.selectCommunityMangementList(communityMangement);
        ExcelUtil<CommunityMangement> util = new ExcelUtil<CommunityMangement>(CommunityMangement.class);
        util.exportExcel(response, list, "小区管理数据");
    }

    /**
     * 获取小区管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityMangementService.selectCommunityMangementById(id));
    }

    /**
     * 新增小区管理
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:add')")
    @Log(title = "小区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityMangement communityMangement)
    {
        communityMangement.setCreateBy(getUsername());
        return toAjax(communityMangementService.insertCommunityMangement(communityMangement));
    }

    /**
     * 修改小区管理
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:edit')")
    @Log(title = "小区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityMangement communityMangement)
    {
        communityMangement.setUpdateBy(getUsername());
        return toAjax(communityMangementService.updateCommunityMangement(communityMangement));
    }

    /**
     * 删除小区管理
     */
    @PreAuthorize("@ss.hasPermi('system:communityMangement:remove')")
    @Log(title = "小区管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityMangementService.deleteCommunityMangementByIds(ids));
    }
}
