package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.SummaryBillGenerationConfig;
import com.estatemanagement.system.service.ISummaryBillGenerationConfigService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 汇总账单生成配置Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/system/summaryBillGenerationConfig")
public class SummaryBillGenerationConfigController extends BaseController
{
    @Autowired
    private ISummaryBillGenerationConfigService summaryBillGenerationConfigService;

    /**
     * 查询汇总账单生成配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        startPage();
        List<SummaryBillGenerationConfig> list = summaryBillGenerationConfigService.selectSummaryBillGenerationConfigList(summaryBillGenerationConfig);
        return getDataTable(list);
    }

    /**
     * 导出汇总账单生成配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:export')")
    @Log(title = "汇总账单生成配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        List<SummaryBillGenerationConfig> list = summaryBillGenerationConfigService.selectSummaryBillGenerationConfigList(summaryBillGenerationConfig);
        ExcelUtil<SummaryBillGenerationConfig> util = new ExcelUtil<SummaryBillGenerationConfig>(SummaryBillGenerationConfig.class);
        util.exportExcel(response, list, "汇总账单生成配置数据");
    }

    /**
     * 获取汇总账单生成配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(summaryBillGenerationConfigService.selectSummaryBillGenerationConfigById(id));
    }

    /**
     * 根据小区ID获取汇总账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:query')")
    @GetMapping(value = "/community/{communityId}")
    public AjaxResult getInfoByCommunityId(@PathVariable("communityId") Long communityId)
    {
        return success(summaryBillGenerationConfigService.selectSummaryBillGenerationConfigByCommunityId(communityId));
    }

    /**
     * 新增汇总账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:add')")
    @Log(title = "汇总账单生成配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        return toAjax(summaryBillGenerationConfigService.insertSummaryBillGenerationConfig(summaryBillGenerationConfig));
    }

    /**
     * 修改汇总账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:edit')")
    @Log(title = "汇总账单生成配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SummaryBillGenerationConfig summaryBillGenerationConfig)
    {
        return toAjax(summaryBillGenerationConfigService.updateSummaryBillGenerationConfig(summaryBillGenerationConfig));
    }

    /**
     * 删除汇总账单生成配置
     */
    @PreAuthorize("@ss.hasPermi('system:summaryBillGenerationConfig:remove')")
    @Log(title = "汇总账单生成配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(summaryBillGenerationConfigService.deleteSummaryBillGenerationConfigByIds(ids));
    }
}
