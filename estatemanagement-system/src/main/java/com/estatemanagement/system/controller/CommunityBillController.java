package com.estatemanagement.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.CommunityBill;
import com.estatemanagement.system.service.ICommunityBillService;
import com.estatemanagement.system.service.impl.CommunityBillServiceImpl.BillGenerationProgress;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 小区账单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/communityBill")
public class CommunityBillController extends BaseController
{
    @Autowired
    private ICommunityBillService communityBillService;

    /**
     * 查询小区账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityBill communityBill)
    {
        startPage();
        List<CommunityBill> list = communityBillService.selectCommunityBillList(communityBill);
        return getDataTable(list);
    }

    /**
     * 导出小区账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:export')")
    @Log(title = "小区账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityBill communityBill)
    {
        List<CommunityBill> list = communityBillService.selectCommunityBillList(communityBill);
        ExcelUtil<CommunityBill> util = new ExcelUtil<CommunityBill>(CommunityBill.class);
        util.exportExcel(response, list, "小区账单数据");
    }

    /**
     * 获取小区账单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityBillService.selectCommunityBillById(id));
    }

    /**
     * 获取小区账单详情（包含明细）
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getBillDetails(@PathVariable("id") Long id)
    {
        return success(communityBillService.selectCommunityBillWithDetails(id));
    }

    /**
     * 新增小区账单
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:add')")
    @Log(title = "小区账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityBill communityBill)
    {
        return toAjax(communityBillService.insertCommunityBill(communityBill));
    }

    /**
     * 修改小区账单
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:edit')")
    @Log(title = "小区账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityBill communityBill)
    {
        return toAjax(communityBillService.updateCommunityBill(communityBill));
    }

    /**
     * 删除小区账单
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:remove')")
    @Log(title = "小区账单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityBillService.deleteCommunityBillByIds(ids));
    }

    /**
     * 自动生成账单
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:add')")
    @Log(title = "自动生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateBills(@RequestBody Map<String, Object> params)
    {
        try {
            Long communityId = Long.valueOf(params.get("communityId").toString());
            String billDate = params.get("billDate").toString();
            String generateType = params.get("generateType").toString();
            
            String taskId = communityBillService.generateBills(communityId, billDate, generateType);
            AjaxResult result = success("账单生成任务已启动");
            result.put("taskId", taskId);
            return result;
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 查询生成进度
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:list')")
    @GetMapping("/progress/{taskId}")
    public AjaxResult getProgress(@PathVariable("taskId") String taskId)
    {
        BillGenerationProgress progress = communityBillService.getGenerationProgress(taskId);
        if (progress == null) {
            return error("任务不存在");
        }
        return success(progress);
    }

    /**
     * 根据小区ID获取未缴费账单列表（用于批量打印催缴单）
     */
    @PreAuthorize("@ss.hasPermi('system:communityBill:list')")
    @GetMapping("/unpaid/community/{communityId}")
    public AjaxResult getUnpaidBillsByCommunity(@PathVariable("communityId") Long communityId)
    {
        List<Map<String, Object>> bills = communityBillService.selectUnpaidBillsByCommunity(communityId);
        return success(bills);
    }
}
