package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.ParkingInfo;
import com.estatemanagement.system.service.IParkingInfoService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 停车费信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/parkingInfo")
public class ParkingInfoController extends BaseController
{
    @Autowired
    private IParkingInfoService parkingInfoService;

    /**
     * 查询停车费信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(ParkingInfo parkingInfo)
    {
        startPage();
        List<ParkingInfo> list = parkingInfoService.selectParkingInfoList(parkingInfo);
        return getDataTable(list);
    }

    /**
     * 导出停车费信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:export')")
    @Log(title = "停车费信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ParkingInfo parkingInfo)
    {
        List<ParkingInfo> list = parkingInfoService.selectParkingInfoList(parkingInfo);
        ExcelUtil<ParkingInfo> util = new ExcelUtil<ParkingInfo>(ParkingInfo.class);
        util.exportExcel(response, list, "停车费信息数据");
    }

    /**
     * 获取停车费信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(parkingInfoService.selectParkingInfoById(id));
    }

    /**
     * 新增停车费信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:add')")
    @Log(title = "停车费信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ParkingInfo parkingInfo)
    {
        parkingInfo.setCreateBy(getUsername());
        return toAjax(parkingInfoService.insertParkingInfo(parkingInfo));
    }

    /**
     * 修改停车费信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:edit')")
    @Log(title = "停车费信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ParkingInfo parkingInfo)
    {
        parkingInfo.setUpdateBy(getUsername());
        return toAjax(parkingInfoService.updateParkingInfo(parkingInfo));
    }

    /**
     * 删除停车费信息
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:remove')")
    @Log(title = "停车费信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(parkingInfoService.deleteParkingInfoByIds(ids));
    }

    /**
     * 根据小区ID获取有效停车记录列表（用于批量打印催缴单）
     */
    @PreAuthorize("@ss.hasPermi('system:parkingInfo:list')")
    @GetMapping("/unpaid/community/{communityId}")
    public AjaxResult getUnpaidParkingByCommunity(@PathVariable("communityId") Long communityId)
    {
        List<ParkingInfo> list = parkingInfoService.selectValidParkingByCommunity(communityId);
        return success(list);
    }
}
