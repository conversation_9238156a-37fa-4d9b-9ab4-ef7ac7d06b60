package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.CommunityBillDetail;
import com.estatemanagement.system.service.ICommunityBillDetailService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 小区账单明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/communityBillDetail")
public class CommunityBillDetailController extends BaseController
{
    @Autowired
    private ICommunityBillDetailService communityBillDetailService;

    /**
     * 查询小区账单明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityBillDetail communityBillDetail)
    {
        startPage();
        List<CommunityBillDetail> list = communityBillDetailService.selectCommunityBillDetailList(communityBillDetail);
        return getDataTable(list);
    }

    /**
     * 导出小区账单明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:export')")
    @Log(title = "小区账单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityBillDetail communityBillDetail)
    {
        List<CommunityBillDetail> list = communityBillDetailService.selectCommunityBillDetailList(communityBillDetail);
        ExcelUtil<CommunityBillDetail> util = new ExcelUtil<CommunityBillDetail>(CommunityBillDetail.class);
        util.exportExcel(response, list, "小区账单明细数据");
    }

    /**
     * 获取小区账单明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityBillDetailService.selectCommunityBillDetailById(id));
    }

    /**
     * 新增小区账单明细
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:add')")
    @Log(title = "小区账单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityBillDetail communityBillDetail)
    {
        return toAjax(communityBillDetailService.insertCommunityBillDetail(communityBillDetail));
    }

    /**
     * 修改小区账单明细
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:edit')")
    @Log(title = "小区账单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityBillDetail communityBillDetail)
    {
        return toAjax(communityBillDetailService.updateCommunityBillDetail(communityBillDetail));
    }

    /**
     * 删除小区账单明细
     */
    @PreAuthorize("@ss.hasPermi('system:communityBillDetail:remove')")
    @Log(title = "小区账单明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityBillDetailService.deleteCommunityBillDetailByIds(ids));
    }
}
