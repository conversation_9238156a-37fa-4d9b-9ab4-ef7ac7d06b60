package com.estatemanagement.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.estatemanagement.common.annotation.Log;
import com.estatemanagement.common.core.controller.BaseController;
import com.estatemanagement.common.core.domain.AjaxResult;
import com.estatemanagement.common.enums.BusinessType;
import com.estatemanagement.system.domain.FaceAccess;
import com.estatemanagement.system.service.IFaceAccessService;
import com.estatemanagement.common.utils.poi.ExcelUtil;
import com.estatemanagement.common.core.page.TableDataInfo;

/**
 * 人脸识别开通记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/faceAccess")
public class FaceAccessController extends BaseController
{
    @Autowired
    private IFaceAccessService faceAccessService;

    /**
     * 查询人脸识别开通记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:list')")
    @GetMapping("/list")
    public TableDataInfo list(FaceAccess faceAccess)
    {
        startPage();
        List<FaceAccess> list = faceAccessService.selectFaceAccessList(faceAccess);
        return getDataTable(list);
    }

    /**
     * 导出人脸识别开通记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:export')")
    @Log(title = "人脸识别开通记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FaceAccess faceAccess)
    {
        List<FaceAccess> list = faceAccessService.selectFaceAccessList(faceAccess);
        ExcelUtil<FaceAccess> util = new ExcelUtil<FaceAccess>(FaceAccess.class);
        util.exportExcel(response, list, "人脸识别开通记录数据");
    }

    /**
     * 获取人脸识别开通记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(faceAccessService.selectFaceAccessById(id));
    }

    /**
     * 新增人脸识别开通记录
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:add')")
    @Log(title = "人脸识别开通记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FaceAccess faceAccess)
    {
        faceAccess.setCreateBy(getUsername());
        return toAjax(faceAccessService.insertFaceAccess(faceAccess));
    }

    /**
     * 修改人脸识别开通记录
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:edit')")
    @Log(title = "人脸识别开通记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FaceAccess faceAccess)
    {
        faceAccess.setUpdateBy(getUsername());
        return toAjax(faceAccessService.updateFaceAccess(faceAccess));
    }

    /**
     * 删除人脸识别开通记录
     */
    @PreAuthorize("@ss.hasPermi('system:faceAccess:remove')")
    @Log(title = "人脸识别开通记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(faceAccessService.deleteFaceAccessByIds(ids));
    }
}
