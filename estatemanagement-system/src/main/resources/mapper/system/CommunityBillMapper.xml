<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.CommunityBillMapper">
    
    <resultMap type="CommunityBill" id="CommunityBillResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="billNumber"    column="bill_number"    />
        <result property="billPeriodStart"    column="bill_period_start"    />
        <result property="billPeriodEnd"    column="bill_period_end"    />
        <result property="propertyFee"    column="property_fee"    />
        <result property="parkingFee"    column="parking_fee"    />
        <result property="elevatorFee"    column="elevator_fee"    />
        <result property="sanitationFee"    column="sanitation_fee"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="paidAmount"    column="paid_amount"    />
        <result property="paymentStatus"    column="payment_status"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="isTenant"    column="is_tenant"    />
        <result property="managerId"    column="manager_id"    />
        <result property="receiptNumber"    column="receipt_number"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="communityName"    column="community_name"    />
        <result property="buildingNumber"    column="building_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="rentalStatus"    column="rental_status"    />
    </resultMap>

    <sql id="selectCommunityBillVo">
        select cb.id, cb.community_id, cb.owner_id, cb.bill_number, cb.bill_period_start, cb.bill_period_end,
               cb.property_fee, cb.parking_fee, cb.elevator_fee, cb.sanitation_fee, cb.total_amount,
               -- 使用数据库中存储的已支付金额和支付状态
               cb.paid_amount,
               cb.payment_status,
               cb.payment_date, cb.payment_method, cb.is_tenant, cb.manager_id,
               cb.receipt_number, cb.create_by, cb.create_time, cb.update_by, cb.update_time, cb.remark,
               c.community_name, c.community_price, c.owner_parking_fee, c.tenant_parking_fee,
               o.building_number, o.house_number, o.owner_name, o.tenant_name, o.rental_status, o.house_area
        from community_bill cb
        left join community_mangement c on cb.community_id = c.id
        left join owner_mangement o on cb.owner_id = o.id
    </sql>

    <select id="selectCommunityBillList" parameterType="CommunityBill" resultMap="CommunityBillResult">
        <include refid="selectCommunityBillVo"/>
        <where>
            <if test="communityId != null "> and cb.community_id = #{communityId}</if>
            <if test="ownerId != null "> and cb.owner_id = #{ownerId}</if>
            <if test="billNumber != null  and billNumber != ''"> and cb.bill_number = #{billNumber}</if>
            <if test="billPeriodStart != null "> and cb.bill_period_start = #{billPeriodStart}</if>
            <if test="billPeriodEnd != null "> and cb.bill_period_end = #{billPeriodEnd}</if>
            <if test="propertyFee != null "> and cb.property_fee = #{propertyFee}</if>
            <if test="parkingFee != null "> and cb.parking_fee = #{parkingFee}</if>
            <if test="elevatorFee != null "> and cb.elevator_fee = #{elevatorFee}</if>
            <if test="sanitationFee != null "> and cb.sanitation_fee = #{sanitationFee}</if>

            <if test="totalAmount != null "> and cb.total_amount = #{totalAmount}</if>
            <if test="paidAmount != null "> and cb.paid_amount = #{paidAmount}</if>
            <if test="paymentStatus != null "> and cb.payment_status = #{paymentStatus}</if>
            <if test="paymentDate != null "> and cb.payment_date = #{paymentDate}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and cb.payment_method = #{paymentMethod}</if>
            <if test="isTenant != null "> and cb.is_tenant = #{isTenant}</if>
            <if test="managerId != null "> and cb.manager_id = #{managerId}</if>
            <if test="receiptNumber != null  and receiptNumber != ''"> and cb.receipt_number = #{receiptNumber}</if>
        </where>
        ORDER BY
            -- 主要排序：支付状态（未支付优先）
            CASE cb.payment_status
                WHEN 0 THEN 1  -- 未支付排在最前
                WHEN 1 THEN 2  -- 部分支付排在第二
                WHEN 2 THEN 3  -- 已支付排在第三
                WHEN 3 THEN 4  -- 合并状态排在最后
                ELSE 5         -- 其他状态排在最后
            END ASC,
            -- 二级排序：账单周期结束日期倒序（最新的账单在前）
            cb.bill_period_end DESC,
            -- 三级排序：小区名称
            c.community_name ASC,
            -- 四级排序：楼号（数字排序）
            CAST(o.building_number AS UNSIGNED) ASC,
            -- 五级排序：门牌号（数字排序）
            CAST(o.house_number AS UNSIGNED) ASC,
            -- 六级排序：业主姓名
            o.owner_name ASC
    </select>
    
    <select id="selectCommunityBillById" parameterType="Long" resultMap="CommunityBillResult">
        <include refid="selectCommunityBillVo"/>
        where cb.id = #{id}
    </select>

    <!-- 用于修复的查询，不使用实时计算 -->
    <select id="selectCommunityBillListForFix" resultMap="CommunityBillResult">
        select cb.id, cb.community_id, cb.owner_id, cb.bill_number, cb.bill_period_start, cb.bill_period_end,
               cb.property_fee, cb.parking_fee, cb.elevator_fee, cb.sanitation_fee, cb.total_amount,
               cb.paid_amount, cb.payment_status, cb.payment_date, cb.payment_method, cb.is_tenant, cb.manager_id,
               cb.receipt_number, cb.create_by, cb.create_time, cb.update_by, cb.update_time, cb.remark,
               cb.property_payment_status, cb.parking_payment_status, cb.sanitation_payment_status,
               cb.elevator_payment_status, cb.property_paid_amount, cb.parking_paid_amount,
               cb.sanitation_paid_amount, cb.elevator_paid_amount,
               c.community_name, o.building_number, o.house_number, o.owner_name, o.tenant_name, o.rental_status
        from community_bill cb
        left join community_mangement c on cb.community_id = c.id
        left join owner_mangement o on cb.owner_id = o.id
        ORDER BY
            -- 主要排序：支付状态（未支付优先）
            CASE cb.payment_status
                WHEN 0 THEN 1  -- 未支付排在最前
                WHEN 1 THEN 2  -- 部分支付排在第二
                WHEN 2 THEN 3  -- 已支付排在第三
                WHEN 3 THEN 4  -- 合并状态排在最后
                ELSE 5         -- 其他状态排在最后
            END ASC,
            -- 二级排序：账单周期结束日期倒序（最新的账单在前）
            cb.bill_period_end DESC,
            -- 三级排序：小区名称
            c.community_name ASC,
            -- 四级排序：楼号（数字排序）
            CAST(o.building_number AS UNSIGNED) ASC,
            -- 五级排序：门牌号（数字排序）
            CAST(o.house_number AS UNSIGNED) ASC,
            -- 六级排序：业主姓名
            o.owner_name ASC
    </select>

    <insert id="insertCommunityBill" parameterType="CommunityBill" useGeneratedKeys="true" keyProperty="id">
        insert into community_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="billNumber != null">bill_number,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="propertyFee != null">property_fee,</if>
            <if test="parkingFee != null">parking_fee,</if>
            <if test="elevatorFee != null">elevator_fee,</if>
            <if test="sanitationFee != null">sanitation_fee,</if>

            <if test="totalAmount != null">total_amount,</if>
            <if test="paidAmount != null">paid_amount,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="isTenant != null">is_tenant,</if>
            <if test="managerId != null">manager_id,</if>
            <if test="receiptNumber != null">receipt_number,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="billNumber != null">#{billNumber},</if>
            <if test="billPeriodStart != null">#{billPeriodStart},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd},</if>
            <if test="propertyFee != null">#{propertyFee},</if>
            <if test="parkingFee != null">#{parkingFee},</if>
            <if test="elevatorFee != null">#{elevatorFee},</if>
            <if test="sanitationFee != null">#{sanitationFee},</if>

            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="paidAmount != null">#{paidAmount},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="isTenant != null">#{isTenant},</if>
            <if test="managerId != null">#{managerId},</if>
            <if test="receiptNumber != null">#{receiptNumber},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCommunityBill" parameterType="CommunityBill">
        update community_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="billNumber != null">bill_number = #{billNumber},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="propertyFee != null">property_fee = #{propertyFee},</if>
            <if test="parkingFee != null">parking_fee = #{parkingFee},</if>
            <if test="elevatorFee != null">elevator_fee = #{elevatorFee},</if>
            <if test="sanitationFee != null">sanitation_fee = #{sanitationFee},</if>

            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="paidAmount != null">paid_amount = #{paidAmount},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="isTenant != null">is_tenant = #{isTenant},</if>
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="receiptNumber != null">receipt_number = #{receiptNumber},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunityBillById" parameterType="Long">
        delete from community_bill where id = #{id}
    </delete>

    <delete id="deleteCommunityBillByIds" parameterType="String">
        delete from community_bill where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>