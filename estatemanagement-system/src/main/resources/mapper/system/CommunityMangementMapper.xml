<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.CommunityMangementMapper">
    
    <resultMap type="CommunityMangement" id="CommunityMangementResult">
        <result property="id"    column="id"    />
        <result property="communityName"    column="community_name"    />
        <result property="communityPrice"    column="community_price"    />
        <result property="ownerParkingFee"    column="owner_parking_fee"    />
        <result property="tenantParkingFee"    column="tenant_parking_fee"    />
        <result property="elevatorFee"    column="elevator_fee"    />
        <result property="sanitationFeeType"    column="sanitation_fee_type"    />
        <result property="fixedSanitationFee"    column="fixed_sanitation_fee"    />
        <result property="tieredFeeConfig"    column="tiered_fee_config"    />
        <result property="entryDate"    column="entry_date"    />
        <result property="statementDate"    column="statement_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCommunityMangementVo">
        select id, community_name, community_price, owner_parking_fee, tenant_parking_fee, elevator_fee, sanitation_fee_type, fixed_sanitation_fee, tiered_fee_config, entry_date, statement_date, create_by, create_time, update_by, update_time, remark from community_mangement
    </sql>

    <select id="selectCommunityMangementList" parameterType="CommunityMangement" resultMap="CommunityMangementResult">
        <include refid="selectCommunityMangementVo"/>
        <where>  
            <if test="communityName != null  and communityName != ''"> and community_name like concat('%', #{communityName}, '%')</if>
            <if test="communityPrice != null "> and community_price = #{communityPrice}</if>
            <if test="ownerParkingFee != null "> and owner_parking_fee = #{ownerParkingFee}</if>
            <if test="tenantParkingFee != null "> and tenant_parking_fee = #{tenantParkingFee}</if>
            <if test="elevatorFee != null "> and elevator_fee = #{elevatorFee}</if>
            <if test="sanitationFeeType != null "> and sanitation_fee_type = #{sanitationFeeType}</if>
            <if test="fixedSanitationFee != null "> and fixed_sanitation_fee = #{fixedSanitationFee}</if>
            <if test="entryDate != null "> and entry_date = #{entryDate}</if>
            <if test="statementDate != null "> and statement_date = #{statementDate}</if>
        </where>
    </select>
    
    <select id="selectCommunityMangementById" parameterType="Long" resultMap="CommunityMangementResult">
        <include refid="selectCommunityMangementVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommunityMangement" parameterType="CommunityMangement">
        insert into community_mangement
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="communityName != null">community_name,</if>
            <if test="communityPrice != null">community_price,</if>
            <if test="ownerParkingFee != null">owner_parking_fee,</if>
            <if test="tenantParkingFee != null">tenant_parking_fee,</if>
            <if test="elevatorFee != null">elevator_fee,</if>
            <if test="sanitationFeeType != null">sanitation_fee_type,</if>
            <if test="fixedSanitationFee != null">fixed_sanitation_fee,</if>
            <if test="tieredFeeConfig != null and tieredFeeConfig != ''">tiered_fee_config,</if>
            <if test="entryDate != null">entry_date,</if>
            <if test="statementDate != null">statement_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="communityPrice != null">#{communityPrice},</if>
            <if test="ownerParkingFee != null">#{ownerParkingFee},</if>
            <if test="tenantParkingFee != null">#{tenantParkingFee},</if>
            <if test="elevatorFee != null">#{elevatorFee},</if>
            <if test="sanitationFeeType != null">#{sanitationFeeType},</if>
            <if test="fixedSanitationFee != null">#{fixedSanitationFee},</if>
            <if test="tieredFeeConfig != null and tieredFeeConfig != ''">#{tieredFeeConfig},</if>
            <if test="entryDate != null">#{entryDate},</if>
            <if test="statementDate != null">#{statementDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCommunityMangement" parameterType="CommunityMangement">
        update community_mangement
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="communityPrice != null">community_price = #{communityPrice},</if>
            <if test="ownerParkingFee != null">owner_parking_fee = #{ownerParkingFee},</if>
            <if test="tenantParkingFee != null">tenant_parking_fee = #{tenantParkingFee},</if>
            <if test="elevatorFee != null">elevator_fee = #{elevatorFee},</if>
            <if test="sanitationFeeType != null">sanitation_fee_type = #{sanitationFeeType},</if>
            <if test="fixedSanitationFee != null">fixed_sanitation_fee = #{fixedSanitationFee},</if>
            <if test="tieredFeeConfig != null">tiered_fee_config = #{tieredFeeConfig},</if>
            <if test="entryDate != null">entry_date = #{entryDate},</if>
            <if test="statementDate != null">statement_date = #{statementDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunityMangementById" parameterType="Long">
        delete from community_mangement where id = #{id}
    </delete>

    <delete id="deleteCommunityMangementByIds" parameterType="String">
        delete from community_mangement where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>