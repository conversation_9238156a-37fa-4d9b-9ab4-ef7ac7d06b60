<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.PaymentRecordMapper">
    
    <resultMap type="PaymentRecord" id="PaymentRecordResult">
        <result property="id"    column="id"    />
        <result property="billId"    column="bill_id"    />
        <result property="receiptNumber"    column="receipt_number"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="feeType"    column="fee_type"    />
        <result property="paymentMonths"    column="payment_months"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="isPartial"    column="is_partial"    />
        <result property="isAdvance"    column="is_advance"    />
        <result property="propertyFeeAmount"    column="property_fee_amount"    />
        <result property="parkingFeeAmount"    column="parking_fee_amount"    />
        <result property="sanitationFeeAmount"    column="sanitation_fee_amount"    />
        <result property="elevatorFeeAmount"    column="elevator_fee_amount"    />
        <result property="lateFeeAmount"    column="late_fee_amount"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="dueAmount"    column="due_amount"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditorId"    column="auditor_id"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditComment"    column="audit_comment"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="communityName"    column="community_name"    />
        <result property="buildingNumber"    column="building_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="billNumber"    column="bill_number"    />
        <result property="auditorName"    column="auditor_name"    />
    </resultMap>

    <sql id="selectPaymentRecordVo">
        select pr.id, pr.bill_id, pr.receipt_number, pr.payment_amount, pr.payment_method,
               pr.payment_date, pr.operator_id, pr.fee_type, pr.payment_months, pr.payment_days,
               pr.is_partial, pr.is_advance, pr.property_fee_amount, pr.parking_fee_amount,
               pr.sanitation_fee_amount, pr.elevator_fee_amount, pr.late_fee_amount,
               pr.community_id, pr.owner_id, pr.due_amount, pr.audit_status, pr.auditor_id,
               pr.audit_time, pr.audit_comment, pr.remark, pr.create_time,
               cm.community_name, om.building_number, om.house_number, om.owner_name,
               CASE WHEN om.rental_status = '1' AND om.tenant_name IS NOT NULL
                    THEN om.tenant_name ELSE '-' END as tenant_name,
               cb.bill_number,
               au.nick_name as auditor_name
        from payment_record pr
        left join community_mangement cm on pr.community_id = cm.id
        left join owner_mangement om on pr.owner_id = om.id
        left join community_bill cb on pr.bill_id = cb.id
        left join sys_user au on pr.auditor_id = au.user_id
    </sql>

    <sql id="selectPaymentRecordSimpleVo">
        select id, bill_id, receipt_number, payment_amount, payment_method, payment_date,
               operator_id, fee_type, payment_months, payment_days, is_partial, is_advance,
               property_fee_amount, parking_fee_amount, sanitation_fee_amount,
               elevator_fee_amount, late_fee_amount, community_id, owner_id, due_amount,
               audit_status, auditor_id, audit_time, audit_comment, remark, create_time
        from payment_record
    </sql>

    <select id="selectPaymentRecordList" parameterType="PaymentRecord" resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        <where>  
            <if test="billId != null "> and pr.bill_id = #{billId}</if>
            <if test="receiptNumber != null  and receiptNumber != ''"> and pr.receipt_number = #{receiptNumber}</if>
            <if test="paymentAmount != null "> and pr.payment_amount = #{paymentAmount}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and pr.payment_method = #{paymentMethod}</if>
            <if test="paymentDate != null "> and pr.payment_date = #{paymentDate}</if>
            <if test="operatorId != null "> and pr.operator_id = #{operatorId}</if>
            <if test="feeType != null "> and pr.fee_type = #{feeType}</if>
            <if test="communityId != null "> and pr.community_id = #{communityId}</if>
            <if test="ownerId != null "> and pr.owner_id = #{ownerId}</if>
            <if test="isPartial != null "> and pr.is_partial = #{isPartial}</if>
            <if test="isAdvance != null "> and pr.is_advance = #{isAdvance}</if>
            <if test="auditStatus != null "> and pr.audit_status = #{auditStatus}</if>
            <if test="auditorId != null "> and pr.auditor_id = #{auditorId}</if>
        </where>
        order by pr.create_time desc
    </select>
    
    <select id="selectPaymentRecordById" parameterType="Long" resultMap="PaymentRecordResult">
        <include refid="selectPaymentRecordVo"/>
        where pr.id = #{id}
    </select>

    <select id="selectUnpaidBillsForPayment" resultType="java.util.Map">
        select cb.id as bill_id,
               cb.bill_number,
               cb.bill_period_start,
               cb.bill_period_end,
               cb.property_fee,
               cb.parking_fee,
               cb.sanitation_fee,
               cb.elevator_fee,
               cb.late_fee,
               cb.total_amount,
               -- 费用明细信息（用于前端显示）
               CONCAT_WS(',',
                   CASE WHEN cb.property_fee > 0 THEN CONCAT('物业费:', cb.property_fee) END,
                   CASE WHEN cb.parking_fee > 0 THEN CONCAT('停车费:', cb.parking_fee) END,
                   CASE WHEN cb.sanitation_fee > 0 THEN CONCAT('卫生费:', cb.sanitation_fee) END,
                   CASE WHEN cb.elevator_fee > 0 THEN CONCAT('电梯费:', cb.elevator_fee) END,
                   CASE WHEN cb.late_fee > 0 THEN CONCAT('滞纳金:', cb.late_fee) END
               ) as fee_details,
               -- 重新计算实际已支付金额
               COALESCE((
                   SELECT SUM(pr.payment_amount)
                   FROM payment_record pr
                   WHERE pr.bill_id = cb.id
               ), 0) as paid_amount,
               -- 重新计算支付状态
               CASE
                   WHEN COALESCE((
                       SELECT SUM(pr.payment_amount)
                       FROM payment_record pr
                       WHERE pr.bill_id = cb.id
                   ), 0) = 0 THEN 0
                   WHEN COALESCE((
                       SELECT SUM(pr.payment_amount)
                       FROM payment_record pr
                       WHERE pr.bill_id = cb.id
                   ), 0) >= cb.total_amount THEN 2
                   ELSE 1
               END as payment_status,
               -- 各项费用的支付状态也需要重新计算，暂时使用原值
               COALESCE(cb.property_payment_status, 0) as property_payment_status,
               COALESCE(cb.parking_payment_status, 0) as parking_payment_status,
               COALESCE(cb.sanitation_payment_status, 0) as sanitation_payment_status,
               COALESCE(cb.elevator_payment_status, 0) as elevator_payment_status,
               COALESCE(cb.late_payment_status, 0) as late_payment_status,
               cb.property_paid_amount,
               cb.parking_paid_amount,
               cb.sanitation_paid_amount,
               cb.elevator_paid_amount,
               cb.late_paid_amount,
               cm.community_name,
               om.building_number,
               om.house_number,
               om.owner_name,
               CASE WHEN om.rental_status = '1' AND om.tenant_name IS NOT NULL
                    THEN om.tenant_name ELSE '-' END as tenant_name,
               om.rental_status,
               om.community_price_enddate,
               om.public_sanitation_fee_enddate,
               cm.community_price,
               cm.id as community_id,
               om.id as owner_id,
               om.house_area
        from community_bill cb
        left join owner_mangement om on cb.owner_id = om.id
        left join community_mangement cm on cb.community_id = cm.id
        where 1=1
        -- 只查询未完全支付的账单（实时计算）
        AND COALESCE((
            SELECT SUM(pr.payment_amount)
            FROM payment_record pr
            WHERE pr.bill_id = cb.id
        ), 0) &lt; cb.total_amount
        <if test="communityId != null">
            and cb.community_id = #{communityId}
        </if>
        <if test="ownerId != null">
            and cb.owner_id = #{ownerId}
        </if>
        order by cb.create_time desc
    </select>

    <!-- 查询指定账单的收费记录及明细 -->
    <select id="selectPaymentRecordsByBillId" resultType="java.util.Map">
        select pd.id,
               pr.receipt_number,
               pd.payment_amount,
               pr.payment_method,
               pr.payment_date,
               pd.is_advance,
               pd.fee_type,
               pd.payment_months,
               pr.remark,
               u.nick_name as operator_name,
               CASE 
                   WHEN pd.fee_type = 1 THEN '物业费'
                   WHEN pd.fee_type = 2 THEN '停车费'
                   WHEN pd.fee_type = 3 THEN '卫生费'
                   WHEN pd.fee_type = 4 THEN '电梯费'
                   WHEN pd.fee_type = 5 THEN '滞纳金'
                   ELSE '混合费用'
               END as fee_type_name,
               -- 使用payment_detail表中已计算好的缴费周期
               pd.period_start,
               pd.period_end,
               -- 直接使用payment_detail表中的车牌号
               pd.plate_number
        from payment_detail pd
        left join payment_record pr on pd.payment_id = pr.id
        left join sys_user u on pr.operator_id = u.user_id
        where pr.bill_id = #{billId}
        order by pr.payment_date desc, pd.id desc
    </select>

    <insert id="insertPaymentRecord" parameterType="PaymentRecord" useGeneratedKeys="true" keyProperty="id">
        insert into payment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="receiptNumber != null and receiptNumber != ''">receipt_number,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="paymentMonths != null">payment_months,</if>
            <if test="paymentDays != null">payment_days,</if>
            <if test="isPartial != null">is_partial,</if>
            <if test="isAdvance != null">is_advance,</if>
            <if test="propertyFeeAmount != null">property_fee_amount,</if>
            <if test="parkingFeeAmount != null">parking_fee_amount,</if>
            <if test="sanitationFeeAmount != null">sanitation_fee_amount,</if>
            <if test="elevatorFeeAmount != null">elevator_fee_amount,</if>
            <if test="lateFeeAmount != null">late_fee_amount,</if>
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="dueAmount != null">due_amount,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditorId != null">auditor_id,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditComment != null">audit_comment,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId},</if>
            <if test="receiptNumber != null and receiptNumber != ''">#{receiptNumber},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentMethod != null and paymentMethod != ''">#{paymentMethod},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="paymentMonths != null">#{paymentMonths},</if>
            <if test="paymentDays != null">#{paymentDays},</if>
            <if test="isPartial != null">#{isPartial},</if>
            <if test="isAdvance != null">#{isAdvance},</if>
            <if test="propertyFeeAmount != null">#{propertyFeeAmount},</if>
            <if test="parkingFeeAmount != null">#{parkingFeeAmount},</if>
            <if test="sanitationFeeAmount != null">#{sanitationFeeAmount},</if>
            <if test="elevatorFeeAmount != null">#{elevatorFeeAmount},</if>
            <if test="lateFeeAmount != null">#{lateFeeAmount},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="dueAmount != null">#{dueAmount},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditorId != null">#{auditorId},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditComment != null">#{auditComment},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updatePaymentRecord" parameterType="PaymentRecord">
        update payment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="receiptNumber != null and receiptNumber != ''">receipt_number = #{receiptNumber},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null and paymentMethod != ''">payment_method = #{paymentMethod},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="paymentMonths != null">payment_months = #{paymentMonths},</if>
            <if test="paymentDays != null">payment_days = #{paymentDays},</if>
            <if test="isPartial != null">is_partial = #{isPartial},</if>
            <if test="isAdvance != null">is_advance = #{isAdvance},</if>
            <if test="propertyFeeAmount != null">property_fee_amount = #{propertyFeeAmount},</if>
            <if test="parkingFeeAmount != null">parking_fee_amount = #{parkingFeeAmount},</if>
            <if test="sanitationFeeAmount != null">sanitation_fee_amount = #{sanitationFeeAmount},</if>
            <if test="elevatorFeeAmount != null">elevator_fee_amount = #{elevatorFeeAmount},</if>
            <if test="lateFeeAmount != null">late_fee_amount = #{lateFeeAmount},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="dueAmount != null">due_amount = #{dueAmount},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditorId != null">auditor_id = #{auditorId},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditComment != null">audit_comment = #{auditComment},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentRecordById" parameterType="Long">
        delete from payment_record where id = #{id}
    </delete>

    <delete id="deletePaymentRecordByIds" parameterType="String">
        delete from payment_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>