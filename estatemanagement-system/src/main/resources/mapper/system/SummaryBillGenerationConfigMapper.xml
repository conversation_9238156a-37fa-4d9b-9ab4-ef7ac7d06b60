<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.SummaryBillGenerationConfigMapper">
    
    <resultMap type="SummaryBillGenerationConfig" id="SummaryBillGenerationConfigResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="communityName"    column="community_name"    />
        <result property="autoGenerate"    column="auto_generate"    />
        <result property="intervalDays"    column="interval_days"    />
        <result property="lastGenerateTime"    column="last_generate_time"    />
        <result property="nextGenerateTime"    column="next_generate_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSummaryBillGenerationConfigVo">
        select sbgc.id, sbgc.community_id,
               CASE WHEN sbgc.community_id IS NULL THEN '全部小区' ELSE cm.community_name END as community_name,
               sbgc.auto_generate, sbgc.interval_days, sbgc.last_generate_time, sbgc.next_generate_time,
               sbgc.status, sbgc.create_by, sbgc.create_time, sbgc.update_by, sbgc.update_time, sbgc.remark
        from summary_bill_generation_config sbgc
        left join community_mangement cm on sbgc.community_id = cm.id
    </sql>

    <select id="selectSummaryBillGenerationConfigList" parameterType="SummaryBillGenerationConfig" resultMap="SummaryBillGenerationConfigResult">
        <include refid="selectSummaryBillGenerationConfigVo"/>
        <where>  
            <if test="communityId != null "> and community_id = #{communityId}</if>
            <if test="communityName != null  and communityName != ''"> and community_name like concat('%', #{communityName}, '%')</if>
            <if test="autoGenerate != null "> and auto_generate = #{autoGenerate}</if>
            <if test="intervalDays != null "> and interval_days = #{intervalDays}</if>
            <if test="lastGenerateTime != null "> and last_generate_time = #{lastGenerateTime}</if>
            <if test="nextGenerateTime != null "> and next_generate_time = #{nextGenerateTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSummaryBillGenerationConfigById" parameterType="Long" resultMap="SummaryBillGenerationConfigResult">
        <include refid="selectSummaryBillGenerationConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectSummaryBillGenerationConfigByCommunityId" parameterType="Long" resultMap="SummaryBillGenerationConfigResult">
        <include refid="selectSummaryBillGenerationConfigVo"/>
        where community_id = #{communityId}
    </select>
        
    <insert id="insertSummaryBillGenerationConfig" parameterType="SummaryBillGenerationConfig" useGeneratedKeys="true" keyProperty="id">
        insert into summary_bill_generation_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="communityName != null">community_name,</if>
            <if test="autoGenerate != null">auto_generate,</if>
            <if test="intervalDays != null">interval_days,</if>
            <if test="lastGenerateTime != null">last_generate_time,</if>
            <if test="nextGenerateTime != null">next_generate_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="autoGenerate != null">#{autoGenerate},</if>
            <if test="intervalDays != null">#{intervalDays},</if>
            <if test="lastGenerateTime != null">#{lastGenerateTime},</if>
            <if test="nextGenerateTime != null">#{nextGenerateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSummaryBillGenerationConfig" parameterType="SummaryBillGenerationConfig">
        update summary_bill_generation_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="autoGenerate != null">auto_generate = #{autoGenerate},</if>
            <if test="intervalDays != null">interval_days = #{intervalDays},</if>
            <if test="lastGenerateTime != null">last_generate_time = #{lastGenerateTime},</if>
            <if test="nextGenerateTime != null">next_generate_time = #{nextGenerateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSummaryBillGenerationConfigById" parameterType="Long">
        delete from summary_bill_generation_config where id = #{id}
    </delete>

    <delete id="deleteSummaryBillGenerationConfigByIds" parameterType="String">
        delete from summary_bill_generation_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
