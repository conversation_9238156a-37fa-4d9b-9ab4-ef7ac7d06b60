<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.CommunitySummaryBillMapper">
    
    <resultMap type="CommunitySummaryBill" id="CommunitySummaryBillResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="communityName"    column="community_name"    />
        <result property="summaryNumber"    column="summary_number"    />
        <result property="summaryPeriodStart"    column="summary_period_start"    />
        <result property="summaryPeriodEnd"    column="summary_period_end"    />
        <result property="totalPropertyFee"    column="total_property_fee"    />
        <result property="totalParkingFee"    column="total_parking_fee"    />
        <result property="totalElevatorFee"    column="total_elevator_fee"    />
        <result property="totalSanitationFee"    column="total_sanitation_fee"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="actualAmount"    column="actual_amount"    />
        <result property="unpaidAmount"    column="unpaid_amount"    />
        <result property="collectionRate"    column="collection_rate"    />
        <result property="houseCount"    column="house_count"    />
        <result property="paidHouseCount"    column="paid_house_count"    />
        <result property="unpaidHouseCount"    column="unpaid_house_count"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="submitBy"    column="submit_by"    />
        <result property="financeConfirm"    column="finance_confirm"    />
        <result property="financeConfirmDate"    column="finance_confirm_date"    />
        <result property="financeConfirmBy"    column="finance_confirm_by"    />
        <result property="actualReceivedAmount"    column="actual_received_amount"    />
        <result property="receivedDate"    column="received_date"    />
        <result property="receivedBy"    column="received_by"    />
        <result property="receiveDifference"    column="receive_difference"    />
        <result property="receiveRemark"    column="receive_remark"    />
        <result property="receiveStatus"    column="receive_status"    />
        <result property="totalRefundAmount"    column="total_refund_amount"    />
        <result property="propertyRefundAmount"    column="property_refund_amount"    />
        <result property="parkingRefundAmount"    column="parking_refund_amount"    />
        <result property="sanitationRefundAmount"    column="sanitation_refund_amount"    />
        <result property="elevatorRefundAmount"    column="elevator_refund_amount"    />
        <result property="lateFeeRefundAmount"    column="late_fee_refund_amount"    />
        <result property="refundCount"    column="refund_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCommunitySummaryBillVo">
        select csb.id, csb.community_id, cm.community_name, csb.summary_number, csb.summary_period_start, csb.summary_period_end, csb.total_property_fee, csb.total_parking_fee, csb.total_elevator_fee, csb.total_sanitation_fee, csb.total_amount, csb.actual_amount, csb.unpaid_amount, csb.collection_rate, csb.house_count, csb.paid_house_count, csb.unpaid_house_count, csb.submit_status, csb.submit_date, csb.submit_by, csb.finance_confirm, csb.finance_confirm_date, csb.finance_confirm_by, csb.actual_received_amount, csb.received_date, csb.received_by, csb.receive_difference, csb.receive_remark, csb.receive_status, csb.total_refund_amount, csb.property_refund_amount, csb.parking_refund_amount, csb.sanitation_refund_amount, csb.elevator_refund_amount, csb.late_fee_refund_amount, csb.refund_count, csb.create_by, csb.create_time, csb.update_by, csb.update_time, csb.remark
        from community_summary_bill csb
        left join community_mangement cm on csb.community_id = cm.id
    </sql>

    <select id="selectCommunitySummaryBillList" parameterType="CommunitySummaryBill" resultMap="CommunitySummaryBillResult">
        <include refid="selectCommunitySummaryBillVo"/>
        <where>
            <if test="communityId != null "> and csb.community_id = #{communityId}</if>
            <if test="summaryNumber != null  and summaryNumber != ''"> and csb.summary_number = #{summaryNumber}</if>
            <if test="summaryPeriodStart != null "> and csb.summary_period_start = #{summaryPeriodStart}</if>
            <if test="summaryPeriodEnd != null "> and csb.summary_period_end = #{summaryPeriodEnd}</if>
            <if test="totalPropertyFee != null "> and total_property_fee = #{totalPropertyFee}</if>
            <if test="totalParkingFee != null "> and total_parking_fee = #{totalParkingFee}</if>
            <if test="totalElevatorFee != null "> and total_elevator_fee = #{totalElevatorFee}</if>
            <if test="totalSanitationFee != null "> and total_sanitation_fee = #{totalSanitationFee}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="actualAmount != null "> and actual_amount = #{actualAmount}</if>
            <if test="unpaidAmount != null "> and unpaid_amount = #{unpaidAmount}</if>
            <if test="collectionRate != null "> and collection_rate = #{collectionRate}</if>
            <if test="houseCount != null "> and house_count = #{houseCount}</if>
            <if test="paidHouseCount != null "> and paid_house_count = #{paidHouseCount}</if>
            <if test="unpaidHouseCount != null "> and unpaid_house_count = #{unpaidHouseCount}</if>
            <if test="submitStatus != null "> and submit_status = #{submitStatus}</if>
            <if test="submitDate != null "> and submit_date = #{submitDate}</if>
            <if test="submitBy != null  and submitBy != ''"> and submit_by = #{submitBy}</if>
            <if test="financeConfirm != null "> and finance_confirm = #{financeConfirm}</if>
            <if test="financeConfirmDate != null "> and finance_confirm_date = #{financeConfirmDate}</if>
            <if test="financeConfirmBy != null  and financeConfirmBy != ''"> and finance_confirm_by = #{financeConfirmBy}</if>
            <if test="actualReceivedAmount != null "> and actual_received_amount = #{actualReceivedAmount}</if>
            <if test="receivedDate != null "> and received_date = #{receivedDate}</if>
            <if test="receivedBy != null  and receivedBy != ''"> and received_by = #{receivedBy}</if>
            <if test="receiveDifference != null "> and receive_difference = #{receiveDifference}</if>
            <if test="receiveRemark != null  and receiveRemark != ''"> and receive_remark = #{receiveRemark}</if>
            <if test="receiveStatus != null "> and receive_status = #{receiveStatus}</if>
        </where>
    </select>
    
    <select id="selectCommunitySummaryBillById" parameterType="Long" resultMap="CommunitySummaryBillResult">
        <include refid="selectCommunitySummaryBillVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommunitySummaryBill" parameterType="CommunitySummaryBill" useGeneratedKeys="true" keyProperty="id">
        insert into community_summary_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="summaryNumber != null">summary_number,</if>
            <if test="summaryPeriodStart != null">summary_period_start,</if>
            <if test="summaryPeriodEnd != null">summary_period_end,</if>
            <if test="totalPropertyFee != null">total_property_fee,</if>
            <if test="totalParkingFee != null">total_parking_fee,</if>
            <if test="totalElevatorFee != null">total_elevator_fee,</if>
            <if test="totalSanitationFee != null">total_sanitation_fee,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="unpaidAmount != null">unpaid_amount,</if>
            <if test="collectionRate != null">collection_rate,</if>
            <if test="houseCount != null">house_count,</if>
            <if test="paidHouseCount != null">paid_house_count,</if>
            <if test="unpaidHouseCount != null">unpaid_house_count,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="submitDate != null">submit_date,</if>
            <if test="submitBy != null">submit_by,</if>
            <if test="financeConfirm != null">finance_confirm,</if>
            <if test="financeConfirmDate != null">finance_confirm_date,</if>
            <if test="financeConfirmBy != null">finance_confirm_by,</if>
            <if test="actualReceivedAmount != null">actual_received_amount,</if>
            <if test="receivedDate != null">received_date,</if>
            <if test="receivedBy != null">received_by,</if>
            <if test="receiveDifference != null">receive_difference,</if>
            <if test="receiveRemark != null">receive_remark,</if>
            <if test="receiveStatus != null">receive_status,</if>
            <if test="totalRefundAmount != null">total_refund_amount,</if>
            <if test="propertyRefundAmount != null">property_refund_amount,</if>
            <if test="parkingRefundAmount != null">parking_refund_amount,</if>
            <if test="sanitationRefundAmount != null">sanitation_refund_amount,</if>
            <if test="elevatorRefundAmount != null">elevator_refund_amount,</if>
            <if test="lateFeeRefundAmount != null">late_fee_refund_amount,</if>
            <if test="refundCount != null">refund_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="summaryNumber != null">#{summaryNumber},</if>
            <if test="summaryPeriodStart != null">#{summaryPeriodStart},</if>
            <if test="summaryPeriodEnd != null">#{summaryPeriodEnd},</if>
            <if test="totalPropertyFee != null">#{totalPropertyFee},</if>
            <if test="totalParkingFee != null">#{totalParkingFee},</if>
            <if test="totalElevatorFee != null">#{totalElevatorFee},</if>
            <if test="totalSanitationFee != null">#{totalSanitationFee},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="unpaidAmount != null">#{unpaidAmount},</if>
            <if test="collectionRate != null">#{collectionRate},</if>
            <if test="houseCount != null">#{houseCount},</if>
            <if test="paidHouseCount != null">#{paidHouseCount},</if>
            <if test="unpaidHouseCount != null">#{unpaidHouseCount},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="submitDate != null">#{submitDate},</if>
            <if test="submitBy != null">#{submitBy},</if>
            <if test="financeConfirm != null">#{financeConfirm},</if>
            <if test="financeConfirmDate != null">#{financeConfirmDate},</if>
            <if test="financeConfirmBy != null">#{financeConfirmBy},</if>
            <if test="actualReceivedAmount != null">#{actualReceivedAmount},</if>
            <if test="receivedDate != null">#{receivedDate},</if>
            <if test="receivedBy != null">#{receivedBy},</if>
            <if test="receiveDifference != null">#{receiveDifference},</if>
            <if test="receiveRemark != null">#{receiveRemark},</if>
            <if test="receiveStatus != null">#{receiveStatus},</if>
            <if test="totalRefundAmount != null">#{totalRefundAmount},</if>
            <if test="propertyRefundAmount != null">#{propertyRefundAmount},</if>
            <if test="parkingRefundAmount != null">#{parkingRefundAmount},</if>
            <if test="sanitationRefundAmount != null">#{sanitationRefundAmount},</if>
            <if test="elevatorRefundAmount != null">#{elevatorRefundAmount},</if>
            <if test="lateFeeRefundAmount != null">#{lateFeeRefundAmount},</if>
            <if test="refundCount != null">#{refundCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCommunitySummaryBill" parameterType="CommunitySummaryBill">
        update community_summary_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="summaryNumber != null">summary_number = #{summaryNumber},</if>
            <if test="summaryPeriodStart != null">summary_period_start = #{summaryPeriodStart},</if>
            <if test="summaryPeriodEnd != null">summary_period_end = #{summaryPeriodEnd},</if>
            <if test="totalPropertyFee != null">total_property_fee = #{totalPropertyFee},</if>
            <if test="totalParkingFee != null">total_parking_fee = #{totalParkingFee},</if>
            <if test="totalElevatorFee != null">total_elevator_fee = #{totalElevatorFee},</if>
            <if test="totalSanitationFee != null">total_sanitation_fee = #{totalSanitationFee},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="unpaidAmount != null">unpaid_amount = #{unpaidAmount},</if>
            <if test="collectionRate != null">collection_rate = #{collectionRate},</if>
            <if test="houseCount != null">house_count = #{houseCount},</if>
            <if test="paidHouseCount != null">paid_house_count = #{paidHouseCount},</if>
            <if test="unpaidHouseCount != null">unpaid_house_count = #{unpaidHouseCount},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="submitBy != null">submit_by = #{submitBy},</if>
            <if test="financeConfirm != null">finance_confirm = #{financeConfirm},</if>
            <if test="financeConfirmDate != null">finance_confirm_date = #{financeConfirmDate},</if>
            <if test="financeConfirmBy != null">finance_confirm_by = #{financeConfirmBy},</if>
            <if test="actualReceivedAmount != null">actual_received_amount = #{actualReceivedAmount},</if>
            <if test="receivedDate != null">received_date = #{receivedDate},</if>
            <if test="receivedBy != null">received_by = #{receivedBy},</if>
            <if test="receiveDifference != null">receive_difference = #{receiveDifference},</if>
            <if test="receiveRemark != null">receive_remark = #{receiveRemark},</if>
            <if test="receiveStatus != null">receive_status = #{receiveStatus},</if>
            <if test="totalRefundAmount != null">total_refund_amount = #{totalRefundAmount},</if>
            <if test="propertyRefundAmount != null">property_refund_amount = #{propertyRefundAmount},</if>
            <if test="parkingRefundAmount != null">parking_refund_amount = #{parkingRefundAmount},</if>
            <if test="sanitationRefundAmount != null">sanitation_refund_amount = #{sanitationRefundAmount},</if>
            <if test="elevatorRefundAmount != null">elevator_refund_amount = #{elevatorRefundAmount},</if>
            <if test="lateFeeRefundAmount != null">late_fee_refund_amount = #{lateFeeRefundAmount},</if>
            <if test="refundCount != null">refund_count = #{refundCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunitySummaryBillById" parameterType="Long">
        delete from community_summary_bill where id = #{id}
    </delete>

    <delete id="deleteCommunitySummaryBillByIds" parameterType="String">
        delete from community_summary_bill where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>