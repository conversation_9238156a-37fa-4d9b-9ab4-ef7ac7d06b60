<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.ParkingInfoMapper">
    
    <resultMap type="ParkingInfo" id="ParkingInfoResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="plateNumber"    column="plate_number"    />
        <result property="spaceNumber"    column="space_number"    />
        <result property="monthlyFee"    column="monthly_fee"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="status"    column="status"    />
        <result property="isThreeCertificates"    column="is_three_certificates"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="communityName"    column="community_name"    />
        <result property="ownerParkingFee"    column="owner_parking_fee"    />
        <result property="tenantParkingFee"    column="tenant_parking_fee"    />
        <result property="buildingNumber"    column="building_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="rentalStatus"    column="rental_status"    />
        <result property="ownerPhone" column="owner_phone"/>
    </resultMap>

    <sql id="selectParkingInfoVo">
        select p.id, p.community_id, p.owner_id, p.tenant_id, p.plate_number, p.space_number,
               p.monthly_fee, p.start_date, p.end_date, p.status, p.is_three_certificates, p.create_time, p.create_by, p.update_by, p.update_time, p.remark,
               c.community_name, c.owner_parking_fee, c.tenant_parking_fee,
               o.building_number, o.house_number, o.owner_name, o.tenant_name, o.rental_status, o.owner_phone
        from parking_info p
        left join community_mangement c on p.community_id = c.id
        left join owner_mangement o on p.owner_id = o.id
    </sql>

    <select id="selectParkingInfoList" parameterType="ParkingInfo" resultMap="ParkingInfoResult">
        <include refid="selectParkingInfoVo"/>
        <where>
            <!-- 默认只显示有车牌的记录 -->
            and p.plate_number is not null and p.plate_number != ''
            <if test="communityId != null "> and p.community_id = #{communityId}</if>
            <if test="ownerId != null "> and p.owner_id = #{ownerId}</if>
            <if test="tenantId != null "> and p.tenant_id = #{tenantId}</if>
            <if test="plateNumber != null  and plateNumber != ''"> and p.plate_number like concat('%', #{plateNumber}, '%')</if>
            <if test="isThreeCertificates != null "> and p.is_three_certificates = #{isThreeCertificates}</if>
            <if test="monthlyFee != null "> and p.monthly_fee = #{monthlyFee}</if>
            <if test="startDate != null "> and p.start_date = #{startDate}</if>
            <if test="endDate != null "> and p.end_date = #{endDate}</if>
            <if test="status != null "> and p.status = #{status}</if>
            <if test="communityName != null and communityName != ''"> and c.community_name like concat('%', #{communityName}, '%')</if>
            <if test="buildingNumber != null and buildingNumber != ''"> and o.building_number like concat('%', #{buildingNumber}, '%')</if>
            <if test="houseNumber != null and houseNumber != ''"> and o.house_number like concat('%', #{houseNumber}, '%')</if>
            <if test="ownerName != null and ownerName != ''"> and (o.owner_name like concat('%', #{ownerName}, '%') or o.tenant_name like concat('%', #{ownerName}, '%'))</if>
            <if test="remark != null and remark != ''"> and p.remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectParkingManagementList" parameterType="ParkingInfo" resultMap="ParkingInfoResult">
        <include refid="selectParkingInfoVo"/>
        <where>
            <!-- 车位管理：显示所有车位，包括空车位 -->
            <if test="communityId != null "> and p.community_id = #{communityId}</if>
            <if test="ownerId != null "> and p.owner_id = #{ownerId}</if>
            <if test="tenantId != null "> and p.tenant_id = #{tenantId}</if>
            <if test="plateNumber != null  and plateNumber != ''"> and p.plate_number like concat('%', #{plateNumber}, '%')</if>
            <if test="spaceNumber != null  and spaceNumber != ''"> and p.space_number like concat('%', #{spaceNumber}, '%')</if>
            <if test="isThreeCertificates != null "> and p.is_three_certificates = #{isThreeCertificates}</if>
            <if test="monthlyFee != null "> and p.monthly_fee = #{monthlyFee}</if>
            <if test="startDate != null "> and p.start_date = #{startDate}</if>
            <if test="endDate != null "> and p.end_date = #{endDate}</if>
            <if test="status != null "> and p.status = #{status}</if>
            <if test="communityName != null and communityName != ''"> and c.community_name like concat('%', #{communityName}, '%')</if>
            <if test="buildingNumber != null and buildingNumber != ''"> and o.building_number like concat('%', #{buildingNumber}, '%')</if>
            <if test="houseNumber != null and houseNumber != ''"> and o.house_number like concat('%', #{houseNumber}, '%')</if>
            <if test="ownerName != null and ownerName != ''"> and (o.owner_name like concat('%', #{ownerName}, '%') or o.tenant_name like concat('%', #{ownerName}, '%'))</if>
            <if test="remark != null and remark != ''"> and p.remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectParkingInfoById" parameterType="Long" resultMap="ParkingInfoResult">
        <include refid="selectParkingInfoVo"/>
        where p.id = #{id}
    </select>

    <insert id="insertParkingInfo" parameterType="ParkingInfo" useGeneratedKeys="true" keyProperty="id">
        insert into parking_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="plateNumber != null">plate_number,</if>
            <if test="spaceNumber != null">space_number,</if>
            <if test="monthlyFee != null">monthly_fee,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="status != null">status,</if>
            <if test="isThreeCertificates != null">is_three_certificates,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="plateNumber != null">#{plateNumber},</if>
            <if test="spaceNumber != null">#{spaceNumber},</if>
            <if test="monthlyFee != null">#{monthlyFee},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="status != null">#{status},</if>
            <if test="isThreeCertificates != null">#{isThreeCertificates},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateParkingInfo" parameterType="ParkingInfo">
        update parking_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="plateNumber != null">plate_number = #{plateNumber},</if>
            <if test="spaceNumber != null">space_number = #{spaceNumber},</if>
            <if test="monthlyFee != null">monthly_fee = #{monthlyFee},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isThreeCertificates != null">is_three_certificates = #{isThreeCertificates},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 强制更新停车费信息（包括null值字段），用于车位释放等场景 -->
    <update id="forceUpdateParkingInfo" parameterType="ParkingInfo">
        update parking_info
        set community_id = #{communityId},
            owner_id = #{ownerId},
            tenant_id = #{tenantId},
            plate_number = #{plateNumber},
            space_number = #{spaceNumber},
            monthly_fee = #{monthlyFee},
            start_date = #{startDate},
            end_date = #{endDate},
            status = #{status},
            is_three_certificates = #{isThreeCertificates},
            create_time = #{createTime},
            create_by = #{createBy},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark}
        where id = #{id}
    </update>

    <delete id="deleteParkingInfoById" parameterType="Long">
        delete from parking_info where id = #{id}
    </delete>

    <delete id="deleteParkingInfoByIds" parameterType="String">
        delete from parking_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据小区ID查询有效停车记录（有车牌号和车位号的记录） -->
    <select id="selectValidParkingByCommunity" parameterType="Long" resultMap="ParkingInfoResult">
        <include refid="selectParkingInfoVo"/>
        where p.community_id = #{communityId}
        and p.plate_number is not null
        and p.plate_number != ''
        and p.space_number is not null
        and p.space_number != ''
        order by p.space_number asc
    </select>
</mapper>