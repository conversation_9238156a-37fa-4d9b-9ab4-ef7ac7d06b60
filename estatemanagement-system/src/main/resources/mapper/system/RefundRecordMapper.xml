<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.RefundRecordMapper">
    
    <resultMap type="RefundRecord" id="RefundRecordResult">
        <result property="id"    column="id"    />
        <result property="refundNumber"    column="refund_number"    />
        <result property="originalPaymentId"    column="original_payment_id"    />
        <result property="originalReceiptNumber"    column="original_receipt_number"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="refundType"    column="refund_type"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="totalRefundAmount"    column="total_refund_amount"    />
        <result property="propertyFeeAmount"    column="property_fee_amount"    />
        <result property="parkingFeeAmount"    column="parking_fee_amount"    />
        <result property="sanitationFeeAmount"    column="sanitation_fee_amount"    />
        <result property="elevatorFeeAmount"    column="elevator_fee_amount"    />
        <result property="lateFeeAmount"    column="late_fee_amount"    />
        <result property="refundMethod"    column="refund_method"    />
        <result property="refundDate"    column="refund_date"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="status"    column="status"    />
        <result property="auditUserId"    column="audit_user_id"    />
        <result property="auditUserName"    column="audit_user_name"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRefundRecordVo">
        select id, refund_number, original_payment_id, original_receipt_number, community_id, owner_id, refund_type, refund_reason, total_refund_amount, property_fee_amount, parking_fee_amount, sanitation_fee_amount, elevator_fee_amount, late_fee_amount, refund_method, refund_date, operator_id, operator_name, status, audit_user_id, audit_user_name, audit_time, audit_remark, remark, create_by, create_time, update_by, update_time from refund_record
    </sql>

    <select id="selectRefundRecordList" parameterType="RefundRecord" resultMap="RefundRecordResult">
        <include refid="selectRefundRecordVo"/>
        <where>  
            <if test="refundNumber != null  and refundNumber != ''"> and refund_number like concat('%', #{refundNumber}, '%')</if>
            <if test="originalPaymentId != null "> and original_payment_id = #{originalPaymentId}</if>
            <if test="originalReceiptNumber != null  and originalReceiptNumber != ''"> and original_receipt_number like concat('%', #{originalReceiptNumber}, '%')</if>
            <if test="communityId != null "> and community_id = #{communityId}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
            <if test="refundType != null "> and refund_type = #{refundType}</if>
            <if test="refundReason != null  and refundReason != ''"> and refund_reason like concat('%', #{refundReason}, '%')</if>
            <if test="refundMethod != null  and refundMethod != ''"> and refund_method = #{refundMethod}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="params.beginRefundDate != null and params.beginRefundDate != ''"><!-- 开始退费日期 -->
                and date_format(refund_date,'%y%m%d') &gt;= date_format(#{params.beginRefundDate},'%y%m%d')
            </if>
            <if test="params.endRefundDate != null and params.endRefundDate != ''"><!-- 结束退费日期 -->
                and date_format(refund_date,'%y%m%d') &lt;= date_format(#{params.endRefundDate},'%y%m%d')
            </if>
        </where>
        order by refund_date desc
    </select>
    
    <select id="selectRefundRecordById" parameterType="Long" resultMap="RefundRecordResult">
        <include refid="selectRefundRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectRefundRecordListWithDetails" parameterType="RefundRecord" resultType="java.util.Map">
        select rr.id,
               rr.refund_number,
               rr.original_payment_id,
               rr.original_receipt_number,
               rr.community_id,
               rr.owner_id,
               rr.refund_type,
               rr.refund_reason,
               rr.total_refund_amount,
               rr.property_fee_amount,
               rr.parking_fee_amount,
               rr.sanitation_fee_amount,
               rr.elevator_fee_amount,
               rr.late_fee_amount,
               rr.refund_method,
               rr.refund_date,
               rr.operator_name,
               rr.status,
               rr.audit_user_name,
               rr.audit_time,
               rr.audit_remark,
               rr.remark,
               -- 关联信息
               cm.community_name,
               om.owner_name,
               om.house_number,
               pr.receipt_number as original_receipt_number,
               pr.payment_date as original_payment_date,
               pr.payment_amount as original_payment_amount
        from refund_record rr
        left join community_mangement cm on rr.community_id = cm.id
        left join owner_mangement om on rr.owner_id = om.id
        left join payment_record pr on rr.original_payment_id = pr.id
        <where>  
            <if test="refundNumber != null  and refundNumber != ''"> and rr.refund_number like concat('%', #{refundNumber}, '%')</if>
            <if test="originalPaymentId != null "> and rr.original_payment_id = #{originalPaymentId}</if>
            <if test="originalReceiptNumber != null  and originalReceiptNumber != ''"> and rr.original_receipt_number like concat('%', #{originalReceiptNumber}, '%')</if>
            <if test="communityId != null "> and rr.community_id = #{communityId}</if>
            <if test="ownerId != null "> and rr.owner_id = #{ownerId}</if>
            <if test="refundType != null "> and rr.refund_type = #{refundType}</if>
            <if test="status != null "> and rr.status = #{status}</if>
            <if test="params.beginRefundDate != null and params.beginRefundDate != ''">
                and date_format(rr.refund_date,'%y%m%d') &gt;= date_format(#{params.beginRefundDate},'%y%m%d')
            </if>
            <if test="params.endRefundDate != null and params.endRefundDate != ''">
                and date_format(rr.refund_date,'%y%m%d') &lt;= date_format(#{params.endRefundDate},'%y%m%d')
            </if>
        </where>
        order by rr.refund_date desc
    </select>

    <select id="selectRefundRecordsByOriginalPaymentId" parameterType="Long" resultMap="RefundRecordResult">
        <include refid="selectRefundRecordVo"/>
        where original_payment_id = #{originalPaymentId}
        order by refund_date desc
    </select>

    <select id="selectRefundRecordByNumber" parameterType="String" resultMap="RefundRecordResult">
        <include refid="selectRefundRecordVo"/>
        where refund_number = #{refundNumber}
    </select>

    <select id="selectRefundStatistics" parameterType="java.util.Map" resultType="java.util.Map">
        select 
            count(*) as total_count,
            coalesce(sum(total_refund_amount), 0) as total_refund_amount,
            coalesce(sum(property_fee_amount), 0) as total_property_fee_amount,
            coalesce(sum(parking_fee_amount), 0) as total_parking_fee_amount,
            coalesce(sum(sanitation_fee_amount), 0) as total_sanitation_fee_amount,
            coalesce(sum(elevator_fee_amount), 0) as total_elevator_fee_amount,
            coalesce(sum(late_fee_amount), 0) as total_late_fee_amount
        from refund_record
        <where>
            <if test="communityId != null"> and community_id = #{communityId}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="beginDate != null and beginDate != ''">
                and date_format(refund_date,'%y%m%d') &gt;= date_format(#{beginDate},'%y%m%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and date_format(refund_date,'%y%m%d') &lt;= date_format(#{endDate},'%y%m%d')
            </if>
        </where>
    </select>
        
    <insert id="insertRefundRecord" parameterType="RefundRecord" useGeneratedKeys="true" keyProperty="id">
        insert into refund_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refundNumber != null and refundNumber != ''">refund_number,</if>
            <if test="originalPaymentId != null">original_payment_id,</if>
            <if test="originalReceiptNumber != null">original_receipt_number,</if>
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="refundType != null">refund_type,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="totalRefundAmount != null">total_refund_amount,</if>
            <if test="propertyFeeAmount != null">property_fee_amount,</if>
            <if test="parkingFeeAmount != null">parking_fee_amount,</if>
            <if test="sanitationFeeAmount != null">sanitation_fee_amount,</if>
            <if test="elevatorFeeAmount != null">elevator_fee_amount,</if>
            <if test="lateFeeAmount != null">late_fee_amount,</if>
            <if test="refundMethod != null">refund_method,</if>
            <if test="refundDate != null">refund_date,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="status != null">status,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditUserName != null">audit_user_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refundNumber != null and refundNumber != ''">#{refundNumber},</if>
            <if test="originalPaymentId != null">#{originalPaymentId},</if>
            <if test="originalReceiptNumber != null">#{originalReceiptNumber},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="refundType != null">#{refundType},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="totalRefundAmount != null">#{totalRefundAmount},</if>
            <if test="propertyFeeAmount != null">#{propertyFeeAmount},</if>
            <if test="parkingFeeAmount != null">#{parkingFeeAmount},</if>
            <if test="sanitationFeeAmount != null">#{sanitationFeeAmount},</if>
            <if test="elevatorFeeAmount != null">#{elevatorFeeAmount},</if>
            <if test="lateFeeAmount != null">#{lateFeeAmount},</if>
            <if test="refundMethod != null">#{refundMethod},</if>
            <if test="refundDate != null">#{refundDate},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="status != null">#{status},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditUserName != null">#{auditUserName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRefundRecord" parameterType="RefundRecord">
        update refund_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="refundNumber != null and refundNumber != ''">refund_number = #{refundNumber},</if>
            <if test="originalPaymentId != null">original_payment_id = #{originalPaymentId},</if>
            <if test="originalReceiptNumber != null">original_receipt_number = #{originalReceiptNumber},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="refundType != null">refund_type = #{refundType},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="totalRefundAmount != null">total_refund_amount = #{totalRefundAmount},</if>
            <if test="propertyFeeAmount != null">property_fee_amount = #{propertyFeeAmount},</if>
            <if test="parkingFeeAmount != null">parking_fee_amount = #{parkingFeeAmount},</if>
            <if test="sanitationFeeAmount != null">sanitation_fee_amount = #{sanitationFeeAmount},</if>
            <if test="elevatorFeeAmount != null">elevator_fee_amount = #{elevatorFeeAmount},</if>
            <if test="lateFeeAmount != null">late_fee_amount = #{lateFeeAmount},</if>
            <if test="refundMethod != null">refund_method = #{refundMethod},</if>
            <if test="refundDate != null">refund_date = #{refundDate},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditUserName != null">audit_user_name = #{auditUserName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="auditRefundRecord">
        update refund_record 
        set status = #{status},
            audit_user_id = #{auditUserId},
            audit_user_name = #{auditUserName},
            audit_time = now(),
            audit_remark = #{auditRemark}
        where id = #{id}
    </update>

    <delete id="deleteRefundRecordById" parameterType="Long">
        delete from refund_record where id = #{id}
    </delete>

    <delete id="deleteRefundRecordByIds" parameterType="String">
        delete from refund_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
