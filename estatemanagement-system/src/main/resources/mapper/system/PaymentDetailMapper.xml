<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.PaymentDetailMapper">
    
    <resultMap type="PaymentDetail" id="PaymentDetailResult">
        <result property="id"    column="id"    />
        <result property="paymentId"    column="payment_id"    />
        <result property="feeType"    column="fee_type"    />
        <result property="feeName"    column="fee_name"    />
        <result property="paymentAmount"    column="payment_amount"    />
        <result property="paymentMonths"    column="payment_months"    />
        <result property="paymentDays"    column="payment_days"    />
        <result property="billDetailId"    column="bill_detail_id"    />
        <result property="isAdvance"    column="is_advance"    />
        <result property="periodStart"    column="period_start"    />
        <result property="periodEnd"    column="period_end"    />
        <result property="plateNumber"    column="plate_number"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPaymentDetailVo">
        select id, payment_id, fee_type, fee_name, payment_amount, payment_months, payment_days, bill_detail_id, is_advance, period_start, period_end, plate_number, create_time from payment_detail
    </sql>

    <select id="selectPaymentDetailList" parameterType="PaymentDetail" resultMap="PaymentDetailResult">
        <include refid="selectPaymentDetailVo"/>
        <where>  
            <if test="paymentId != null "> and payment_id = #{paymentId}</if>
            <if test="feeType != null "> and fee_type = #{feeType}</if>
            <if test="feeName != null  and feeName != ''"> and fee_name like concat('%', #{feeName}, '%')</if>
            <if test="paymentAmount != null "> and payment_amount = #{paymentAmount}</if>
            <if test="paymentMonths != null "> and payment_months = #{paymentMonths}</if>
            <if test="paymentDays != null "> and payment_days = #{paymentDays}</if>
            <if test="billDetailId != null "> and bill_detail_id = #{billDetailId}</if>
            <if test="isAdvance != null "> and is_advance = #{isAdvance}</if>
            <if test="periodStart != null "> and period_start = #{periodStart}</if>
            <if test="periodEnd != null "> and period_end = #{periodEnd}</if>
            <if test="plateNumber != null  and plateNumber != ''"> and plate_number = #{plateNumber}</if>
        </where>
    </select>
    
    <select id="selectPaymentDetailById" parameterType="Long" resultMap="PaymentDetailResult">
        <include refid="selectPaymentDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertPaymentDetail" parameterType="PaymentDetail" useGeneratedKeys="true" keyProperty="id">
        insert into payment_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="paymentId != null">payment_id,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="feeName != null and feeName != ''">fee_name,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="paymentMonths != null">payment_months,</if>
            <if test="paymentDays != null">payment_days,</if>
            <if test="billDetailId != null">bill_detail_id,</if>
            <if test="isAdvance != null">is_advance,</if>
            <if test="periodStart != null">period_start,</if>
            <if test="periodEnd != null">period_end,</if>
            <if test="plateNumber != null and plateNumber != ''">plate_number,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="paymentId != null">#{paymentId},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="feeName != null and feeName != ''">#{feeName},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="paymentMonths != null">#{paymentMonths},</if>
            <if test="paymentDays != null">#{paymentDays},</if>
            <if test="billDetailId != null">#{billDetailId},</if>
            <if test="isAdvance != null">#{isAdvance},</if>
            <if test="periodStart != null">#{periodStart},</if>
            <if test="periodEnd != null">#{periodEnd},</if>
            <if test="plateNumber != null and plateNumber != ''">#{plateNumber},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updatePaymentDetail" parameterType="PaymentDetail">
        update payment_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="paymentId != null">payment_id = #{paymentId},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="feeName != null and feeName != ''">fee_name = #{feeName},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMonths != null">payment_months = #{paymentMonths},</if>
            <if test="paymentDays != null">payment_days = #{paymentDays},</if>
            <if test="billDetailId != null">bill_detail_id = #{billDetailId},</if>
            <if test="isAdvance != null">is_advance = #{isAdvance},</if>
            <if test="periodStart != null">period_start = #{periodStart},</if>
            <if test="periodEnd != null">period_end = #{periodEnd},</if>
            <if test="plateNumber != null">plate_number = #{plateNumber},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaymentDetailById" parameterType="Long">
        delete from payment_detail where id = #{id}
    </delete>

    <delete id="deletePaymentDetailByIds" parameterType="String">
        delete from payment_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据账单ID和费用类型查询收费明细 -->
    <select id="selectPaymentDetailsByBillAndFeeType" resultType="java.util.Map">
        select pd.payment_amount
        from payment_detail pd
        left join payment_record pr on pd.payment_id = pr.id
        where pr.bill_id = #{billId} and pd.fee_type = #{feeType}
    </select>

    <!-- 根据收费记录ID查询收费明细 -->
    <select id="selectPaymentDetailsByPaymentId" resultType="java.util.Map">
        select pd.id,
               pd.fee_type,
               pd.fee_name as fee_type_name,
               pd.payment_amount,
               pd.payment_months,
               pd.payment_days,
               pd.is_advance,
               pd.period_start,
               pd.period_end,
               pd.plate_number
        from payment_detail pd
        left join payment_record pr on pd.payment_id = pr.id
        where pd.payment_id = #{paymentId}
        order by pd.id
    </select>

    <!-- 查询业主某个费用类型的最后缴费记录结束时间 -->
    <select id="selectLastPaymentEndDateByOwnerAndFeeType" resultType="java.util.Date">
        select pd.period_end
        from payment_detail pd
        left join payment_record pr on pd.payment_id = pr.id
        where pr.owner_id = #{ownerId}
          and pd.fee_type = #{feeType}
          and pd.period_end is not null
          <if test="plateNumber != null and plateNumber != ''">
              <!-- 停车费需要匹配车牌号 -->
              and pd.plate_number = #{plateNumber}
          </if>
        order by pd.period_end desc
        limit 1
    </select>
</mapper>