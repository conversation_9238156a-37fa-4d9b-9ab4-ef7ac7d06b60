<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.RefundDetailMapper">
    
    <resultMap type="RefundDetail" id="RefundDetailResult">
        <result property="id"    column="id"    />
        <result property="refundId"    column="refund_id"    />
        <result property="originalPaymentDetailId"    column="original_payment_detail_id"    />
        <result property="feeType"    column="fee_type"    />
        <result property="feeName"    column="fee_name"    />
        <result property="originalPaymentAmount"    column="original_payment_amount"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundMonths"    column="refund_months"    />
        <result property="refundDays"    column="refund_days"    />
        <result property="originalPeriodStart"    column="original_period_start"    />
        <result property="originalPeriodEnd"    column="original_period_end"    />
        <result property="refundPeriodStart"    column="refund_period_start"    />
        <result property="refundPeriodEnd"    column="refund_period_end"    />
        <result property="remainingPeriodStart"    column="remaining_period_start"    />
        <result property="remainingPeriodEnd"    column="remaining_period_end"    />
        <result property="plateNumber"    column="plate_number"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectRefundDetailVo">
        select id, refund_id, original_payment_detail_id, fee_type, fee_name, original_payment_amount, refund_amount, refund_months, refund_days, original_period_start, original_period_end, refund_period_start, refund_period_end, remaining_period_start, remaining_period_end, plate_number, remark, create_time from refund_detail
    </sql>

    <select id="selectRefundDetailList" parameterType="RefundDetail" resultMap="RefundDetailResult">
        <include refid="selectRefundDetailVo"/>
        <where>  
            <if test="refundId != null "> and refund_id = #{refundId}</if>
            <if test="originalPaymentDetailId != null "> and original_payment_detail_id = #{originalPaymentDetailId}</if>
            <if test="feeType != null "> and fee_type = #{feeType}</if>
            <if test="feeName != null  and feeName != ''"> and fee_name like concat('%', #{feeName}, '%')</if>
            <if test="plateNumber != null  and plateNumber != ''"> and plate_number like concat('%', #{plateNumber}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectRefundDetailById" parameterType="Long" resultMap="RefundDetailResult">
        <include refid="selectRefundDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectRefundDetailsByRefundId" parameterType="Long" resultType="java.util.Map">
        select rd.id,
               rd.refund_id,
               rd.original_payment_detail_id,
               rd.fee_type,
               rd.fee_name,
               rd.original_payment_amount,
               rd.refund_amount,
               rd.refund_months,
               rd.refund_days,
               rd.original_period_start,
               rd.original_period_end,
               rd.refund_period_start,
               rd.refund_period_end,
               rd.remaining_period_start,
               rd.remaining_period_end,
               rd.plate_number,
               rd.remark,
               -- 原收费明细信息
               pd.payment_amount as original_detail_amount,
               pd.payment_months as original_detail_months,
               pd.period_start as original_detail_period_start,
               pd.period_end as original_detail_period_end
        from refund_detail rd
        left join payment_detail pd on rd.original_payment_detail_id = pd.id
        where rd.refund_id = #{refundId}
        order by rd.fee_type, rd.create_time
    </select>

    <select id="selectRefundDetailsByOriginalPaymentDetailId" parameterType="Long" resultMap="RefundDetailResult">
        <include refid="selectRefundDetailVo"/>
        where original_payment_detail_id = #{originalPaymentDetailId}
        order by create_time desc
    </select>
        
    <insert id="insertRefundDetail" parameterType="RefundDetail" useGeneratedKeys="true" keyProperty="id">
        insert into refund_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refundId != null">refund_id,</if>
            <if test="originalPaymentDetailId != null">original_payment_detail_id,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="feeName != null and feeName != ''">fee_name,</if>
            <if test="originalPaymentAmount != null">original_payment_amount,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundMonths != null">refund_months,</if>
            <if test="refundDays != null">refund_days,</if>
            <if test="originalPeriodStart != null">original_period_start,</if>
            <if test="originalPeriodEnd != null">original_period_end,</if>
            <if test="refundPeriodStart != null">refund_period_start,</if>
            <if test="refundPeriodEnd != null">refund_period_end,</if>
            <if test="remainingPeriodStart != null">remaining_period_start,</if>
            <if test="remainingPeriodEnd != null">remaining_period_end,</if>
            <if test="plateNumber != null">plate_number,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refundId != null">#{refundId},</if>
            <if test="originalPaymentDetailId != null">#{originalPaymentDetailId},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="feeName != null and feeName != ''">#{feeName},</if>
            <if test="originalPaymentAmount != null">#{originalPaymentAmount},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundMonths != null">#{refundMonths},</if>
            <if test="refundDays != null">#{refundDays},</if>
            <if test="originalPeriodStart != null">#{originalPeriodStart},</if>
            <if test="originalPeriodEnd != null">#{originalPeriodEnd},</if>
            <if test="refundPeriodStart != null">#{refundPeriodStart},</if>
            <if test="refundPeriodEnd != null">#{refundPeriodEnd},</if>
            <if test="remainingPeriodStart != null">#{remainingPeriodStart},</if>
            <if test="remainingPeriodEnd != null">#{remainingPeriodEnd},</if>
            <if test="plateNumber != null">#{plateNumber},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRefundDetail" parameterType="RefundDetail">
        update refund_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="originalPaymentDetailId != null">original_payment_detail_id = #{originalPaymentDetailId},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="feeName != null and feeName != ''">fee_name = #{feeName},</if>
            <if test="originalPaymentAmount != null">original_payment_amount = #{originalPaymentAmount},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundMonths != null">refund_months = #{refundMonths},</if>
            <if test="refundDays != null">refund_days = #{refundDays},</if>
            <if test="originalPeriodStart != null">original_period_start = #{originalPeriodStart},</if>
            <if test="originalPeriodEnd != null">original_period_end = #{originalPeriodEnd},</if>
            <if test="refundPeriodStart != null">refund_period_start = #{refundPeriodStart},</if>
            <if test="refundPeriodEnd != null">refund_period_end = #{refundPeriodEnd},</if>
            <if test="remainingPeriodStart != null">remaining_period_start = #{remainingPeriodStart},</if>
            <if test="remainingPeriodEnd != null">remaining_period_end = #{remainingPeriodEnd},</if>
            <if test="plateNumber != null">plate_number = #{plateNumber},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRefundDetailById" parameterType="Long">
        delete from refund_detail where id = #{id}
    </delete>

    <delete id="deleteRefundDetailByIds" parameterType="String">
        delete from refund_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRefundDetailByRefundId" parameterType="Long">
        delete from refund_detail where refund_id = #{refundId}
    </delete>
</mapper>
