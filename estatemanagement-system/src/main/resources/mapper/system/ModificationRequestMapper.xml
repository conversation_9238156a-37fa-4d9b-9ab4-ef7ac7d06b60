<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.ModificationRequestMapper">
    
    <resultMap type="ModificationRequest" id="ModificationRequestResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="requestType"    column="request_type"    />
        <result property="requestContent"    column="request_content"    />
        <result property="requestBy"    column="request_by"    />
        <result property="requestTime"    column="request_time"    />
        <result property="status"    column="status"    />
        <result property="processBy"    column="process_by"    />
        <result property="processTime"    column="process_time"    />
        <result property="processResult"    column="process_result"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectModificationRequestVo">
        select id, community_id, owner_id, request_type, request_content, request_by, request_time, status, process_by, process_time, process_result, create_time, update_time, create_by, update_by, remark from modification_request
    </sql>

    <select id="selectModificationRequestList" parameterType="ModificationRequest" resultMap="ModificationRequestResult">
        <include refid="selectModificationRequestVo"/>
        <where>  
            <if test="communityId != null "> and community_id = #{communityId}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
            <if test="requestType != null "> and request_type = #{requestType}</if>
            <if test="requestContent != null  and requestContent != ''"> and request_content = #{requestContent}</if>
            <if test="requestBy != null  and requestBy != ''"> and request_by = #{requestBy}</if>
            <if test="requestTime != null "> and request_time = #{requestTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="processBy != null  and processBy != ''"> and process_by = #{processBy}</if>
            <if test="processTime != null "> and process_time = #{processTime}</if>
            <if test="processResult != null  and processResult != ''"> and process_result = #{processResult}</if>
        </where>
    </select>
    
    <select id="selectModificationRequestById" parameterType="Long" resultMap="ModificationRequestResult">
        <include refid="selectModificationRequestVo"/>
        where id = #{id}
    </select>

    <insert id="insertModificationRequest" parameterType="ModificationRequest" useGeneratedKeys="true" keyProperty="id">
        insert into modification_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="requestType != null">request_type,</if>
            <if test="requestContent != null">request_content,</if>
            <if test="requestBy != null">request_by,</if>
            <if test="requestTime != null">request_time,</if>
            <if test="status != null">status,</if>
            <if test="processBy != null">process_by,</if>
            <if test="processTime != null">process_time,</if>
            <if test="processResult != null">process_result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="requestType != null">#{requestType},</if>
            <if test="requestContent != null">#{requestContent},</if>
            <if test="requestBy != null">#{requestBy},</if>
            <if test="requestTime != null">#{requestTime},</if>
            <if test="status != null">#{status},</if>
            <if test="processBy != null">#{processBy},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="processResult != null">#{processResult},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateModificationRequest" parameterType="ModificationRequest">
        update modification_request
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="requestType != null">request_type = #{requestType},</if>
            <if test="requestContent != null">request_content = #{requestContent},</if>
            <if test="requestBy != null">request_by = #{requestBy},</if>
            <if test="requestTime != null">request_time = #{requestTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processBy != null">process_by = #{processBy},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="processResult != null">process_result = #{processResult},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModificationRequestById" parameterType="Long">
        delete from modification_request where id = #{id}
    </delete>

    <delete id="deleteModificationRequestByIds" parameterType="String">
        delete from modification_request where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>