<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.FaceAccessMapper">
    
    <resultMap type="FaceAccess" id="FaceAccessResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="personName"    column="person_name"    />
        <result property="personType"    column="person_type"    />
        <result property="openTime"    column="open_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="buildingNumber"    column="building_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="ownerPhone"    column="owner_phone"    />
        <result property="rentalStatus"    column="rental_status"    />
        <result property="tenantName"    column="tenant_name"    />
        <result property="tenantPhone"    column="tenant_phone"    />
        <result property="communityName"    column="community_name"    />
    </resultMap>

    <sql id="selectFaceAccessVo">
        select id, community_id, owner_id, tenant_id, person_name, person_type, open_time, expire_time, status, create_by, create_time from face_access
    </sql>

    <select id="selectFaceAccessList" parameterType="FaceAccess" resultMap="FaceAccessResult">
        select fa.id, fa.community_id, fa.owner_id, fa.tenant_id, fa.person_name, fa.person_type, fa.open_time, fa.expire_time, fa.status, fa.create_by, fa.create_time,
        om.building_number,om.house_number,om.owner_name,om.owner_phone,om.rental_status,om.tenant_name,om.tenant_phone,
        cm.community_name
        from face_access fa
        left join owner_mangement om on fa.owner_id = om.id
        left join community_mangement cm on fa.community_id = cm.id
        <where>  
            <if test="communityId != null "> and fa.community_id = #{communityId}</if>
            <if test="ownerId != null "> and fa.owner_id = #{ownerId}</if>
            <if test="tenantId != null "> and fa.tenant_id = #{tenantId}</if>
            <if test="personName != null  and personName != ''"> and fa.person_name like concat('%', #{personName}, '%')</if>
            <if test="personType != null "> and fa.person_type = #{personType}</if>
            <if test="openTime != null "> and fa.open_time = #{openTime}</if>
            <if test="expireTime != null "> and fa.expire_time = #{expireTime}</if>
            <if test="status != null "> and fa.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectFaceAccessById" parameterType="Long" resultMap="FaceAccessResult">
        <include refid="selectFaceAccessVo"/>
        where id = #{id}
    </select>

    <insert id="insertFaceAccess" parameterType="FaceAccess" useGeneratedKeys="true" keyProperty="id">
        insert into face_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="personName != null and personName != ''">person_name,</if>
            <if test="personType != null">person_type,</if>
            <if test="openTime != null">open_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="personName != null and personName != ''">#{personName},</if>
            <if test="personType != null">#{personType},</if>
            <if test="openTime != null">#{openTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateFaceAccess" parameterType="FaceAccess">
        update face_access
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="personName != null and personName != ''">person_name = #{personName},</if>
            <if test="personType != null">person_type = #{personType},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFaceAccessById" parameterType="Long">
        delete from face_access where id = #{id}
    </delete>

    <delete id="deleteFaceAccessByIds" parameterType="String">
        delete from face_access where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>