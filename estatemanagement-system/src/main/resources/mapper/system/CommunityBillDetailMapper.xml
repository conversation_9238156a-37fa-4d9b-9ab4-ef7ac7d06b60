<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.CommunityBillDetailMapper">
    
    <resultMap type="CommunityBillDetail" id="CommunityBillDetailResult">
        <result property="id"    column="id"    />
        <result property="billId"    column="bill_id"    />
        <result property="feeType"    column="fee_type"    />
        <result property="feeName"    column="fee_name"    />
        <result property="baseAmount"    column="base_amount"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="billingDays"    column="billing_days"    />
        <result property="amount"    column="amount"    />
        <result property="plateNumber"    column="plate_number"    />
        <result property="spaceNumber"    column="space_number"    />
        <result property="calculationFormula"    column="calculation_formula"    />
        <result property="billPeriodStart"    column="bill_period_start"    />
        <result property="billPeriodEnd"    column="bill_period_end"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCommunityBillDetailVo">
        select id, bill_id, fee_type, fee_name, base_amount, unit_price, billing_days, amount, plate_number, space_number, calculation_formula, bill_period_start, bill_period_end, create_by, create_time, update_by, update_time, remark from community_bill_detail
    </sql>

    <select id="selectCommunityBillDetailList" parameterType="CommunityBillDetail" resultMap="CommunityBillDetailResult">
        <include refid="selectCommunityBillDetailVo"/>
        <where>  
            <if test="billId != null "> and bill_id = #{billId}</if>
            <if test="feeType != null "> and fee_type = #{feeType}</if>
            <if test="feeName != null  and feeName != ''"> and fee_name like concat('%', #{feeName}, '%')</if>
            <if test="baseAmount != null "> and base_amount = #{baseAmount}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="billingDays != null "> and billing_days = #{billingDays}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="plateNumber != null  and plateNumber != ''"> and plate_number = #{plateNumber}</if>
            <if test="spaceNumber != null  and spaceNumber != ''"> and space_number = #{spaceNumber}</if>
            <if test="calculationFormula != null  and calculationFormula != ''"> and calculation_formula = #{calculationFormula}</if>
        </where>
    </select>
    
    <select id="selectCommunityBillDetailById" parameterType="Long" resultMap="CommunityBillDetailResult">
        <include refid="selectCommunityBillDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommunityBillDetail" parameterType="CommunityBillDetail" useGeneratedKeys="true" keyProperty="id">
        insert into community_bill_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="feeName != null and feeName != ''">fee_name,</if>
            <if test="baseAmount != null">base_amount,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="billingDays != null">billing_days,</if>
            <if test="amount != null">amount,</if>
            <if test="plateNumber != null">plate_number,</if>
            <if test="spaceNumber != null">space_number,</if>
            <if test="calculationFormula != null">calculation_formula,</if>
            <if test="billPeriodStart != null">bill_period_start,</if>
            <if test="billPeriodEnd != null">bill_period_end,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="feeName != null and feeName != ''">#{feeName},</if>
            <if test="baseAmount != null">#{baseAmount},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="billingDays != null">#{billingDays},</if>
            <if test="amount != null">#{amount},</if>
            <if test="plateNumber != null">#{plateNumber},</if>
            <if test="spaceNumber != null">#{spaceNumber},</if>
            <if test="calculationFormula != null">#{calculationFormula},</if>
            <if test="billPeriodStart != null">#{billPeriodStart},</if>
            <if test="billPeriodEnd != null">#{billPeriodEnd},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateCommunityBillDetail" parameterType="CommunityBillDetail">
        update community_bill_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="feeName != null and feeName != ''">fee_name = #{feeName},</if>
            <if test="baseAmount != null">base_amount = #{baseAmount},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="billingDays != null">billing_days = #{billingDays},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="plateNumber != null">plate_number = #{plateNumber},</if>
            <if test="spaceNumber != null">space_number = #{spaceNumber},</if>
            <if test="calculationFormula != null">calculation_formula = #{calculationFormula},</if>
            <if test="billPeriodStart != null">bill_period_start = #{billPeriodStart},</if>
            <if test="billPeriodEnd != null">bill_period_end = #{billPeriodEnd},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunityBillDetailById" parameterType="Long">
        delete from community_bill_detail where id = #{id}
    </delete>

    <delete id="deleteCommunityBillDetailByIds" parameterType="String">
        delete from community_bill_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>