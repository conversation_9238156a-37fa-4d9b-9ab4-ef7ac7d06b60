<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.DepositManagementMapper">
    
    <resultMap type="DepositManagement" id="DepositManagementResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="depositType"    column="deposit_type"    />
        <result property="depositName"    column="deposit_name"    />
        <result property="depositAmount"    column="deposit_amount"    />
        <result property="collectDate"    column="collect_date"    />
        <result property="collectUserId"    column="collect_user_id"    />
        <result property="collectUserName"    column="collect_user_name"    />
        <result property="receiptNumber"    column="receipt_number"    />
        <result property="refundDate"    column="refund_date"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundUserId"    column="refund_user_id"    />
        <result property="refundUserName"    column="refund_user_name"    />
        <result property="refundReason"    column="refund_reason"    />
        <result property="status"    column="status"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="renovationStartDate"    column="renovation_start_date"    />
        <result property="renovationEndDate"    column="renovation_end_date"    />
        <result property="renovationManager"    column="renovation_manager"    />
        <result property="renovationManagerPhone"    column="renovation_manager_phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="communityName"    column="community_name"    />
        <result property="buildingNumber"    column="building_number"    />
        <result property="houseNumber"    column="house_number"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="ownerPhone"    column="owner_phone"    />
    </resultMap>

    <sql id="selectDepositManagementVo">
        select d.id, d.community_id, d.owner_id, d.deposit_type, d.deposit_name, d.deposit_amount, 
               d.collect_date, d.collect_user_id, d.collect_user_name, d.receipt_number, 
               d.refund_date, d.refund_amount, d.refund_user_id, d.refund_user_name, d.refund_reason, 
               d.status, d.payment_method, d.renovation_start_date, d.renovation_end_date, 
               d.renovation_manager, d.renovation_manager_phone, d.create_by, d.create_time, 
               d.update_by, d.update_time, d.remark,
               c.community_name, o.building_number, o.house_number, o.owner_name, o.owner_phone
        from deposit_management d
        left join community_mangement c on d.community_id = c.id
        left join owner_mangement o on d.owner_id = o.id
    </sql>

    <select id="selectDepositManagementList" parameterType="DepositManagement" resultMap="DepositManagementResult">
        <include refid="selectDepositManagementVo"/>
        <where>  
            <if test="communityId != null "> and d.community_id = #{communityId}</if>
            <if test="ownerId != null "> and d.owner_id = #{ownerId}</if>
            <if test="depositType != null "> and d.deposit_type = #{depositType}</if>
            <if test="depositName != null  and depositName != ''"> and d.deposit_name like concat('%', #{depositName}, '%')</if>
            <if test="collectDate != null "> and d.collect_date = #{collectDate}</if>
            <if test="refundDate != null "> and d.refund_date = #{refundDate}</if>
            <if test="status != null "> and d.status = #{status}</if>
            <if test="collectUserName != null  and collectUserName != ''"> and d.collect_user_name like concat('%', #{collectUserName}, '%')</if>
            <if test="refundUserName != null  and refundUserName != ''"> and d.refund_user_name like concat('%', #{refundUserName}, '%')</if>
        </where>
        order by d.collect_date desc
    </select>
    
    <select id="selectDepositManagementById" parameterType="Long" resultMap="DepositManagementResult">
        <include refid="selectDepositManagementVo"/>
        where d.id = #{id}
    </select>
        
    <insert id="insertDepositManagement" parameterType="DepositManagement" useGeneratedKeys="true" keyProperty="id">
        insert into deposit_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="depositType != null">deposit_type,</if>
            <if test="depositName != null">deposit_name,</if>
            <if test="depositAmount != null">deposit_amount,</if>
            <if test="collectDate != null">collect_date,</if>
            <if test="collectUserId != null">collect_user_id,</if>
            <if test="collectUserName != null">collect_user_name,</if>
            <if test="receiptNumber != null">receipt_number,</if>
            <if test="refundDate != null">refund_date,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundUserId != null">refund_user_id,</if>
            <if test="refundUserName != null">refund_user_name,</if>
            <if test="refundReason != null">refund_reason,</if>
            <if test="status != null">status,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="renovationStartDate != null">renovation_start_date,</if>
            <if test="renovationEndDate != null">renovation_end_date,</if>
            <if test="renovationManager != null">renovation_manager,</if>
            <if test="renovationManagerPhone != null">renovation_manager_phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="depositType != null">#{depositType},</if>
            <if test="depositName != null">#{depositName},</if>
            <if test="depositAmount != null">#{depositAmount},</if>
            <if test="collectDate != null">#{collectDate},</if>
            <if test="collectUserId != null">#{collectUserId},</if>
            <if test="collectUserName != null">#{collectUserName},</if>
            <if test="receiptNumber != null">#{receiptNumber},</if>
            <if test="refundDate != null">#{refundDate},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundUserId != null">#{refundUserId},</if>
            <if test="refundUserName != null">#{refundUserName},</if>
            <if test="refundReason != null">#{refundReason},</if>
            <if test="status != null">#{status},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="renovationStartDate != null">#{renovationStartDate},</if>
            <if test="renovationEndDate != null">#{renovationEndDate},</if>
            <if test="renovationManager != null">#{renovationManager},</if>
            <if test="renovationManagerPhone != null">#{renovationManagerPhone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDepositManagement" parameterType="DepositManagement">
        update deposit_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="depositType != null">deposit_type = #{depositType},</if>
            <if test="depositName != null">deposit_name = #{depositName},</if>
            <if test="depositAmount != null">deposit_amount = #{depositAmount},</if>
            <if test="collectDate != null">collect_date = #{collectDate},</if>
            <if test="collectUserId != null">collect_user_id = #{collectUserId},</if>
            <if test="collectUserName != null">collect_user_name = #{collectUserName},</if>
            <if test="receiptNumber != null">receipt_number = #{receiptNumber},</if>
            <if test="refundDate != null">refund_date = #{refundDate},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundUserId != null">refund_user_id = #{refundUserId},</if>
            <if test="refundUserName != null">refund_user_name = #{refundUserName},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="renovationStartDate != null">renovation_start_date = #{renovationStartDate},</if>
            <if test="renovationEndDate != null">renovation_end_date = #{renovationEndDate},</if>
            <if test="renovationManager != null">renovation_manager = #{renovationManager},</if>
            <if test="renovationManagerPhone != null">renovation_manager_phone = #{renovationManagerPhone},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDepositManagementById" parameterType="Long">
        delete from deposit_management where id = #{id}
    </delete>

    <delete id="deleteDepositManagementByIds" parameterType="String">
        delete from deposit_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
