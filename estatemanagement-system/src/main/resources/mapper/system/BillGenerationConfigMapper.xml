<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.BillGenerationConfigMapper">
    
    <resultMap type="BillGenerationConfig" id="BillGenerationConfigResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="communityName"    column="community_name"    />
        <result property="autoGenerate"    column="auto_generate"    />
        <result property="intervalDays"    column="interval_days"    />
        <result property="generateType"    column="generate_type"    />
        <result property="lastGenerateTime"    column="last_generate_time"    />
        <result property="nextGenerateTime"    column="next_generate_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBillGenerationConfigVo">
        select bgc.id, bgc.community_id, cm.community_name, bgc.auto_generate, bgc.interval_days, 
               bgc.generate_type, bgc.last_generate_time, bgc.next_generate_time, bgc.status, 
               bgc.create_by, bgc.create_time, bgc.update_by, bgc.update_time, bgc.remark 
        from bill_generation_config bgc
        left join community_mangement cm on bgc.community_id = cm.id
    </sql>

    <select id="selectBillGenerationConfigList" parameterType="BillGenerationConfig" resultMap="BillGenerationConfigResult">
        <include refid="selectBillGenerationConfigVo"/>
        <where>  
            <if test="communityId != null "> and bgc.community_id = #{communityId}</if>
            <if test="communityName != null  and communityName != ''"> and cm.community_name like concat('%', #{communityName}, '%')</if>
            <if test="autoGenerate != null "> and bgc.auto_generate = #{autoGenerate}</if>
            <if test="intervalDays != null "> and bgc.interval_days = #{intervalDays}</if>
            <if test="generateType != null  and generateType != ''"> and bgc.generate_type = #{generateType}</if>
            <if test="status != null "> and bgc.status = #{status}</if>
        </where>
        order by bgc.create_time desc
    </select>
    
    <select id="selectBillGenerationConfigById" parameterType="Long" resultMap="BillGenerationConfigResult">
        <include refid="selectBillGenerationConfigVo"/>
        where bgc.id = #{id}
    </select>

    <select id="selectBillGenerationConfigByCommunityId" parameterType="Long" resultMap="BillGenerationConfigResult">
        <include refid="selectBillGenerationConfigVo"/>
        where bgc.community_id = #{communityId}
    </select>

    <insert id="insertBillGenerationConfig" parameterType="BillGenerationConfig" useGeneratedKeys="true" keyProperty="id">
        insert into bill_generation_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="autoGenerate != null">auto_generate,</if>
            <if test="intervalDays != null">interval_days,</if>
            <if test="generateType != null">generate_type,</if>
            <if test="lastGenerateTime != null">last_generate_time,</if>
            <if test="nextGenerateTime != null">next_generate_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="autoGenerate != null">#{autoGenerate},</if>
            <if test="intervalDays != null">#{intervalDays},</if>
            <if test="generateType != null">#{generateType},</if>
            <if test="lastGenerateTime != null">#{lastGenerateTime},</if>
            <if test="nextGenerateTime != null">#{nextGenerateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateBillGenerationConfig" parameterType="BillGenerationConfig">
        update bill_generation_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="autoGenerate != null">auto_generate = #{autoGenerate},</if>
            <if test="intervalDays != null">interval_days = #{intervalDays},</if>
            <if test="generateType != null">generate_type = #{generateType},</if>
            <if test="lastGenerateTime != null">last_generate_time = #{lastGenerateTime},</if>
            <if test="nextGenerateTime != null">next_generate_time = #{nextGenerateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBillGenerationConfigById" parameterType="Long">
        delete from bill_generation_config where id = #{id}
    </delete>

    <delete id="deleteBillGenerationConfigByIds" parameterType="String">
        delete from bill_generation_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
