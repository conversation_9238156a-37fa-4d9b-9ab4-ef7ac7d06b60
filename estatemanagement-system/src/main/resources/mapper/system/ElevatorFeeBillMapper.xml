<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estatemanagement.system.mapper.ElevatorFeeBillMapper">
    
    <resultMap type="ElevatorFeeBill" id="ElevatorFeeBillResult">
        <result property="id"    column="id"    />
        <result property="communityId"    column="community_id"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="billMonth"    column="bill_month"    />
        <result property="houseArea"    column="house_area"    />
        <result property="amount"    column="amount"    />
        <result property="status"    column="status"    />
        <result property="paymentDate"    column="payment_date"    />
        <result property="receiptNumber"    column="receipt_number"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectElevatorFeeBillVo">
        select id, community_id, owner_id, bill_month, house_area, amount, status, payment_date, receipt_number, create_time from elevator_fee_bill
    </sql>

    <select id="selectElevatorFeeBillList" parameterType="ElevatorFeeBill" resultMap="ElevatorFeeBillResult">
        <include refid="selectElevatorFeeBillVo"/>
        <where>  
            <if test="communityId != null "> and community_id = #{communityId}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
            <if test="billMonth != null "> and bill_month = #{billMonth}</if>
            <if test="houseArea != null "> and house_area = #{houseArea}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="paymentDate != null "> and payment_date = #{paymentDate}</if>
            <if test="receiptNumber != null  and receiptNumber != ''"> and receipt_number = #{receiptNumber}</if>
        </where>
    </select>
    
    <select id="selectElevatorFeeBillById" parameterType="Long" resultMap="ElevatorFeeBillResult">
        <include refid="selectElevatorFeeBillVo"/>
        where id = #{id}
    </select>

    <insert id="insertElevatorFeeBill" parameterType="ElevatorFeeBill" useGeneratedKeys="true" keyProperty="id">
        insert into elevator_fee_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="billMonth != null">bill_month,</if>
            <if test="houseArea != null">house_area,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
            <if test="paymentDate != null">payment_date,</if>
            <if test="receiptNumber != null">receipt_number,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="billMonth != null">#{billMonth},</if>
            <if test="houseArea != null">#{houseArea},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
            <if test="paymentDate != null">#{paymentDate},</if>
            <if test="receiptNumber != null">#{receiptNumber},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateElevatorFeeBill" parameterType="ElevatorFeeBill">
        update elevator_fee_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="billMonth != null">bill_month = #{billMonth},</if>
            <if test="houseArea != null">house_area = #{houseArea},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paymentDate != null">payment_date = #{paymentDate},</if>
            <if test="receiptNumber != null">receipt_number = #{receiptNumber},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElevatorFeeBillById" parameterType="Long">
        delete from elevator_fee_bill where id = #{id}
    </delete>

    <delete id="deleteElevatorFeeBillByIds" parameterType="String">
        delete from elevator_fee_bill where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>