# 收费记录审核前端功能实现文档

## 功能概述

在收费记录管理页面中增加了审核功能，财务人员可以对收费记录进行审核，包括通过审核、拒绝审核，并可以修改实收金额和填写审核意见。

## 功能特性

### 1. 审核状态显示
- **待审核**：黄色标签，表示需要审核的记录
- **通过审核**：绿色标签，表示已通过审核的记录
- **审核拒绝**：红色标签，表示被拒绝的记录

### 2. 审核操作
- **单个审核**：点击"审核"按钮对单条记录进行审核
- **批量审核**：选择多条记录进行批量通过或拒绝
- **审核筛选**：可按审核状态筛选记录

### 3. 审核表单
- **收据编号**：显示收费记录的收据编号（只读）
- **业主信息**：显示业主的房号和姓名（只读）
- **应缴金额**：显示应该收取的金额（只读）
- **实收金额**：可修改的实际收到金额
- **审核结果**：选择通过审核或审核拒绝
- **审核意见**：必填的审核说明，5-500字符

## 页面修改内容

### 1. 查询条件增强
```vue
<el-form-item label="审核状态" prop="auditStatus">
  <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
    <el-option label="待审核" value="0"></el-option>
    <el-option label="通过审核" value="1"></el-option>
    <el-option label="审核拒绝" value="2"></el-option>
  </el-select>
</el-form-item>
```

### 2. 表格列增强
```vue
<!-- 应缴金额列 -->
<el-table-column label="应缴金额" align="center" prop="dueAmount" min-width="100">
  <template slot-scope="scope">
    <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.dueAmount || scope.row.paymentAmount }}</span>
  </template>
</el-table-column>

<!-- 审核状态列 -->
<el-table-column label="审核状态" align="center" prop="auditStatus" min-width="100">
  <template slot-scope="scope">
    <el-tag v-if="scope.row.auditStatus === 0" type="warning">待审核</el-tag>
    <el-tag v-else-if="scope.row.auditStatus === 1" type="success">通过审核</el-tag>
    <el-tag v-else-if="scope.row.auditStatus === 2" type="danger">审核拒绝</el-tag>
    <el-tag v-else type="info">未知状态</el-tag>
  </template>
</el-table-column>
```

### 3. 操作按钮增强
```vue
<el-button
  v-if="scope.row.auditStatus === 0"
  size="mini"
  type="text"
  icon="el-icon-check"
  @click="handleAudit(scope.row)"
  v-hasPermi="['system:paymentRecord:audit']"
  style="color: #409EFF;"
>审核</el-button>
```

### 4. 审核对话框
```vue
<el-dialog title="收费记录审核" :visible.sync="auditOpen" width="600px" append-to-body>
  <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
    <!-- 表单内容 -->
  </el-form>
</el-dialog>
```

## API 接口集成

### 1. 新增 API 方法
```javascript
// 审核收费记录
export function auditPaymentRecord(data) {
  return request({
    url: '/system/paymentRecord/audit',
    method: 'post',
    data: data
  })
}

// 批量审核收费记录
export function batchAuditPaymentRecords(data) {
  return request({
    url: '/system/paymentRecord/batchAudit',
    method: 'post',
    data: data
  })
}
```

### 2. 导入 API 方法
```javascript
import { 
  listPaymentRecord, 
  auditPaymentRecord,
  // ... 其他方法
} from "@/api/system/paymentRecord"
```

## 数据结构

### 1. 查询参数扩展
```javascript
queryParams: {
  pageNum: 1,
  pageSize: 10,
  communityId: null,
  ownerId: null,
  receiptNumber: null,
  paymentMethod: null,
  paymentDate: null,
  auditStatus: null, // 新增审核状态筛选
}
```

### 2. 审核表单数据
```javascript
auditForm: {
  paymentId: null,        // 收费记录ID
  receiptNumber: '',      // 收据编号（显示用）
  ownerInfo: '',          // 业主信息（显示用）
  dueAmount: 0,           // 应缴金额（显示用）
  actualAmount: 0,        // 实收金额（可修改）
  auditStatus: 1,         // 审核状态（1通过，2拒绝）
  auditComment: ''        // 审核意见
}
```

### 3. 表单验证规则
```javascript
auditRules: {
  actualAmount: [
    { required: true, message: "请输入实收金额", trigger: "blur" },
    { type: 'number', min: 0, message: "实收金额不能小于0", trigger: "blur" }
  ],
  auditStatus: [
    { required: true, message: "请选择审核结果", trigger: "change" }
  ],
  auditComment: [
    { required: true, message: "请输入审核意见", trigger: "blur" },
    { min: 5, max: 500, message: "审核意见长度在5到500个字符", trigger: "blur" }
  ]
}
```

## 核心方法实现

### 1. 处理审核
```javascript
handleAudit(row) {
  this.resetAuditForm()
  this.auditForm.paymentId = row.id
  this.auditForm.receiptNumber = row.receiptNumber
  this.auditForm.ownerInfo = `${row.buildingNumber}-${row.houseNumber} ${row.ownerName}`
  this.auditForm.dueAmount = row.dueAmount || row.paymentAmount
  this.auditForm.actualAmount = row.paymentAmount
  this.auditOpen = true
}
```

### 2. 提交审核
```javascript
submitAudit() {
  this.$refs["auditForm"].validate(valid => {
    if (valid) {
      this.auditLoading = true
      const auditData = {
        paymentId: this.auditForm.paymentId,
        auditStatus: this.auditForm.auditStatus,
        auditComment: this.auditForm.auditComment
      }
      
      auditPaymentRecord(auditData).then(() => {
        this.$modal.msgSuccess("审核成功")
        this.auditOpen = false
        this.getList()
      }).catch(error => {
        this.$modal.msgError("审核失败：" + (error.msg || error.message))
      }).finally(() => {
        this.auditLoading = false
      })
    }
  })
}
```

## 权限控制

### 1. 按钮权限
```vue
v-hasPermi="['system:paymentRecord:audit']"
```

### 2. 菜单权限
需要在系统菜单中配置相应的权限标识。

## 使用说明

### 1. 审核流程
1. 财务人员登录系统
2. 进入收费记录管理页面
3. 筛选待审核的记录
4. 点击"审核"按钮
5. 填写审核信息并提交
6. 系统更新记录状态

### 2. 批量操作
1. 选择多条待审核记录
2. 点击"批量通过"或"批量拒绝"
3. 填写批量审核意见
4. 确认提交

### 3. 状态筛选
- 选择"待审核"查看需要处理的记录
- 选择"通过审核"查看已通过的记录
- 选择"审核拒绝"查看被拒绝的记录

## 注意事项

1. **权限控制**：确保只有财务人员有审核权限
2. **数据验证**：前端和后端都要进行数据验证
3. **用户体验**：提供清晰的状态提示和操作反馈
4. **错误处理**：妥善处理网络错误和业务异常
5. **性能优化**：大量数据时考虑分页和筛选优化
