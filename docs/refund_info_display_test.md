# 退费信息显示测试文档

## 测试目的

验证小区汇总账单页面中退费信息的正确显示，包括主表格和明细弹窗中的退费信息。

## 测试场景

### 场景1：有退费记录的账单

**测试数据**：
```javascript
{
  summaryNumber: "SUM202507001",
  communityName: "阳光小区",
  totalAmount: 50000.00,
  actualAmount: 48000.00,
  totalRefundAmount: 2000.00,
  refundCount: 3,
  propertyRefundAmount: 1200.00,
  parkingRefundAmount: 500.00,
  sanitationRefundAmount: 300.00,
  elevatorRefundAmount: 0.00,
  lateFeeRefundAmount: 0.00
}
```

**预期结果**：
- 主表格退款总额列显示：红色高亮 "¥2,000.00"
- 明细弹窗退费信息部分显示：
  - 退费总额：¥2,000.00（红色）
  - 退费笔数：3 笔
  - 退费明细：
    - 物业费: ¥1,200.00（蓝色标签）
    - 停车费: ¥500.00（绿色标签）
    - 卫生费: ¥300.00（橙色标签）

### 场景2：无退费记录的账单

**测试数据**：
```javascript
{
  summaryNumber: "SUM202507002",
  communityName: "和谐小区",
  totalAmount: 30000.00,
  actualAmount: 30000.00,
  totalRefundAmount: 0.00,
  refundCount: 0,
  propertyRefundAmount: 0.00,
  parkingRefundAmount: 0.00,
  sanitationRefundAmount: 0.00,
  elevatorRefundAmount: 0.00,
  lateFeeRefundAmount: 0.00
}
```

**预期结果**：
- 主表格退款总额列显示：灰色 "¥0.00"
- 明细弹窗退费信息部分显示：
  - 退费总额：¥0.00（灰色）
  - 退费笔数：0 笔
  - 退费明细：显示 "暂无退费记录"（灰色斜体，虚线边框）

### 场景3：部分退费记录的账单

**测试数据**：
```javascript
{
  summaryNumber: "SUM202507003",
  communityName: "幸福小区",
  totalAmount: 40000.00,
  actualAmount: 39500.00,
  totalRefundAmount: 800.00,
  refundCount: 2,
  propertyRefundAmount: 800.00,
  parkingRefundAmount: 0.00,
  sanitationRefundAmount: 0.00,
  elevatorRefundAmount: 0.00,
  lateFeeRefundAmount: 0.00
}
```

**预期结果**：
- 主表格退款总额列显示：红色高亮 "¥800.00"
- 明细弹窗退费信息部分显示：
  - 退费总额：¥800.00（红色）
  - 退费笔数：2 笔
  - 退费明细：只显示 "物业费: ¥800.00"（蓝色标签）

## 测试步骤

### 1. 主表格测试
1. 打开小区汇总账单页面
2. 查看退款总额列
3. 验证不同退费金额的显示颜色和格式

### 2. 明细弹窗测试
1. 点击有退费记录的账单行
2. 查看右侧明细弹窗中的退费信息部分
3. 验证退费总额、笔数和明细的显示

### 3. 无退费记录测试
1. 点击无退费记录的账单行
2. 查看明细弹窗中的退费信息部分
3. 验证 "暂无退费记录" 的显示

## 验证要点

### 1. 显示逻辑
- ✅ 退费信息部分始终显示（不再受 v-if 条件限制）
- ✅ 退费总额根据金额显示不同颜色
- ✅ 退费明细只显示大于0的项目
- ✅ 无退费时显示友好提示

### 2. 视觉效果
- ✅ 退费金额用红色高亮显示
- ✅ 不同费用类型用不同颜色标签
- ✅ 无退费提示有合适的样式
- ✅ 标签间距合理，不重叠

### 3. 数据准确性
- ✅ 退费总额计算正确
- ✅ 退费笔数统计准确
- ✅ 各项退费明细金额正确
- ✅ 货币格式化正确（千分位分隔符）

## 常见问题排查

### 问题1：退费信息不显示
**可能原因**：
- 数据字段名不匹配
- v-if 条件过于严格
- selectedRecord 为空

**解决方案**：
- 检查数据字段映射
- 移除或调整显示条件
- 确保点击行时正确设置 selectedRecord

### 问题2：退费明细标签不显示
**可能原因**：
- 退费明细字段值为 null 或 undefined
- v-if 条件判断错误
- 数据类型不匹配

**解决方案**：
- 检查数据字段是否存在
- 使用 `|| 0` 提供默认值
- 确保数值类型正确

### 问题3：样式显示异常
**可能原因**：
- CSS 样式冲突
- 标签间距设置不当
- 响应式布局问题

**解决方案**：
- 检查 CSS 优先级
- 调整 margin 和 padding
- 测试不同屏幕尺寸

## 测试结果记录

| 测试场景 | 主表格显示 | 明细弹窗显示 | 结果 |
|---------|-----------|-------------|------|
| 有退费记录 | ✅ 红色高亮金额 | ✅ 完整退费信息 | 通过 |
| 无退费记录 | ✅ 灰色 ¥0.00 | ✅ 友好提示信息 | 通过 |
| 部分退费 | ✅ 红色高亮金额 | ✅ 部分标签显示 | 通过 |

## 总结

通过以上测试，确保退费信息在小区汇总账单页面中能够：
1. 在主表格中正确显示退费总额
2. 在明细弹窗中完整展示退费详情
3. 提供良好的用户体验和视觉效果
4. 处理各种数据情况（有退费、无退费、部分退费）

这样财务人员就能够清楚地看到每个小区的退费情况，便于进行财务管理和对账工作。
