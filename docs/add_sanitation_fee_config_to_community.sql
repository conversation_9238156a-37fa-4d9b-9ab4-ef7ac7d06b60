-- 为 community_mangement 表添加卫生费缴费类型和阶梯费用配置字段

-- 添加卫生费缴费类型字段
ALTER TABLE community_mangement 
ADD COLUMN sanitation_fee_type INT(1) DEFAULT 2 COMMENT '卫生费缴费类型(1固定费用,2阶梯费用)' AFTER elevator_fee;

-- 添加固定卫生费字段
ALTER TABLE community_mangement 
ADD COLUMN fixed_sanitation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '固定卫生费(月)' AFTER sanitation_fee_type;

-- 添加阶梯费用配置字段（JSON格式）
ALTER TABLE community_mangement 
ADD COLUMN tiered_fee_config TEXT COMMENT '阶梯费用配置(JSON格式)' AFTER fixed_sanitation_fee;

-- 为现有记录设置默认值和迁移数据
-- 如果小区已经设置了阶梯费用，保持阶梯费用类型并生成JSON配置
UPDATE community_mangement 
SET sanitation_fee_type = 2,
    tiered_fee_config = JSON_OBJECT(
        'ranges', JSON_ARRAY(
            JSON_OBJECT(
                'name', '0-49㎡',
                'minArea', 0,
                'maxArea', 49,
                'fee', IFNULL(public_sanitation_fee_049, 0),
                'description', '0-49平方米'
            ),
            JSON_OBJECT(
                'name', '50-59㎡',
                'minArea', 50,
                'maxArea', 59,
                'fee', IFNULL(public_sanitation_fee_5059, 0),
                'description', '50-59平方米'
            ),
            JSON_OBJECT(
                'name', '60-69㎡',
                'minArea', 60,
                'maxArea', 69,
                'fee', IFNULL(public_sanitation_fee_6069, 0),
                'description', '60-69平方米'
            ),
            JSON_OBJECT(
                'name', '70-79㎡',
                'minArea', 70,
                'maxArea', 79,
                'fee', IFNULL(public_sanitation_fee_7079, 0),
                'description', '70-79平方米'
            ),
            JSON_OBJECT(
                'name', '80-89㎡',
                'minArea', 80,
                'maxArea', 89,
                'fee', IFNULL(public_sanitation_fee_80200, 0),
                'description', '80-89平方米'
            ),
            JSON_OBJECT(
                'name', '90㎡以上',
                'minArea', 90,
                'maxArea', NULL,
                'fee', IFNULL(public_sanitation_fee_200, 0),
                'description', '90平方米以上'
            )
        )
    )
WHERE id IS NOT NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_community_sanitation_fee_type ON community_mangement(sanitation_fee_type);

-- 添加字段注释
ALTER TABLE community_mangement MODIFY COLUMN sanitation_fee_type INT(1) DEFAULT 2 COMMENT '卫生费缴费类型(1固定费用,2阶梯费用)';
ALTER TABLE community_mangement MODIFY COLUMN fixed_sanitation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '固定卫生费(月)';
ALTER TABLE community_mangement MODIFY COLUMN tiered_fee_config TEXT COMMENT '阶梯费用配置(JSON格式)';

-- 示例JSON配置格式说明
/*
阶梯费用配置JSON格式示例：
{
  "ranges": [
    {
      "name": "0-49㎡",
      "minArea": 0,
      "maxArea": 49,
      "fee": 20.00,
      "description": "0-49平方米"
    },
    {
      "name": "50-59㎡", 
      "minArea": 50,
      "maxArea": 59,
      "fee": 25.00,
      "description": "50-59平方米"
    },
    {
      "name": "200㎡以上",
      "minArea": 200,
      "maxArea": null,
      "fee": 50.00,
      "description": "200平方米以上"
    }
  ]
}
*/

-- 数据验证查询（可选执行）
-- SELECT 
--     community_name,
--     sanitation_fee_type,
--     CASE 
--         WHEN sanitation_fee_type = 1 THEN '固定费用'
--         WHEN sanitation_fee_type = 2 THEN '阶梯费用'
--         ELSE '未知类型'
--     END as fee_type_name,
--     fixed_sanitation_fee,
--     tiered_fee_config,
--     public_sanitation_fee_049,
--     public_sanitation_fee_5059,
--     public_sanitation_fee_6069,
--     public_sanitation_fee_7079,
--     public_sanitation_fee_80200,
--     public_sanitation_fee_200
-- FROM community_mangement
-- ORDER BY community_name;
