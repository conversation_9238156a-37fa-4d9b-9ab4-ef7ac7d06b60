## 2025-06-30
- [x] 1、账单编号缩短一点（最好删掉）
> 格式：CB + 十三位时间戳
- 2、缴费的时候周期是按照开始周期计算，比如2022年10月到2026年10月，交1年就是交2022年10月到2023年10月
- 3、车位费计算不太对
- 4、车位费的退费，物业、卫生费退费
- 5、卫生费改成自定义阶梯定价
> 阶梯定价： 卫生费(0-49平方)、卫生费(50-59平方)、卫生费(60-69平方)、卫生费(70-79平方)、卫生费(80-89平方)、卫生费(90平方+)
- [x] 6、业主信息-车牌管理展示一下车牌的到期时间  
- [x] 7、押金管理增加一个施工押金
> 提取为字典值 押金类型：property_deposit_type
- 8、押金管理不合其他业务关联（需要排查是否关联）
- 9、人脸识别记录在新增业主的时候可以新增，开通时间按照小区入园时间，结束时间根据卫生费或者物业费走（只会交卫生费或者物业费，不会同时出现收两种，根据小区设置收哪个就根据哪个到期时间走）



##  更新sql
```mysql
-- 为 community_mangement 表添加卫生费缴费类型和阶梯费用配置字段

-- 添加卫生费缴费类型字段
ALTER TABLE community_mangement 
ADD COLUMN sanitation_fee_type INT(1) DEFAULT 2 COMMENT '卫生费缴费类型(1固定费用,2阶梯费用)' AFTER elevator_fee;

-- 添加固定卫生费字段
ALTER TABLE community_mangement 
ADD COLUMN fixed_sanitation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '固定卫生费(月)' AFTER sanitation_fee_type;

-- 添加阶梯费用配置字段（JSON格式）
ALTER TABLE community_mangement 
ADD COLUMN tiered_fee_config TEXT COMMENT '阶梯费用配置(JSON格式)' AFTER fixed_sanitation_fee;

-- 为现有记录设置默认值和迁移数据
-- 如果小区已经设置了阶梯费用，保持阶梯费用类型并生成JSON配置
UPDATE community_mangement 
SET sanitation_fee_type = 2,
    tiered_fee_config = JSON_OBJECT(
        'ranges', JSON_ARRAY(
            JSON_OBJECT(
                'name', '0-49㎡',
                'minArea', 0,
                'maxArea', 49,
                'fee', IFNULL(public_sanitation_fee_049, 0),
                'description', '0-49平方米'
            ),
            JSON_OBJECT(
                'name', '50-59㎡',
                'minArea', 50,
                'maxArea', 59,
                'fee', IFNULL(public_sanitation_fee_5059, 0),
                'description', '50-59平方米'
            ),
            JSON_OBJECT(
                'name', '60-69㎡',
                'minArea', 60,
                'maxArea', 69,
                'fee', IFNULL(public_sanitation_fee_6069, 0),
                'description', '60-69平方米'
            ),
            JSON_OBJECT(
                'name', '70-79㎡',
                'minArea', 70,
                'maxArea', 79,
                'fee', IFNULL(public_sanitation_fee_7079, 0),
                'description', '70-79平方米'
            ),
            JSON_OBJECT(
                'name', '80-89㎡',
                'minArea', 80,
                'maxArea', 89,
                'fee', IFNULL(public_sanitation_fee_80200, 0),
                'description', '80-89平方米'
            ),
            JSON_OBJECT(
                'name', '90㎡以上',
                'minArea', 90,
                'maxArea', NULL,
                'fee', IFNULL(public_sanitation_fee_200, 0),
                'description', '90平方米以上'
            )
        )
    )
WHERE id IS NOT NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_community_sanitation_fee_type ON community_mangement(sanitation_fee_type);

-- 添加字段注释
ALTER TABLE community_mangement MODIFY COLUMN sanitation_fee_type INT(1) DEFAULT 2 COMMENT '卫生费缴费类型(1固定费用,2阶梯费用)';
ALTER TABLE community_mangement MODIFY COLUMN fixed_sanitation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '固定卫生费(月)';
ALTER TABLE community_mangement MODIFY COLUMN tiered_fee_config TEXT COMMENT '阶梯费用配置(JSON格式)';

```

-- 2025-07-04（已执行）
```mysql
ALTER TABLE `estate_management`.`payment_detail` 
ADD COLUMN `plate_number` varchar(20) NULL COMMENT '车牌号' AFTER `create_time`;
```
```mysql

-- 添加应缴金额字段
ALTER TABLE payment_record 
ADD COLUMN due_amount DECIMAL(10,2) COMMENT '应缴金额' AFTER owner_id;

-- 添加审核状态字段
ALTER TABLE payment_record 
ADD COLUMN audit_status INT(1) DEFAULT 0 COMMENT '审核状态(0待审核,1通过审核,2审核拒绝)' AFTER due_amount;

-- 添加审核人ID字段
ALTER TABLE payment_record 
ADD COLUMN auditor_id BIGINT COMMENT '审核人ID' AFTER audit_status;

-- 添加审核时间字段
ALTER TABLE payment_record 
ADD COLUMN audit_time DATETIME COMMENT '审核时间' AFTER auditor_id;

-- 添加审核意见字段
ALTER TABLE payment_record 
ADD COLUMN audit_comment VARCHAR(500) COMMENT '审核意见' AFTER audit_time;

-- 为现有记录设置默认值
-- 将现有记录的应缴金额设置为实收金额（假设之前的记录都是准确的）
UPDATE payment_record 
SET due_amount = payment_amount 
WHERE due_amount IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_payment_record_audit_status ON payment_record(audit_status);
CREATE INDEX idx_payment_record_auditor ON payment_record(auditor_id);
CREATE INDEX idx_payment_record_audit_time ON payment_record(audit_time);
```
- 2025-07-03 （已执行）
```shell
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (101, '押金类型', 'property_deposit_type', '0', 'admin', '2025-07-01 09:30:53', '', NULL, '押金类型 1装修押金 2 其他押金 3 施工押金');

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (102, 1, '装修押金', '1', 'property_deposit_type', 'primary', 'default', 'N', '0', 'admin', '2025-07-01 09:31:10', 'admin', '2025-07-01 09:39:56', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (103, 2, '其他押金', '2', 'property_deposit_type', 'warning', 'default', 'N', '0', 'admin', '2025-07-01 09:31:17', 'admin', '2025-07-01 09:40:04', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (104, 3, '施工押金', '3', 'property_deposit_type', 'success', 'default', 'N', '0', 'admin', '2025-07-01 09:31:30', 'admin', '2025-07-01 09:40:11', NULL);

```

```mysql
-- 退费类型字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('退费类型', 'refund_type', '0', 'admin', sysdate(), '退费类型列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '全额退费', '1', 'refund_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '全额退费');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '部分退费', '2', 'refund_type', '', 'info', 'N', '0', 'admin', sysdate(), '部分退费');

-- 退费方式字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('退费方式', 'refund_method', '0', 'admin', sysdate(), '退费方式列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '现金', 'cash', 'refund_method', '', 'primary', 'Y', '0', 'admin', sysdate(), '现金退费');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '银行转账', 'bank_transfer', 'refund_method', '', 'success', 'N', '0', 'admin', sysdate(), '银行转账退费');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '微信', 'wechat', 'refund_method', '', 'warning', 'N', '0', 'admin', sysdate(), '微信退费');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (4, '支付宝', 'alipay', 'refund_method', '', 'info', 'N', '0', 'admin', sysdate(), '支付宝退费');

-- 退费状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('退费状态', 'refund_status', '0', 'admin', sysdate(), '退费状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (1, '待审核', '0', 'refund_status', '', 'warning', 'N', '0', 'admin', sysdate(), '待审核状态');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (2, '已完成', '1', 'refund_status', '', 'success', 'Y', '0', 'admin', sysdate(), '已完成状态');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) 
VALUES (3, '已取消', '2', 'refund_status', '', 'danger', 'N', '0', 'admin', sysdate(), '已取消状态');
```
```mysql
CREATE TABLE `refund_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_number` varchar(50) NOT NULL COMMENT '退费单号',
  `original_payment_id` bigint NOT NULL COMMENT '原收费记录ID',
  `original_receipt_number` varchar(50) DEFAULT NULL COMMENT '原收据号',
  `community_id` bigint NOT NULL COMMENT '小区ID',
  `owner_id` bigint NOT NULL COMMENT '业主ID',
  `refund_type` tinyint NOT NULL DEFAULT '1' COMMENT '退费类型(1全额退费,2部分退费)',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退费原因',
  `total_refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总退费金额',
  `property_fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '物业费退费金额',
  `parking_fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '停车费退费金额',
  `sanitation_fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '卫生费退费金额',
  `elevator_fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '电梯费退费金额',
  `late_fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '滞纳金退费金额',
  `refund_method` varchar(20) DEFAULT NULL COMMENT '退费方式(cash现金,bank_transfer银行转账,wechat微信,alipay支付宝)',
  `refund_date` datetime NOT NULL COMMENT '退费日期',
  `operator_id` bigint NOT NULL COMMENT '操作员ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作员姓名',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0待审核,1已完成,2已取消)',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_user_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refund_number` (`refund_number`),
  KEY `idx_original_payment_id` (`original_payment_id`),
  KEY `idx_community_owner` (`community_id`,`owner_id`),
  KEY `idx_refund_date` (`refund_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='退费记录表';
CREATE TABLE `refund_detail` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `refund_id` bigint NOT NULL COMMENT '退费记录ID',
                                 `original_payment_detail_id` bigint NOT NULL COMMENT '原收费明细ID',
                                 `fee_type` tinyint NOT NULL COMMENT '费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)',
                                 `fee_name` varchar(50) DEFAULT NULL COMMENT '费用名称',
                                 `original_payment_amount` decimal(10,2) NOT NULL COMMENT '原支付金额',
                                 `refund_amount` decimal(10,2) NOT NULL COMMENT '退费金额',
                                 `refund_months` bigint DEFAULT NULL COMMENT '退费月数',
                                 `refund_days` bigint DEFAULT NULL COMMENT '退费天数',
                                 `original_period_start` date DEFAULT NULL COMMENT '原缴费周期开始',
                                 `original_period_end` date DEFAULT NULL COMMENT '原缴费周期结束',
                                 `refund_period_start` date DEFAULT NULL COMMENT '退费周期开始',
                                 `refund_period_end` date DEFAULT NULL COMMENT '退费周期结束',
                                 `remaining_period_start` date DEFAULT NULL COMMENT '剩余周期开始',
                                 `remaining_period_end` date DEFAULT NULL COMMENT '剩余周期结束',
                                 `plate_number` varchar(20) DEFAULT NULL COMMENT '车牌号(停车费相关)',
                                 `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_refund_id` (`refund_id`),
                                 KEY `idx_original_payment_detail_id` (`original_payment_detail_id`),
                                 KEY `idx_fee_type` (`fee_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='退费明细表';
```

```mysql

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2090, '退费管理', 2000, 6, 'refund', NULL, NULL, '', 1, 0, 'M', '0', '0', NULL, 'money', 'admin', '2025-07-02 12:05:46', '', NULL, '退费管理菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2091, '退费记录', 2090, 2, 'refundRecord', 'system/refundRecord/index', NULL, '', 1, 0, 'C', '0', '0', 'system:refundRecord:list', 'list', 'admin', '2025-07-02 12:05:46', '', NULL, '退费记录菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2092, '退费申请', 2090, 1, 'refundApply', 'system/refundRecord/refundApply', NULL, '', 1, 0, 'C', '0', '0', 'system:refundRecord:apply', 'edit', 'admin', '2025-07-02 12:05:46', '', NULL, '退费申请菜单');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2093, '退费记录查询', 2091, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:query', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2094, '退费记录新增', 2091, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:add', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2095, '退费记录修改', 2091, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:edit', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2096, '退费记录删除', 2091, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:remove', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2097, '退费记录导出', 2091, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:export', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2098, '退费审核', 2091, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:audit', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2099, '退费申请权限', 2090, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:refundRecord:apply', '#', 'admin', '2025-07-02 12:05:46', '', NULL, '');

```

```mysql
-- 为小区账单汇总表添加退费相关字段
ALTER TABLE `community_summary_bill` 
ADD COLUMN `total_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '总退费金额' AFTER `total_amount`,
ADD COLUMN `property_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '物业费退费金额' AFTER `total_refund_amount`,
ADD COLUMN `parking_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '停车费退费金额' AFTER `property_refund_amount`,
ADD COLUMN `sanitation_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '卫生费退费金额' AFTER `parking_refund_amount`,
ADD COLUMN `elevator_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '电梯费退费金额' AFTER `sanitation_refund_amount`,
ADD COLUMN `late_fee_refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '滞纳金退费金额' AFTER `elevator_refund_amount`,
ADD COLUMN `refund_count` int(11) DEFAULT '0' COMMENT '退费笔数' AFTER `late_fee_refund_amount`;
```