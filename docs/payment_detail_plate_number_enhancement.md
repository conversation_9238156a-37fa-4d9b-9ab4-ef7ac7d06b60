# 费用明细表增加车牌号字段优化方案

## 问题描述

在原有的混合支付计算逻辑中，存在以下问题：
1. `calculateMixedPaymentAmount` 方法在计算费用时没有查询收费明细中已存在的支付订单
2. 缴费周期计算基于当前时间而不是基于账单周期和已缴费后的缴费周期结束时间
3. 停车费相关的车牌号信息需要通过复杂的关联查询获取，影响性能

## 解决方案

### 1. 数据库结构优化

#### 1.1 添加 plate_number 字段
在 `payment_detail` 表中添加 `plate_number` 字段：

```sql
ALTER TABLE payment_detail 
ADD COLUMN plate_number VARCHAR(20) COMMENT '车牌号（停车费时记录具体车牌号）' AFTER period_end;
```

#### 1.2 添加索引
```sql
CREATE INDEX idx_payment_detail_owner_fee_plate ON payment_detail(fee_type, plate_number);
```

### 2. 实体类修改

#### 2.1 PaymentDetail 实体类
- 添加 `plateNumber` 字段
- 更新 `toString()` 方法包含车牌号信息

### 3. Mapper 层修改

#### 3.1 PaymentDetailMapper 接口
- 添加 `selectLastPaymentEndDateByOwnerAndFeeType` 方法，用于查询业主某个费用类型的最后缴费记录结束时间

#### 3.2 PaymentDetailMapper.xml
- 更新 resultMap 包含 `plate_number` 字段
- 更新 SQL 查询语句包含 `plate_number` 字段
- 添加新的查询方法，简化车牌号查询逻辑
- 简化现有查询，直接使用 `plate_number` 字段而不是复杂的关联查询

### 4. 业务逻辑优化

#### 4.1 新增业主账单查询方法
- 添加 `getLatestBillStartDateForOwner` 方法，用于获取业主最新账单的周期开始时间
- 支持混合支付在没有历史缴费记录时从账单周期开始计算

#### 4.2 混合支付计算逻辑优化
- 修改 `calculateSingleFeeForMixed` 方法，使用新的 `getLastPaymentEndDateForOwnerAndFeeType` 方法
- 优先从收费明细表中查询最后缴费记录的结束时间
- 如果没有找到缴费记录，则查找业主最新账单的周期开始时间作为缴费起始点
- 只有在既没有缴费记录也没有账单时，才降级到使用当前费用到期时间

**缴费开始时间计算逻辑**：
1. **有历史缴费记录**：从最后缴费记录结束时间的下一天开始
2. **无历史缴费记录但有账单**：从账单周期开始时间开始
3. **无历史缴费记录且无账单**：从当前费用到期时间的下一天开始

#### 4.3 费用明细生成逻辑优化
- 在 `generatePaymentDetailForBillPayment` 方法中添加车牌号设置
- 在 `generatePaymentDetailForFeeTypeWithoutBill` 方法中添加车牌号设置
- 在混合支付明细生成中添加车牌号设置

### 5. 查询性能优化

#### 5.1 简化车牌号查询
- 原来需要通过 `parking_info` 表关联查询车牌号
- 现在直接从 `payment_detail` 表中获取车牌号
- 减少了复杂的 JOIN 操作，提高查询性能

#### 5.2 缴费记录查询优化
- 新增专门的查询方法 `selectLastPaymentEndDateByOwnerAndFeeType`
- 支持按业主ID、费用类型、车牌号查询最后缴费记录
- 为混合支付提供准确的缴费周期计算基础

## 修改文件清单

### 1. 实体类
- `estatemanagement-system/src/main/java/com/estatemanagement/system/domain/PaymentDetail.java`

### 2. Mapper 接口
- `estatemanagement-system/src/main/java/com/estatemanagement/system/mapper/PaymentDetailMapper.java`

### 3. Mapper XML
- `estatemanagement-system/src/main/resources/mapper/system/PaymentDetailMapper.xml`
- `estatemanagement-system/src/main/resources/mapper/system/PaymentRecordMapper.xml`

### 4. 业务逻辑
- `estatemanagement-system/src/main/java/com/estatemanagement/system/service/impl/PaymentRecordServiceImpl.java`

### 5. 数据库迁移
- `estatemanagement-system/src/main/resources/db/migration/add_plate_number_to_payment_detail.sql`

### 6. 测试文件
- `estatemanagement-system/src/test/java/com/estatemanagement/system/service/PaymentDetailPlateNumberTest.java`

## 优化效果

### 1. 性能提升
- 减少了复杂的关联查询
- 直接从费用明细表获取车牌号信息
- 提高了混合支付计算的准确性和效率

### 2. 数据一致性
- 车牌号信息直接存储在费用明细中，避免数据不一致
- 支持历史数据的准确追溯

### 3. 功能完善
- 混合支付计算逻辑更加准确
- 支持基于已缴费记录的缴费周期计算
- 提供了更好的数据查询和统计能力

## 注意事项

1. 需要执行数据库迁移脚本添加新字段
2. 对于历史数据，可以选择性地执行数据更新脚本
3. 新的逻辑向后兼容，不会影响现有功能
4. 建议在测试环境充分验证后再部署到生产环境
