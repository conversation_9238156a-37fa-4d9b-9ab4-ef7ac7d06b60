-- 创建汇总账单生成配置表
CREATE TABLE IF NOT EXISTS `summary_bill_generation_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `community_id` bigint(20) DEFAULT NULL COMMENT '小区ID（NULL表示全部小区）',
  `community_name` varchar(100) DEFAULT NULL COMMENT '小区名称',
  `auto_generate` tinyint(1) DEFAULT '0' COMMENT '是否启用自动生成(0否,1是)',
  `interval_days` int(11) DEFAULT '30' COMMENT '生成间隔天数',
  `last_generate_time` datetime DEFAULT NULL COMMENT '上次生成时间',
  `next_generate_time` datetime DEFAULT NULL COMMENT '下次生成时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0停用,1启用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_status` (`status`),
  KEY `idx_auto_generate` (`auto_generate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汇总账单生成配置表';
