-- 更新账单支付状态字段注释，添加新的状态3：合并到新账单
ALTER TABLE `community_bill` MODIFY COLUMN `payment_status` int(11) DEFAULT NULL COMMENT '支付状态(0未支付,1部分支付,2已支付,3合并到新账单)';

-- 更新各项费用支付状态字段注释
ALTER TABLE `community_bill` MODIFY COLUMN `property_payment_status` int(11) DEFAULT NULL COMMENT '物业费支付状态(0未支付,1部分支付,2已支付,3合并到新账单)';
ALTER TABLE `community_bill` MODIFY COLUMN `parking_payment_status` int(11) DEFAULT NULL COMMENT '停车费支付状态(0未支付,1部分支付,2已支付,3合并到新账单)';
ALTER TABLE `community_bill` MODIFY COLUMN `sanitation_payment_status` int(11) DEFAULT NULL COMMENT '卫生费支付状态(0未支付,1部分支付,2已支付,3合并到新账单)';
ALTER TABLE `community_bill` MODIFY COLUMN `elevator_payment_status` int(11) DEFAULT NULL COMMENT '电梯费支付状态(0未支付,1部分支付,2已支付,3合并到新账单)';

-- 查询当前合并状态的账单数量（用于验证）
SELECT 
    payment_status,
    COUNT(*) as count,
    CASE 
        WHEN payment_status = 0 THEN '未支付'
        WHEN payment_status = 1 THEN '部分支付'
        WHEN payment_status = 2 THEN '已支付'
        WHEN payment_status = 3 THEN '合并到新账单'
        ELSE '未知状态'
    END as status_name
FROM community_bill 
GROUP BY payment_status 
ORDER BY payment_status;
