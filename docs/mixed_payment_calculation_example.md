# 混合支付计算逻辑示例

## 问题背景

在原有的混合支付计算中，缴费开始时间的计算存在问题：
- 当收费明细中没有缴费记录时，系统使用当前费用到期时间作为起始点
- 这导致缴费周期与账单周期不一致，可能出现重复计费或遗漏计费的问题

## 修复后的逻辑

### 缴费开始时间计算优先级

1. **第一优先级：历史缴费记录**
   - 查询收费明细表中该业主该费用类型的最后缴费记录
   - 如果找到记录，从最后缴费记录结束时间的下一天开始

2. **第二优先级：账单周期**
   - 如果没有历史缴费记录，查找业主最新账单的周期开始时间
   - 从账单周期开始时间开始计算

3. **第三优先级：当前费用到期时间**
   - 如果既没有历史缴费记录也没有账单，使用当前费用到期时间
   - 从当前费用到期时间的下一天开始

## 具体示例

### 示例1：有历史缴费记录

**场景**：业主张三要缴纳3个月的物业费
- 最后缴费记录结束时间：2024-12-31
- 计算结果：
  - 缴费开始时间：2025-01-01
  - 缴费结束时间：2025-03-31
  - 缴费周期：2025-01-01 至 2025-03-31

```
最后缴费记录: 2024-10-01 至 2024-12-31
新缴费周期:   2025-01-01 至 2025-03-31  ✓ 连续无重叠
```

### 示例2：无历史缴费记录但有账单

**场景**：业主李四首次缴纳停车费
- 账单周期：2024-01-01 至 2024-12-31
- 车牌号：京A12345
- 计算结果：
  - 缴费开始时间：2024-01-01（从账单周期开始）
  - 缴费结束时间：2024-03-31
  - 缴费周期：2024-01-01 至 2024-03-31

```
账单周期:     2024-01-01 至 2024-12-31
新缴费周期:   2024-01-01 至 2024-03-31  ✓ 与账单周期一致
```

### 示例3：无历史缴费记录且无账单

**场景**：新业主王五缴纳卫生费
- 当前卫生费到期时间：2024-06-30
- 计算结果：
  - 缴费开始时间：2024-07-01
  - 缴费结束时间：2024-09-30
  - 缴费周期：2024-07-01 至 2024-09-30

```
当前费用到期: 2024-06-30
新缴费周期:   2024-07-01 至 2024-09-30  ✓ 从到期后开始
```

## 代码实现要点

### 核心方法：getLastPaymentEndDateForOwnerAndFeeType

```java
private LocalDate getLastPaymentEndDateForOwnerAndFeeType(Long ownerId, Integer feeType, String plateNumber) {
    // 1. 查询历史缴费记录
    Date lastPaymentEndDate = paymentDetailMapper.selectLastPaymentEndDateByOwnerAndFeeType(ownerId, feeType, plateNumber);
    
    if (lastPaymentEndDate != null) {
        // 有历史记录：返回最后缴费结束时间
        return lastPaymentEndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    } else {
        // 2. 查询账单周期
        LocalDate billStartDate = getLatestBillStartDateForOwner(ownerId);
        if (billStartDate != null) {
            // 有账单：返回账单开始时间的前一天（plusDays(1)后就是账单开始时间）
            return billStartDate.minusDays(1);
        } else {
            // 3. 使用当前费用到期时间
            return getCurrentFeeEndDate(ownerId, feeType, paymentData);
        }
    }
}
```

### 使用方式

```java
// 在 calculateSingleFeeForMixed 方法中
LocalDate lastEndDate = getLastPaymentEndDateForOwnerAndFeeType(owner.getId(), feeType, plateNumber);
LocalDate startDate = lastEndDate.plusDays(1);  // 缴费开始时间
LocalDate endDate = feeCalculationService.calculateEndDateByMonths(startDate, months);  // 缴费结束时间
```

## 优势

1. **数据一致性**：确保缴费周期与账单周期保持一致
2. **避免重复计费**：基于历史缴费记录计算，避免重复收费
3. **灵活降级**：提供多层降级机制，适应不同场景
4. **性能优化**：直接查询收费明细表，减少复杂关联查询

## 测试验证

系统提供了完整的测试用例来验证各种场景：
- `MixedPaymentCalculationTest.java`：测试混合支付计算逻辑
- `PaymentDetailPlateNumberTest.java`：测试车牌号字段功能

通过这些测试可以确保修改后的逻辑在各种场景下都能正确工作。
