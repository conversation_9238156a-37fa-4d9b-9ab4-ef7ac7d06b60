# 小区管理卫生费JSON配置功能实现文档

## 功能概述

为小区管理模块新增了卫生费缴费类型字段，支持固定费用和阶梯费用两种模式。阶梯费用采用JSON格式存储，提供灵活的区间配置功能，便于后期维护和扩展。

## 数据库设计

### 新增字段
```sql
-- 卫生费缴费类型
sanitation_fee_type INT(1) DEFAULT 2 COMMENT '卫生费缴费类型(1固定费用,2阶梯费用)'

-- 固定卫生费
fixed_sanitation_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '固定卫生费(月)'

-- 阶梯费用配置（JSON格式）
tiered_fee_config TEXT COMMENT '阶梯费用配置(JSON格式)'
```

### JSON配置格式
```json
{
  "ranges": [
    {
      "name": "0-49㎡",
      "minArea": 0,
      "maxArea": 49,
      "fee": 20.00,
      "description": "0-49平方米"
    },
    {
      "name": "50-59㎡", 
      "minArea": 50,
      "maxArea": 59,
      "fee": 25.00,
      "description": "50-59平方米"
    },
    {
      "name": "200㎡以上",
      "minArea": 200,
      "maxArea": null,
      "fee": 50.00,
      "description": "200平方米以上"
    }
  ]
}
```

## 后端实现

### 1. 实体类扩展
```java
public class CommunityMangement {
    /** 卫生费缴费类型(1固定费用,2阶梯费用) */
    private Integer sanitationFeeType;

    /** 固定卫生费（月） */
    private BigDecimal fixedSanitationFee;

    /** 阶梯费用配置(JSON格式) */
    private String tieredFeeConfig;
}
```

### 2. 阶梯费用配置类
```java
@Data
public class TieredFeeConfig {
    private List<TieredFeeRange> ranges;
    
    @Data
    public static class TieredFeeRange {
        private String name;
        private BigDecimal minArea;
        private BigDecimal maxArea;
        private BigDecimal fee;
        private String description;
        
        public boolean isInRange(BigDecimal area) {
            // 判断面积是否在区间内
        }
        
        public String getRangeText() {
            // 获取区间显示文本
        }
    }
    
    public BigDecimal getFeeByArea(BigDecimal area) {
        // 根据面积获取对应费用
    }
}
```

### 3. 卫生费计算工具类
```java
@Slf4j
public class SanitationFeeCalculator {
    
    public static BigDecimal calculateSanitationFee(CommunityMangement community, BigDecimal houseArea) {
        Integer feeType = community.getSanitationFeeType();
        
        if (feeType == 1) {
            // 固定费用
            return calculateFixedFee(community);
        } else {
            // 阶梯费用
            return calculateTieredFee(community, houseArea);
        }
    }
    
    private static BigDecimal calculateTieredFee(CommunityMangement community, BigDecimal houseArea) {
        // 优先使用JSON配置
        String tieredConfig = community.getTieredFeeConfig();
        if (tieredConfig != null && !tieredConfig.trim().isEmpty()) {
            return calculateTieredFeeFromJson(tieredConfig, houseArea);
        }
        
        // 兼容旧的固定字段配置
        return calculateTieredFeeFromFields(community, houseArea);
    }
    
    public static String getSanitationFeeRuleDescription(CommunityMangement community, BigDecimal houseArea) {
        // 获取计算规则描述
    }
}
```

## 前端实现

### 1. 界面设计

#### 卫生费类型选择
```vue
<el-form-item label="卫生费缴费类型" prop="sanitationFeeType">
  <el-radio-group v-model="form.sanitationFeeType" @change="handleSanitationFeeTypeChange">
    <el-radio :label="1">固定费用</el-radio>
    <el-radio :label="2">阶梯费用</el-radio>
  </el-radio-group>
</el-form-item>
```

#### 固定费用配置
```vue
<div v-show="form.sanitationFeeType === 1">
  <el-form-item label="固定卫生费" prop="fixedSanitationFee">
    <el-input v-model="form.fixedSanitationFee" placeholder="月价格" type="number">
      <template slot="append">元/月</template>
    </el-input>
  </el-form-item>
</div>
```

#### 阶梯费用配置
```vue
<div v-show="form.sanitationFeeType === 2">
  <el-form-item label="阶梯费用配置">
    <el-button type="primary" size="small" @click="openTieredFeeConfig">
      配置阶梯费用
    </el-button>
    <span style="margin-left: 10px; color: #909399; font-size: 12px;">
      点击配置灵活的阶梯费用区间
    </span>
  </el-form-item>
</div>
```

### 2. 阶梯费用配置对话框

#### 配置表格
```vue
<el-table :data="tieredRanges" border>
  <el-table-column label="区间名称" width="120">
    <template slot-scope="scope">
      <el-input v-model="scope.row.name" placeholder="如：0-49㎡" size="small"></el-input>
    </template>
  </el-table-column>
  <el-table-column label="最小面积" width="120">
    <template slot-scope="scope">
      <el-input-number v-model="scope.row.minArea" :min="0" :precision="2" size="small"></el-input-number>
    </template>
  </el-table-column>
  <el-table-column label="最大面积" width="120">
    <template slot-scope="scope">
      <el-input-number v-model="scope.row.maxArea" :min="0" :precision="2" size="small" placeholder="留空表示无上限"></el-input-number>
    </template>
  </el-table-column>
  <el-table-column label="费用(元/月)" width="120">
    <template slot-scope="scope">
      <el-input-number v-model="scope.row.fee" :min="0" :precision="2" size="small"></el-input-number>
    </template>
  </el-table-column>
</el-table>
```

#### 配置预览
```vue
<div class="config-preview">
  <h4>配置预览</h4>
  <el-table :data="tieredRanges" border size="small">
    <el-table-column label="区间" prop="name"></el-table-column>
    <el-table-column label="面积范围">
      <template slot-scope="scope">
        <span>{{ formatRange(scope.row) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="费用">
      <template slot-scope="scope">
        <span>{{ scope.row.fee || 0 }}元/月</span>
      </template>
    </el-table-column>
  </el-table>
</div>
```

### 3. JavaScript方法

#### 配置管理
```javascript
// 打开配置对话框
openTieredFeeConfig() {
  this.tieredFeeConfigOpen = true
  this.loadTieredFeeConfig()
}

// 加载配置
loadTieredFeeConfig() {
  if (this.form.tieredFeeConfig) {
    try {
      const config = JSON.parse(this.form.tieredFeeConfig)
      this.tieredRanges = config.ranges || []
    } catch (e) {
      this.loadDefaultConfig()
    }
  } else {
    this.loadDefaultConfig()
  }
}

// 保存配置
saveTieredFeeConfig() {
  // 验证配置
  for (let i = 0; i < this.tieredRanges.length; i++) {
    const range = this.tieredRanges[i]
    if (!range.name || range.fee == null || range.fee < 0) {
      this.$modal.msgError(`第${i + 1}个区间配置有误`)
      return
    }
  }
  
  // 生成JSON配置
  const config = { ranges: this.tieredRanges }
  this.form.tieredFeeConfig = JSON.stringify(config)
  this.tieredFeeConfigOpen = false
  this.$modal.msgSuccess('阶梯费用配置保存成功')
}
```

## 功能特性

### 1. 灵活配置
- **自定义区间**：支持任意数量的面积区间
- **灵活范围**：支持开区间（无上限或无下限）
- **实时预览**：配置过程中实时预览效果
- **默认模板**：提供常用的默认配置模板

### 2. 数据验证
- **前端验证**：区间名称、费用金额的必填验证
- **后端验证**：JSON格式验证和数据完整性检查
- **兼容性**：保持对旧数据格式的兼容

### 3. 用户体验
- **直观界面**：表格化配置，操作简单
- **即时反馈**：配置错误时立即提示
- **批量操作**：支持添加、删除、批量配置

### 4. 向后兼容
- **数据迁移**：自动将旧的固定字段转换为JSON格式
- **双重支持**：同时支持JSON配置和固定字段配置
- **平滑过渡**：不影响现有数据和功能

## 使用流程

### 1. 新建小区配置
1. 选择卫生费缴费类型（固定费用/阶梯费用）
2. 如选择固定费用：直接输入月费金额
3. 如选择阶梯费用：点击"配置阶梯费用"按钮
4. 在配置对话框中设置各个面积区间和对应费用
5. 保存配置

### 2. 修改现有配置
1. 编辑小区信息
2. 修改卫生费缴费类型或具体配置
3. 保存更改

### 3. 费用计算
- 系统自动根据配置类型和房屋面积计算卫生费
- 支持在收费记录中查看详细的计算规则

## 技术优势

### 1. 扩展性
- JSON格式便于添加新的配置项
- 支持复杂的费用计算规则
- 易于集成其他费用类型

### 2. 维护性
- 配置与代码分离，便于维护
- 可视化配置界面，降低操作难度
- 完整的数据验证和错误处理

### 3. 性能
- JSON解析性能优秀
- 缓存机制减少重复计算
- 数据库索引优化查询性能

## 总结

通过引入JSON配置的阶梯费用功能，系统在保持向后兼容的同时，大大提升了配置的灵活性和可维护性。用户可以根据实际需求自定义任意数量的费用区间，满足不同小区的个性化收费需求。
