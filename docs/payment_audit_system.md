# 收费记录审核系统设计文档

## 概述

为了加强财务管理和对账功能，在收费记录表中增加了审核状态、应缴金额、审核人等字段，实现了完整的收费记录审核流程。

## 功能特性

### 1. 审核状态管理
- **待审核（0）**：新创建的收费记录默认状态
- **通过审核（1）**：财务确认金额无误后的状态
- **审核拒绝（2）**：发现问题需要重新处理的状态

### 2. 对账功能
- **应缴金额**：记录应该收取的金额
- **实收金额**：记录实际收到的金额
- **金额对比**：支持应缴与实收金额的对比分析

### 3. 审核流程
- **创建记录**：系统自动设置为待审核状态
- **财务审核**：财务人员进行审核并记录意见
- **状态跟踪**：完整记录审核人、审核时间、审核意见

## 数据库设计

### 新增字段

```sql
-- 应缴金额
due_amount DECIMAL(10,2) COMMENT '应缴金额'

-- 审核状态
audit_status INT(1) DEFAULT 0 COMMENT '审核状态(0待审核,1通过审核,2审核拒绝)'

-- 审核人ID
auditor_id BIGINT COMMENT '审核人ID'

-- 审核时间
audit_time DATETIME COMMENT '审核时间'

-- 审核意见
audit_comment VARCHAR(500) COMMENT '审核意见'
```

### 索引优化

```sql
-- 审核状态索引
CREATE INDEX idx_payment_record_audit_status ON payment_record(audit_status);

-- 审核人索引
CREATE INDEX idx_payment_record_auditor ON payment_record(auditor_id);

-- 审核时间索引
CREATE INDEX idx_payment_record_audit_time ON payment_record(audit_time);
```

## API 接口

### 1. 审核收费记录
```
POST /system/paymentRecord/audit
```

**请求参数：**
```json
{
  "paymentId": 123,
  "auditStatus": 1,
  "auditComment": "审核通过，金额正确"
}
```

### 2. 批量审核
```
POST /system/paymentRecord/batchAudit
```

**请求参数：**
```json
{
  "paymentIds": [123, 124, 125],
  "auditStatus": 1,
  "auditComment": "批量审核通过"
}
```

### 3. 查询待审核记录
```
GET /system/paymentRecord/pendingAudit
```

### 4. 审核统计信息
```
GET /system/paymentRecord/auditStatistics?communityId=1
```

**响应示例：**
```json
{
  "totalCount": 100,
  "pendingCount": 15,
  "approvedCount": 80,
  "rejectedCount": 5,
  "approvalRate": 80.0
}
```

## 业务流程

### 1. 收费记录创建流程

```mermaid
graph TD
    A[创建收费记录] --> B[设置应缴金额]
    B --> C[设置实收金额]
    C --> D[设置审核状态为待审核]
    D --> E[保存记录]
    E --> F[等待财务审核]
```

### 2. 审核流程

```mermaid
graph TD
    A[财务人员登录] --> B[查看待审核记录]
    B --> C[核对金额信息]
    C --> D{金额是否正确?}
    D -->|是| E[审核通过]
    D -->|否| F[审核拒绝]
    E --> G[记录审核意见]
    F --> H[记录拒绝原因]
    G --> I[更新审核状态]
    H --> I
    I --> J[完成审核]
```

### 3. 对账流程

```mermaid
graph TD
    A[选择时间范围] --> B[查询收费记录]
    B --> C[按审核状态分类]
    C --> D[计算应缴总额]
    D --> E[计算实收总额]
    E --> F[生成对账报表]
    F --> G[标识差异记录]
```

## 权限控制

### 权限定义
- `system:paymentRecord:audit` - 审核收费记录权限
- `system:paymentRecord:list` - 查看收费记录权限
- `system:paymentRecord:statistics` - 查看统计信息权限

### 角色分配
- **财务人员**：拥有审核权限，可以审核收费记录
- **收费员**：只能创建记录，不能审核
- **管理员**：拥有所有权限，可以查看统计信息

## 使用场景

### 1. 日常收费审核
1. 收费员创建收费记录
2. 财务人员定期审核记录
3. 确认金额无误后通过审核
4. 发现问题时拒绝审核并说明原因

### 2. 月度对账
1. 财务人员查询月度收费记录
2. 按审核状态筛选记录
3. 对比应缴与实收金额
4. 生成对账报表

### 3. 异常处理
1. 查询审核拒绝的记录
2. 分析拒绝原因
3. 联系相关人员处理
4. 重新提交审核

## 数据迁移

### 历史数据处理
```sql
-- 为现有记录设置默认值
UPDATE payment_record 
SET due_amount = payment_amount 
WHERE due_amount IS NULL;

-- 将现有记录设置为已审核状态
UPDATE payment_record 
SET audit_status = 1 
WHERE audit_status = 0;
```

## 监控指标

### 1. 审核效率指标
- 平均审核时间
- 待审核记录数量
- 审核通过率

### 2. 质量指标
- 审核拒绝率
- 金额差异率
- 重复审核率

### 3. 业务指标
- 日均收费金额
- 月度收费趋势
- 各小区收费情况

## 注意事项

1. **数据一致性**：确保应缴金额与实收金额的准确性
2. **权限控制**：严格控制审核权限，防止越权操作
3. **审计跟踪**：完整记录审核过程，便于后续追溯
4. **性能优化**：合理使用索引，提高查询效率
5. **备份策略**：定期备份审核数据，防止数据丢失

## 扩展功能

### 1. 自动审核
- 设置自动审核规则
- 小额收费自动通过
- 异常金额自动标记

### 2. 审核工作流
- 多级审核流程
- 审核任务分配
- 审核提醒通知

### 3. 报表分析
- 审核效率分析
- 收费趋势分析
- 异常数据分析
