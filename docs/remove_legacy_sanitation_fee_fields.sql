-- 移除传统固定区间卫生费字段
-- 注意：执行前请确保已经将数据迁移到 tiered_fee_config 字段

-- 备份现有数据（可选，建议在生产环境执行）
-- CREATE TABLE community_mangement_backup AS SELECT * FROM community_mangement;

-- 检查数据迁移状态
-- 确保所有使用阶梯费用的小区都已经配置了 tiered_fee_config
SELECT 
    community_name,
    sanitation_fee_type,
    CASE 
        WHEN sanitation_fee_type = 2 AND (tiered_fee_config IS NULL OR tiered_fee_config = '') THEN '需要配置JSON'
        WHEN sanitation_fee_type = 2 AND tiered_fee_config IS NOT NULL THEN '已配置JSON'
        WHEN sanitation_fee_type = 1 THEN '使用固定费用'
        ELSE '未知状态'
    END as config_status,
    public_sanitation_fee_049,
    public_sanitation_fee_5059,
    public_sanitation_fee_6069,
    public_sanitation_fee_7079,
    public_sanitation_fee_80200,
    public_sanitation_fee_200,
    tiered_fee_config
FROM community_mangement 
WHERE sanitation_fee_type = 2
ORDER BY community_name;

-- 如果上述查询显示所有阶梯费用小区都已配置JSON，则可以安全删除旧字段

-- 删除传统固定区间字段
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_049;
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_5059;
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_6069;
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_7079;
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_80200;
ALTER TABLE community_mangement DROP COLUMN IF EXISTS public_sanitation_fee_200;

-- 验证字段删除结果
DESCRIBE community_mangement;

-- 验证数据完整性
SELECT 
    COUNT(*) as total_communities,
    SUM(CASE WHEN sanitation_fee_type = 1 THEN 1 ELSE 0 END) as fixed_fee_count,
    SUM(CASE WHEN sanitation_fee_type = 2 THEN 1 ELSE 0 END) as tiered_fee_count,
    SUM(CASE WHEN sanitation_fee_type = 1 AND fixed_sanitation_fee IS NOT NULL THEN 1 ELSE 0 END) as fixed_fee_configured,
    SUM(CASE WHEN sanitation_fee_type = 2 AND tiered_fee_config IS NOT NULL THEN 1 ELSE 0 END) as tiered_fee_configured
FROM community_mangement;

-- 显示当前表结构（验证）
SHOW CREATE TABLE community_mangement;

-- 清理说明
/*
执行此脚本后，community_mangement 表将只保留以下卫生费相关字段：
1. sanitation_fee_type - 卫生费缴费类型(1固定费用,2阶梯费用)
2. fixed_sanitation_fee - 固定卫生费(月)
3. tiered_fee_config - 阶梯费用配置(JSON格式)

传统的固定区间字段已被移除：
- public_sanitation_fee_049
- public_sanitation_fee_5059  
- public_sanitation_fee_6069
- public_sanitation_fee_7079
- public_sanitation_fee_80200
- public_sanitation_fee_200

这样简化了表结构，提高了配置的灵活性。
*/
