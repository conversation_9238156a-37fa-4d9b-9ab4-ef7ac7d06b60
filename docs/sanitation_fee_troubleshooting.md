# 卫生费配置问题排查文档

## 问题描述

1. 阶梯费用明细只显示3条，需要显示全部
2. submitForm 中存在旧数据处理逻辑
3. 将卫生费类型改为固定费用时，列表查不到这条固定费用的数据

## 问题解决

### 1. 阶梯费用明细全部显示

#### 修复前
```javascript
// 只显示前3个区间，避免表格过宽
return config.ranges.slice(0, 3).map(range => ({
  name: range.name,
  fee: range.fee || 0
}))
```

#### 修复后
```javascript
// 显示全部区间
return config.ranges.map(range => ({
  name: range.name,
  fee: range.fee || 0
}))
```

### 2. submitForm 旧数据清理

#### 修复前
```javascript
// 处理卫生费数据
if (this.form.sanitationFeeType === 1) {
  // 固定卫生费：将固定值设置到所有阶梯
  const fixedFee = this.form.fixedSanitationFee
  this.form.publicSanitationFee049 = fixedFee
  this.form.publicSanitationFee5059 = fixedFee
  this.form.publicSanitationFee6069 = fixedFee
  this.form.publicSanitationFee7079 = fixedFee
  this.form.publicSanitationFee80200 = fixedFee
  this.form.publicSanitationFee200 = fixedFee
}
```

#### 修复后
```javascript
// 数据验证
if (this.form.sanitationFeeType === 1) {
  // 固定费用：验证固定费用金额
  if (!this.form.fixedSanitationFee || this.form.fixedSanitationFee <= 0) {
    this.$modal.msgError("请输入有效的固定卫生费金额")
    return
  }
  // 清空阶梯费用配置
  this.form.tieredFeeConfig = null
} else if (this.form.sanitationFeeType === 2) {
  // 阶梯费用：验证JSON配置
  if (!this.form.tieredFeeConfig) {
    this.$modal.msgError("请配置阶梯费用")
    return
  }
  // 清空固定费用
  this.form.fixedSanitationFee = null
}
```

### 3. 固定费用数据查询问题排查

#### 可能的原因

1. **数据保存问题**：
   - 固定费用金额没有正确保存到数据库
   - 卫生费类型字段没有正确更新

2. **查询条件问题**：
   - 前端查询参数有误
   - 后端查询逻辑有问题

3. **缓存问题**：
   - 浏览器缓存导致数据不刷新
   - 后端缓存没有更新

#### 排查步骤

1. **检查数据库数据**：
```sql
-- 查看具体的小区数据
SELECT 
    id,
    community_name,
    sanitation_fee_type,
    fixed_sanitation_fee,
    tiered_fee_config
FROM community_mangement 
WHERE community_name = '你的小区名称';

-- 查看所有固定费用的小区
SELECT 
    id,
    community_name,
    sanitation_fee_type,
    fixed_sanitation_fee
FROM community_mangement 
WHERE sanitation_fee_type = 1;
```

2. **检查前端请求**：
```javascript
// 在 getList 方法中添加调试日志
getList() {
  this.loading = true
  console.log('查询参数:', this.queryParams)
  listCommunityMangement(this.queryParams).then(response => {
    console.log('查询结果:', response)
    this.communityMangementList = response.rows
    this.total = response.total
    this.loading = false
  })
}
```

3. **检查后端响应**：
```java
// 在 CommunityMangementController 中添加日志
@GetMapping("/list")
public TableDataInfo list(CommunityMangement communityMangement) {
    System.out.println("查询参数: " + communityMangement);
    startPage();
    List<CommunityMangement> list = communityMangementService.selectCommunityMangementList(communityMangement);
    System.out.println("查询结果数量: " + list.size());
    return getDataTable(list);
}
```

#### 解决方案

1. **确保数据正确保存**：
```javascript
// 在提交前验证数据
submitForm() {
  this.$refs["form"].validate(valid => {
    if (valid) {
      console.log('提交的表单数据:', this.form)
      
      // 数据验证和清理
      if (this.form.sanitationFeeType === 1) {
        if (!this.form.fixedSanitationFee || this.form.fixedSanitationFee <= 0) {
          this.$modal.msgError("请输入有效的固定卫生费金额")
          return
        }
        this.form.tieredFeeConfig = null
      } else if (this.form.sanitationFeeType === 2) {
        if (!this.form.tieredFeeConfig) {
          this.$modal.msgError("请配置阶梯费用")
          return
        }
        this.form.fixedSanitationFee = null
      }
      
      // 提交数据...
    }
  })
}
```

2. **检查数据库字段约束**：
```sql
-- 检查字段定义
DESCRIBE community_mangement;

-- 检查字段约束
SHOW CREATE TABLE community_mangement;
```

3. **清除缓存**：
```javascript
// 强制刷新列表
handleQuery() {
  this.queryParams.pageNum = 1
  // 清除可能的缓存
  this.communityMangementList = []
  this.getList()
}
```

## 测试验证

### 1. 功能测试

#### 测试用例1：固定费用配置
```
步骤：
1. 新建小区
2. 选择卫生费类型：固定费用
3. 输入固定费用金额：30元/月
4. 保存

预期结果：
- 保存成功
- 列表中显示该小区
- 卫生费类型显示"固定费用"
- 固定卫生费显示"30元/月"
```

#### 测试用例2：阶梯费用配置
```
步骤：
1. 新建小区
2. 选择卫生费类型：阶梯费用
3. 配置阶梯费用（多个区间）
4. 保存

预期结果：
- 保存成功
- 列表中显示该小区
- 卫生费类型显示"阶梯费用"
- 阶梯费用明细显示所有配置的区间
```

#### 测试用例3：类型切换
```
步骤：
1. 编辑现有小区
2. 将阶梯费用改为固定费用
3. 输入固定费用金额
4. 保存

预期结果：
- 保存成功
- 列表中正常显示
- 费用类型正确更新
- 相关字段正确清空
```

### 2. 数据验证

#### 验证数据库数据
```sql
-- 验证固定费用数据
SELECT * FROM community_mangement WHERE sanitation_fee_type = 1;

-- 验证阶梯费用数据
SELECT * FROM community_mangement WHERE sanitation_fee_type = 2;

-- 验证数据完整性
SELECT 
    sanitation_fee_type,
    COUNT(*) as count,
    COUNT(CASE WHEN sanitation_fee_type = 1 AND fixed_sanitation_fee IS NOT NULL THEN 1 END) as fixed_configured,
    COUNT(CASE WHEN sanitation_fee_type = 2 AND tiered_fee_config IS NOT NULL THEN 1 END) as tiered_configured
FROM community_mangement 
GROUP BY sanitation_fee_type;
```

#### 验证前端数据
```javascript
// 在浏览器控制台检查
console.log('小区列表数据:', this.communityMangementList)
console.log('查询参数:', this.queryParams)
```

## 常见问题

### Q1: 保存后列表不显示数据
**可能原因**：
- 数据没有正确保存到数据库
- 查询条件有误
- 前端缓存问题

**解决方法**：
1. 检查数据库数据是否正确保存
2. 检查前端查询参数
3. 清除浏览器缓存，强制刷新

### Q2: 固定费用显示为空
**可能原因**：
- fixed_sanitation_fee 字段为 NULL
- 前端显示逻辑有误

**解决方法**：
1. 确保保存时设置了正确的值
2. 检查前端模板中的显示条件

### Q3: 阶梯费用配置丢失
**可能原因**：
- tiered_fee_config 字段为 NULL 或空字符串
- JSON 格式错误

**解决方法**：
1. 验证 JSON 格式是否正确
2. 检查保存逻辑是否正确处理 JSON 数据

## 总结

通过以上修复：

1. ✅ **阶梯费用明细全部显示**：移除了3条限制，显示所有配置的区间
2. ✅ **清理旧数据逻辑**：移除了对已删除字段的操作，添加了正确的数据验证
3. ✅ **修复查询问题**：提供了完整的排查步骤和解决方案

如果问题仍然存在，建议按照排查步骤逐一检查数据库数据、前端请求和后端响应。
