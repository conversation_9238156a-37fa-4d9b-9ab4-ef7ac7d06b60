# 物业管理系统 (Property Management System)

[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)
[![Java](https://img.shields.io/badge/Java-8+-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7+-green.svg)](https://spring.io/projects/spring-boot)
[![Vue](https://img.shields.io/badge/Vue-2.6+-brightgreen.svg)](https://vuejs.org/)
[![MySQL](https://img.shields.io/badge/MySQL-5.7+-blue.svg)](https://www.mysql.com/)

## 📋 项目简介

物业管理系统是一个基于 Spring Boot + Vue 的现代化物业管理解决方案，旨在帮助物业公司提高管理效率，优化业主服务体验。系统涵盖了小区管理、业主管理、收费管理、账单管理等核心功能模块。

## ✨ 核心功能

### 🏢 小区管理
- 小区基础信息管理
- 楼栋房屋信息维护
- 物业费用标准配置
- 小区公告发布

### 👥 业主管理
- 业主档案管理
- 房屋产权信息
- 租户信息管理
- 停车位分配

### 💰 收费管理
- 多种费用类型支持（物业费、停车费、卫生费等）
- 灵活的收费方式（按月、按年、预缴）
- 混合支付功能
- 收费记录审核流程
- 自动生成收据

### 📊 账单管理
- 自动生成账单
- 账单周期配置
- 费用明细追踪
- 欠费统计分析

### 🔍 审核系统
- 收费记录审核
- 应缴与实收金额对比
- 审核状态跟踪
- 财务对账功能

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 2.7+
- **安全**: Spring Security + JWT
- **数据库**: MySQL 5.7+
- **ORM**: MyBatis Plus
- **缓存**: Redis
- **文档**: Swagger/Knife4j
- **构建工具**: Maven

### 前端技术
- **框架**: Vue 2.6+
- **UI组件**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Webpack

### 开发工具
- **IDE**: IntelliJ IDEA / VS Code
- **版本控制**: Git
- **API测试**: Postman
- **数据库工具**: Navicat / DataGrip

## 🚀 快速开始

### 环境要求
- JDK 8+
- Node.js 14+
- MySQL 5.7+
- Redis 6.0+

### 后端启动

1. **克隆项目**
```bash
git clone https://github.com/your-username/property-management-system.git
cd property-management-system
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE property_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入初始数据
mysql -u root -p property_management < sql/property_management.sql
```

3. **修改配置文件**
```yaml
# estatemanagement-system/src/main/resources/application.yml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

4. **启动后端服务**
```bash
cd estatemanagement-system
mvn clean install
mvn spring-boot:run
```

### 前端启动

1. **安装依赖**
```bash
cd estatemanagement-ui
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

3. **访问系统**
- 前端地址: http://localhost:80
- 后端API: http://localhost:8080
- 接口文档: http://localhost:8080/swagger-ui.html

### 默认账号
- 管理员: admin / admin123
- 财务: finance / finance123
- 收费员: cashier / cashier123

## 📁 项目结构

```
property-management-system/
├── estatemanagement-system/          # 后端项目
│   ├── src/main/java/
│   │   ├── com/estatemanagement/
│   │   │   ├── common/               # 公共模块
│   │   │   ├── framework/            # 框架配置
│   │   │   └── system/               # 业务模块
│   │   │       ├── controller/       # 控制器
│   │   │       ├── domain/           # 实体类
│   │   │       ├── mapper/           # 数据访问层
│   │   │       ├── service/          # 业务逻辑层
│   │   │       └── enums/            # 枚举类
│   │   └── resources/
│   │       ├── mapper/               # MyBatis映射文件
│   │       ├── db/migration/         # 数据库迁移脚本
│   │       └── application.yml       # 配置文件
│   └── pom.xml
├── estatemanagement-ui/              # 前端项目
│   ├── src/
│   │   ├── api/                      # API接口
│   │   ├── assets/                   # 静态资源
│   │   ├── components/               # 公共组件
│   │   ├── router/                   # 路由配置
│   │   ├── store/                    # 状态管理
│   │   ├── utils/                    # 工具函数
│   │   └── views/                    # 页面组件
│   │       └── system/               # 系统管理页面
│   ├── public/
│   └── package.json
├── sql/                              # 数据库脚本
├── docs/                             # 项目文档
└── README.md
```

## 🔧 核心功能详解

### 收费管理流程
1. **账单生成**: 系统根据配置自动生成月度账单
2. **费用收取**: 支持单项收费和混合支付
3. **记录审核**: 财务人员审核收费记录
4. **对账管理**: 应缴与实收金额对比分析

### 混合支付功能
- 支持同时缴纳多种费用类型
- 智能计算缴费周期
- 基于历史缴费记录的周期计算
- 车牌号关联的停车费管理

### 审核系统
- 三级审核状态：待审核、通过审核、审核拒绝
- 完整的审核轨迹记录
- 批量审核操作
- 审核统计分析

## 📊 系统特色

### 智能化管理
- **自动账单生成**: 根据配置自动生成月度/季度账单
- **智能费用计算**: 支持阶梯计费、优惠政策等复杂计费规则
- **预警提醒**: 欠费提醒、到期提醒等智能通知
- **数据分析**: 收费统计、趋势分析、财务报表

### 用户体验优化
- **响应式设计**: 支持PC、平板、手机多端访问
- **操作简便**: 一键批量操作，提高工作效率
- **实时更新**: 数据实时同步，状态即时反馈
- **打印功能**: 支持收据、报表等文档打印

### 数据安全
- **权限控制**: 细粒度权限管理，确保数据安全
- **操作日志**: 完整的操作审计轨迹
- **数据备份**: 自动数据备份机制
- **加密传输**: HTTPS + JWT 双重安全保障

## 🔐 权限管理

### 角色权限
- **系统管理员**: 全部功能权限
- **物业经理**: 业务管理权限
- **财务人员**: 收费审核权限
- **收费员**: 收费操作权限

### 功能权限
- `system:community:*` - 小区管理
- `system:owner:*` - 业主管理
- `system:paymentRecord:*` - 收费管理
- `system:paymentRecord:audit` - 收费审核
- `system:bill:*` - 账单管理

## 🚀 部署指南

### Docker 部署
```bash
# 克隆项目
git clone https://github.com/your-username/property-management-system.git
cd property-management-system

# 使用 Docker Compose 一键部署
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 手动部署

#### 1. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE property_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'property_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON property_management.* TO 'property_user'@'%';
FLUSH PRIVILEGES;

-- 导入数据结构和初始数据
mysql -u property_user -p property_management < sql/schema.sql
mysql -u property_user -p property_management < sql/data.sql
```

#### 2. 后端部署
```bash
# 编译打包
cd estatemanagement-system
mvn clean package -Dmaven.test.skip=true

# 运行应用
java -jar target/estatemanagement-system-2.1.0.jar --spring.profiles.active=prod
```

#### 3. 前端部署
```bash
# 安装依赖并构建
cd estatemanagement-ui
npm install
npm run build:prod

# 部署到 Nginx
cp -r dist/* /var/www/html/property-management/
```

#### 4. Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/html/property-management;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 环境配置

#### 生产环境配置文件
```yaml
# application-prod.yml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: property_user
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD}
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

logging:
  level:
    com.estatemanagement: info
    root: warn
  file:
    name: logs/property-management.log
```

### 性能优化建议

#### 数据库优化
- 为常用查询字段添加索引
- 定期清理历史数据
- 配置数据库连接池
- 启用查询缓存

#### 应用优化
- 启用 Redis 缓存
- 配置 JVM 参数
- 使用 CDN 加速静态资源
- 启用 Gzip 压缩

## ❓ 常见问题

### 安装问题

**Q: 启动时提示数据库连接失败？**
A: 请检查以下配置：
- 数据库服务是否启动
- 数据库连接信息是否正确
- 数据库用户权限是否足够
- 防火墙是否阻止连接

**Q: 前端页面空白或报错？**
A: 请尝试以下解决方案：
- 清除浏览器缓存
- 检查后端服务是否正常运行
- 查看浏览器控制台错误信息
- 确认 API 接口地址配置正确

**Q: 权限相关问题？**
A: 请确认：
- 用户角色配置正确
- 菜单权限已分配
- 接口权限已配置
- 清除浏览器缓存重新登录

### 性能问题

**Q: 系统响应慢？**
A: 优化建议：
- 检查数据库查询性能
- 启用 Redis 缓存
- 优化前端资源加载
- 增加服务器配置

**Q: 大量数据处理慢？**
A: 解决方案：
- 使用分页查询
- 添加数据库索引
- 异步处理大批量操作
- 定期清理历史数据

### 业务问题

**Q: 混合支付计算错误？**
A: 请检查：
- 账单周期配置
- 费用标准设置
- 历史缴费记录
- 计算逻辑参数

**Q: 审核功能异常？**
A: 排查步骤：
- 确认用户有审核权限
- 检查审核状态流转
- 查看系统日志
- 验证数据完整性

## 🤝 贡献指南

### 参与贡献
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范
- **代码规范**: 遵循阿里巴巴 Java 开发手册
- **前端规范**: 使用 ESLint + Prettier 进行代码检查
- **提交规范**: 使用 Conventional Commits 规范
- **测试要求**: 新功能需要编写单元测试
- **文档更新**: 重要变更需要更新相关文档

### 代码提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 开发环境搭建
```bash
# 1. 克隆项目
git clone https://github.com/your-username/property-management-system.git

# 2. 安装后端依赖
cd estatemanagement-system
mvn clean install

# 3. 安装前端依赖
cd ../estatemanagement-ui
npm install

# 4. 启动开发环境
npm run dev
```

## 📝 更新日志

### v2.1.0 (2025-07-04)
- ✨ 新增收费记录审核功能
- ✨ 新增混合支付功能
- ✨ 新增车牌号管理
- 🐛 修复缴费周期计算问题
- 📈 优化查询性能

### v2.0.0 (2025-06-01)
- ✨ 重构前端界面
- ✨ 新增账单自动生成
- ✨ 新增费用统计分析
- 🔧 优化数据库结构

### v1.0.0 (2025-01-01)
- 🎉 项目初始版本发布
- ✨ 基础功能模块完成

## 📞 联系我们

- 📧 邮箱: <EMAIL>
- 🌐 官网: https://www.property-management.com
- 📱 微信群: 扫描二维码加入技术交流群

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目的支持：
- [Spring Boot](https://spring.io/projects/spring-boot)
- [Vue.js](https://vuejs.org/)
- [Element UI](https://element.eleme.io/)
- [MyBatis Plus](https://baomidou.com/)

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
