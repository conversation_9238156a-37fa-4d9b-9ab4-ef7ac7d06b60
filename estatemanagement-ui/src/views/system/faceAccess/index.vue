<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <el-select v-model="queryParams.communityId" placeholder="请选择小区" clearable filterable>
          <el-option v-for="item in communityOptions" :key="item.id" :label="item.communityName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="人员姓名" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="人员类型" prop="personType">
        <el-select v-model="queryParams.personType" placeholder="请选择人员类型" clearable>
          <el-option label="业主" :value="1"></el-option>
          <el-option label="租客" :value="2"></el-option>
          <el-option label="家属" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:faceAccess:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:faceAccess:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:faceAccess:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:faceAccess:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="faceAccessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="小区名称" align="center" prop="communityName" />
      <el-table-column label="楼号" align="center" prop="buildingNumber" />
      <el-table-column label="门牌号" align="center" prop="houseNumber" />
      <el-table-column label="业主名字" align="center" prop="ownerName" />
      <el-table-column label="租客名字" align="center" prop="tenantName">
        <template slot-scope="scope">
          <span>{{ scope.row.rentalStatus === '1' ? scope.row.tenantName : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="人员姓名" align="center" prop="personName" />
      <el-table-column label="人员类型" align="center" prop="personType">
        <template slot-scope="scope">
          <span>{{ getPersonTypeLabel(scope.row.personType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开通时间" align="center" prop="openTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.openTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到期时间" align="center" prop="expireTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ getStatusLabel(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:faceAccess:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:faceAccess:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改人脸识别开通记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区" prop="communityId">
          <el-select v-model="form.communityId" placeholder="请选择小区" filterable @change="handleCommunityChange">
            <el-option v-for="item in communityOptions" :key="item.id" :label="item.communityName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业主" prop="ownerId">
          <el-select v-model="form.ownerId" placeholder="请选择业主" filterable>
            <el-option v-for="item in ownerOptions" :key="item.id" :label="`${item.ownerName} - ${item.buildingNumber}${item.houseNumber}`" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人员姓名" prop="personName">
          <el-input v-model="form.personName" placeholder="请输入人员姓名" />
        </el-form-item>
        <el-form-item label="人员类型" prop="personType">
          <el-select v-model="form.personType" placeholder="请选择人员类型">
            <el-option label="业主" :value="1"></el-option>
            <el-option label="租客" :value="2"></el-option>
            <el-option label="家属" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开通时间" prop="openTime">
          <el-date-picker clearable
            v-model="form.openTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开通时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间" prop="expireTime">
          <el-date-picker clearable
            v-model="form.expireTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择到期时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFaceAccess, getFaceAccess, delFaceAccess, addFaceAccess, updateFaceAccess } from "@/api/system/faceAccess"
import { listCommunityMangement } from "@/api/system/communityMangement"
import { listOwnerMangement } from "@/api/system/ownerMangement"

export default {
  name: "FaceAccess",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人脸识别开通记录表格数据
      faceAccessList: [],
      // 小区列表
      communityOptions: [],
      // 业主列表
      ownerOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        tenantId: null,
        personName: null,
        personType: null,
        openTime: null,
        expireTime: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "小区ID不能为空", trigger: "blur" }
        ],
        personName: [
          { required: true, message: "人员姓名不能为空", trigger: "blur" }
        ],
        personType: [
          { required: true, message: "人员类型(1业主,2租客,3家属)不能为空", trigger: "change" }
        ],
        openTime: [
          { required: true, message: "开通时间不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getCommunityList()
  },
  methods: {
    /** 查询人脸识别开通记录列表 */
    getList() {
      this.loading = true
      listFaceAccess(this.queryParams).then(response => {
        this.faceAccessList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询小区列表 */
    getCommunityList() {
      listCommunityMangement().then(response => {
        this.communityOptions = response.rows
      })
    },
    /** 查询业主列表 */
    getOwnerList(communityId) {
      if (communityId) {
        listOwnerMangement({ communityId: communityId }).then(response => {
          this.ownerOptions = response.rows
        })
      } else {
        this.ownerOptions = []
      }
    },
    /** 小区选择变化处理 */
    handleCommunityChange(communityId) {
      this.form.ownerId = null
      this.getOwnerList(communityId)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        tenantId: null,
        personName: null,
        personType: 1,
        openTime: null,
        expireTime: null,
        status: null,
        createBy: null,
        createTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加人脸识别开通记录"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getFaceAccess(id).then(response => {
        this.form = response.data
        // 确保 personType 是数字类型
        if (this.form.personType !== null && this.form.personType !== undefined) {
          this.form.personType = Number(this.form.personType)
        } else {
          this.form.personType = 1
        }
        this.open = true
        this.title = "修改人脸识别开通记录"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFaceAccess(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addFaceAccess(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除人脸识别开通记录编号为"' + ids + '"的数据项？').then(function() {
        return delFaceAccess(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/faceAccess/export', {
        ...this.queryParams
      }, `faceAccess_${new Date().getTime()}.xlsx`)
    },
    /** 获取人员类型标签 */
    getPersonTypeLabel(personType) {
      const typeMap = {
        1: '业主',
        2: '租客',
        3: '家属'
      }
      return typeMap[personType] || '未知'
    },
    /** 获取状态标签 */
    getStatusLabel(status) {
      const statusMap = {
        1: '有效',
        0: '禁用'
      }
      return statusMap[status] || '未知'
    }
  }
}
</script>
