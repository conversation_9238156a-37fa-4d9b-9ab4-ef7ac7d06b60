<template>
  <div class="app-container">
    <!-- 审核流程提醒 -->
    <el-alert
      title="审核流程更新提醒："
      type="warning"
      show-icon
      :closable="true"
      style="margin-bottom: 15px;"
      description="重要变更：为确保费用管理的准确性，现在只有在审核通过后，系统才会更新业主的物业费、卫生费到期时间以及车牌的停车费到期时间。收费时不再立即更新到期时间。"
    />
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <CommunitySelect
          v-model="queryParams.communityId"
          @change="handleCommunityChange"
        />
      </el-form-item>
      <el-form-item label="业主" prop="ownerId">
        <OwnerSelect
          v-model="queryParams.ownerId"
          :community-id="queryParams.communityId"
          placeholder="请选择业主"
        />
      </el-form-item>
      <el-form-item label="收据编号" prop="receiptNumber">
        <el-input
          v-model="queryParams.receiptNumber"
          placeholder="请输入收据编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付方式" prop="paymentMethod">
        <el-select v-model="queryParams.paymentMethod" placeholder="请选择支付方式" clearable>
          <el-option label="现金" value="现金"></el-option>
          <el-option label="银行转账" value="银行转账"></el-option>
          <el-option label="微信支付" value="微信支付"></el-option>
          <el-option label="支付宝" value="支付宝"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="缴费日期" prop="paymentDate">
        <el-date-picker clearable
          v-model="queryParams.paymentDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择缴费日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option label="待审核" value="0"></el-option>
          <el-option label="通过审核" value="1"></el-option>
          <el-option label="审核拒绝" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:paymentRecord:add']"
        >收费</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:paymentRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:paymentRecord:export']"
        >导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="paymentRecordList"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      highlight-current-row>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="收据编号" align="center" prop="receiptNumber" min-width="140" />
      <el-table-column label="业主信息" align="center" min-width="180">
        <template slot-scope="scope">
          <div>
            <div style="font-weight: bold;">{{ scope.row.buildingNumber }}-{{ scope.row.houseNumber }}</div>
            <div style="color: #909399; font-size: 12px;">{{ scope.row.ownerName }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="应缴金额" align="center" prop="dueAmount" min-width="100">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">{{ formatCurrency(scope.row.dueAmount || scope.row.paymentAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实收金额" align="center" prop="paymentAmount" min-width="100">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">{{ formatCurrency(scope.row.paymentAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.auditStatus === 0" type="warning" size="mini">待审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 1" type="success" size="mini">通过审核</el-tag>
          <el-tag v-else-if="scope.row.auditStatus === 2" type="danger" size="mini">审核拒绝</el-tag>
          <el-tag v-else type="info" size="mini">未知状态</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="缴费日期" align="center" prop="paymentDate" min-width="110">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="320">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleRowClick(scope.row, $event)"
            v-hasPermi="['system:paymentRecord:query']"
          >查看详情</el-button>
          <el-button
            v-if="scope.row.auditStatus === 0 "
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row, $event)"
            v-hasPermi="['system:paymentRecord:audit']"
            style="color: #409EFF;"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-printer"
            @click="handlePrint(scope.row, $event)"
            v-hasPermi="['system:paymentRecord:print']"
          >打印收费单</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, $event)"
            v-hasPermi="['system:paymentRecord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 金额汇总 -->
    <div class="summary-container" v-if="paymentRecordList.length > 0">
      <div class="summary-header">
        <i class="el-icon-data-line"></i>
        <span>数据汇总</span>
        <el-button
          type="text"
          size="mini"
          @click="toggleSummaryDetail"
          style="margin-left: auto;">
          {{ showDetailSummary ? '收起详情' : '展开详情' }}
          <i :class="showDetailSummary ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        </el-button>
      </div>

      <!-- 基础汇总 -->
      <el-row :gutter="20" class="summary-row">
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="summary-content">
              <div class="summary-label">本页记录数</div>
              <div class="summary-value">{{ paymentRecordList.length }} 条</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon due-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="summary-content">
              <div class="summary-label">应收金额汇总</div>
              <div class="summary-value due-amount">{{ formatCurrency(totalDueAmount) }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon actual-icon">
              <i class="el-icon-wallet"></i>
            </div>
            <div class="summary-content">
              <div class="summary-label">实收金额汇总</div>
              <div class="summary-value actual-amount">{{ formatCurrency(totalActualAmount) }}</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-icon" :class="differenceIconClass">
              <i class="el-icon-sort"></i>
            </div>
            <div class="summary-content">
              <div class="summary-label">差额</div>
              <div class="summary-value" :class="differenceClass">{{ formatCurrency(totalDifference) }}</div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 详细汇总 -->
      <div v-show="showDetailSummary" class="detail-summary">
        <el-divider content-position="left">
          <span style="color: #909399; font-size: 12px;">详细统计</span>
        </el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">审核状态统计：</span>
              <div class="audit-stats">
                <el-tag type="warning" size="mini">待审核: {{ auditStatusStats.pending }}条</el-tag>
                <el-tag type="success" size="mini">已通过: {{ auditStatusStats.approved }}条</el-tag>
                <el-tag type="danger" size="mini">已拒绝: {{ auditStatusStats.rejected }}条</el-tag>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">支付方式统计：</span>
              <div class="payment-method-stats">
                <span v-for="(count, method) in paymentMethodStats" :key="method" class="method-stat">
                  {{ method }}: {{ count }}条
                </span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="detail-label">平均金额：</span>
              <div class="avg-amount">
                <span>应收: {{ formatCurrency(avgDueAmount) }}</span>
                <span>实收: {{ formatCurrency(avgActualAmount) }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 收费操作对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 第一步：选择小区和业主 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择小区" prop="communityId">
              <CommunitySelect
                v-model="form.communityId"
                @change="handleFormCommunityChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择业主" prop="ownerId">
              <OwnerSelect
                v-model="form.ownerId"
                :community-id="form.communityId"
                @change="handleOwnerChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二步：选择账单 -->
        <el-form-item label="选择账单" prop="billId" v-if="unpaidBills.length > 0">
          <el-table
            :data="unpaidBills"
            @selection-change="handleBillSelection"
            ref="billTable"
            highlight-current-row
            @current-change="handleCurrentBillChange">
            <el-table-column type="selection" width="55" align="center" :selectable="() => true" />
            <el-table-column label="账单编号" prop="bill_number"  />
            <el-table-column label="账单周期" width="200">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.bill_period_start, '{y}-{m}-{d}') }} ~ {{ parseTime(scope.row.bill_period_end, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总金额" prop="total_amount" >
              <template slot-scope="scope">
                <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.total_amount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="费用明细" >
              <template slot-scope="scope">
                <div class="fee-details-container">
                  <el-tag v-if="scope.row.property_fee > 0" type="primary" size="mini" style="margin: 2px;">
                    物业费: {{ scope.row.property_fee }}
                  </el-tag>
                  <el-tag v-if="scope.row.parking_fee > 0" type="success" size="mini" style="margin: 2px;">
                    停车费: {{ scope.row.parking_fee }}
                  </el-tag>
                  <el-tag v-if="scope.row.sanitation_fee > 0" type="warning" size="mini" style="margin: 2px;">
                    卫生费: {{ scope.row.sanitation_fee }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="已支付" prop="paid_amount" />
            <el-table-column label="支付状态" prop="payment_status" >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.payment_status === 0" type="danger">未支付</el-tag>
                <el-tag v-else-if="scope.row.payment_status === 1" type="warning">部分支付</el-tag>
                <el-tag v-else type="success">已支付</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <!-- 没有账单时的提示 -->
        <el-form-item v-if="form.ownerId && unpaidBills.length === 0">
          <el-alert
            title="该业主暂无未支付账单，只能进行预交费用操作"
            type="info"
            :closable="false">
          </el-alert>
        </el-form-item>

        <!-- 第三步：选择收费类型和金额 -->
        <div v-if="selectedBill || (form.ownerId && unpaidBills.length === 0)">
          <el-divider>收费设置</el-divider>
          <el-form-item label="收费类型" prop="paymentType">
            <!-- 有账单时显示所有选项 -->
            <el-radio-group v-if="selectedBill" v-model="form.paymentType" @change="handlePaymentTypeChange">
              <el-radio label="full">全额支付</el-radio>
              <el-radio label="mixed">部分支付</el-radio>
              <el-radio label="advance">预交费用</el-radio>
            </el-radio-group>
            <!-- 没有账单时显示预交费用和部分支付 -->
            <el-radio-group v-else v-model="form.paymentType" @change="handlePaymentTypeChange">
              <el-radio label="advance">预交费用</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 全额支付 -->
          <div v-if="form.paymentType === 'full'">
            <el-form-item label="应收金额">
              <span style="font-size: 18px; color: #E6A23C; font-weight: bold;">{{ calculateTotalUnpaid() }} 元</span>
            </el-form-item>
          </div>

          <!-- 部分支付 -->
          <div v-if="form.paymentType === 'mixed'">

            <!-- 费用类型选择 -->
            <el-form-item label="包含费用:">
              <el-checkbox-group v-model="form.mixedFeeTypes" @change="calculateMixedAmount">
                <el-checkbox label="1">物业费</el-checkbox>
                <el-checkbox label="2">停车费</el-checkbox>
                <el-checkbox label="3">卫生费</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="支付月数" prop="mixedPaymentMonths">
                  <el-input-number v-model="form.mixedPaymentMonths" :min="1" :max="999" @change="calculateMixedAmount"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="应收金额">
                  <span style="font-size: 18px; color: #E6A23C; font-weight: bold;">{{ form.mixedCalculatedAmount || 0 }} 元</span>
                </el-form-item>
              </el-col>
            </el-row>


            <!-- 费用明细显示 -->
            <el-form-item label="费用明细" v-if="form.mixedFeeDetails && form.mixedFeeDetails.length > 0">
              <el-table :data="form.mixedFeeDetails" border size="small">
                <el-table-column label="费用类型" prop="feeTypeName" width="80" />
                <el-table-column label="计算规则" width="200">
                  <template slot-scope="scope">
                    <div class="calculation-rule">
                      <span v-if="scope.row.feeType === 1" class="rule-text">
                        <i class="el-icon-house"></i>
                        {{ scope.row.unitPrice || 0 }}元/㎡ × {{ scope.row.area || 0 }}㎡
                      </span>
                      <span v-else-if="scope.row.feeType === 2" class="rule-text">
                        <i class="el-icon-truck"></i>
                        {{ scope.row.parkingType === 1 ? '三证合一' : '非三证合一' }}停车位
                        <span v-if="scope.row.spaceNumber">({{ scope.row.spaceNumber }})</span>
                      </span>
                      <span v-else-if="scope.row.feeType === 3" class="rule-text">
                        <i class="el-icon-delete-solid"></i>
                        <span>{{ scope.row.areaRange || '未知范围' }}
                          <span v-if="scope.row.area">({{ scope.row.area }}㎡)</span>
                        </span>
                      </span>

                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="月费用" prop="monthlyFee" width="80">
                  <template slot-scope="scope">
                    <span>{{ scope.row.monthlyFee }} 元</span>
                  </template>
                </el-table-column>
                <el-table-column label="支付月数" prop="months" width="100" />
                <el-table-column label="小计" prop="totalAmount" width="100">
                  <template slot-scope="scope">
                    <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.totalAmount }} 元</span>
                  </template>
                </el-table-column>
                <el-table-column label="缴费周期" prop="period">
                  <template slot-scope="scope">
                    <span>{{ scope.row.startDate }} 至 {{ scope.row.endDate }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>

            <!-- 停车费时显示车位选择 -->
            <el-row v-if="form.mixedFeeTypes.includes('2')" :gutter="20">
              <el-col :span="24">
                <el-form-item label="选择车位" prop="selectedParkingSpaces">
                  <div class="parking-selection">
                    <div class="selection-header">
                      <span class="selection-title">可选车位列表：</span>
                      <el-button type="text" size="mini" @click="selectAllParkingSpaces">
                        {{ isAllParkingSelected ? '取消全选' : '全选' }}
                      </el-button>
                    </div>
                    <div class="parking-list">
                      <el-checkbox-group v-model="form.selectedParkingSpaces" @change="handleParkingSelectionChange">
                        <div v-for="parking in ownerParkingList" :key="parking.plateNumber" class="parking-item">
                          <el-checkbox :label="parking.plateNumber" class="parking-checkbox">
                            <div class="parking-info">
                              <div class="parking-main">
                                <span class="plate-number">{{ parking.plateNumber }}</span>
                                <span class="space-number" v-if="parking.spaceNumber">({{ parking.spaceNumber }})</span>
                              </div>
                              <div class="parking-details">
                                <el-tag :type="parking.parkingType === 1 ? 'success' : 'warning'" size="mini">
                                  {{ parking.parkingType === 1 ? '三证合一' : '非三证合一' }}
                                </el-tag>
                                <span class="monthly-fee">{{ parking.monthlyFee || 0 }}元/月</span>
                              </div>
                            </div>
                          </el-checkbox>
                        </div>
                      </el-checkbox-group>
                    </div>
                    <div class="selection-summary" v-if="form.selectedParkingSpaces.length > 0">
                      <span>已选择 {{ form.selectedParkingSpaces.length }} 个车位，预计月费用：{{ calculateSelectedParkingFee() }} 元</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 预交费用 -->
          <div v-if="form.paymentType === 'advance'">
            <!-- 费用类型选择 -->
            <el-form-item label="包含费用:">
              <el-checkbox-group v-model="form.advanceFeeTypes" @change="calculateAdvanceAmount">
                <el-checkbox label="1">物业费</el-checkbox>
                <el-checkbox label="2">停车费</el-checkbox>
                <el-checkbox label="3">卫生费</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预交月数" prop="advanceMonths">
                  <el-input-number v-model="form.advanceMonths" :min="1" :max="999" @change="calculateAdvanceAmount"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应收金额">
                  <span style="font-size: 18px; color: #E6A23C; font-weight: bold;">{{ form.advanceCalculatedAmount || 0 }} 元</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 停车费时显示车位选择 -->
            <el-row v-if="form.advanceFeeTypes.includes('2')" :gutter="20">
              <el-col :span="24">
                <el-form-item label="选择车位" prop="advanceSelectedParkingSpaces">
                  <div class="parking-selection">
                    <div class="selection-header">
                      <span class="selection-title">可选车位列表：</span>
                      <el-button type="text" size="mini" @click="selectAllAdvanceParkingSpaces">
                        {{ isAllAdvanceParkingSelected ? '取消全选' : '全选' }}
                      </el-button>
                    </div>
                    <div class="parking-list">
                      <el-checkbox-group v-model="form.advanceSelectedParkingSpaces" @change="handleAdvanceParkingSelectionChange">
                        <div v-for="parking in ownerParkingList" :key="parking.plateNumber" class="parking-item">
                          <el-checkbox :label="parking.plateNumber" class="parking-checkbox">
                            <div class="parking-info">
                              <div class="parking-main">
                                <span class="plate-number">{{ parking.plateNumber }}</span>
                                <span class="space-number" v-if="parking.spaceNumber">({{ parking.spaceNumber }})</span>
                              </div>
                              <div class="parking-details">
                                <el-tag :type="parking.parkingType === 1 ? 'success' : 'warning'" size="mini">
                                  {{ parking.parkingType === 1 ? '三证合一' : '非三证合一' }}
                                </el-tag>
                                <span class="monthly-fee">{{ parking.monthlyFee || 0 }}元/月</span>
                              </div>
                            </div>
                          </el-checkbox>
                        </div>
                      </el-checkbox-group>
                    </div>
                    <div class="selection-summary" v-if="form.advanceSelectedParkingSpaces.length > 0">
                      <span>已选择 {{ form.advanceSelectedParkingSpaces.length }} 个车位，预计月费用：{{ calculateAdvanceSelectedParkingFee() }} 元</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 费用明细表格 -->
            <el-form-item label="费用明细" v-if="form.advanceFeeDetails.length > 0">
              <el-table :data="form.advanceFeeDetails" border size="small">
                <el-table-column label="费用类型" prop="feeTypeName" width="80" />
                <el-table-column label="计算规则" width="200">
                  <template slot-scope="scope">
                    <div class="calculation-rule">
                      <span v-if="scope.row.feeType === 1" class="rule-text">
                        <i class="el-icon-house"></i>
                        {{ scope.row.unitPrice || 0 }}元/㎡ × {{ scope.row.area || 0 }}㎡
                      </span>
                      <span v-else-if="scope.row.feeType === 2" class="rule-text">
                        <i class="el-icon-truck"></i>
                        {{ scope.row.parkingType === 1 ? '三证合一' : '非三证合一' }}停车位
                        <span v-if="scope.row.spaceNumber">({{ scope.row.spaceNumber }})</span>
                      </span>
                      <span v-else-if="scope.row.feeType === 3" class="rule-text">
                        <i class="el-icon-delete-solid"></i>
                        <span > {{ scope.row.areaRange || '未知范围' }}
                          <span v-if="scope.row.area">({{ scope.row.area }}㎡)</span>
                        </span>
                      </span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="月费用" prop="monthlyFee" width="80">
                  <template slot-scope="scope">
                    <span>{{ scope.row.monthlyFee }} 元</span>
                  </template>
                </el-table-column>
                <el-table-column label="预交月数" prop="months" width="80" />
                <el-table-column label="小计" prop="totalAmount" width="100">
                  <template slot-scope="scope">
                    <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.totalAmount }} 元</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </div>

          <el-form-item label="支付方式" prop="paymentMethod">
            <el-select v-model="form.paymentMethod" placeholder="请选择支付方式">
              <el-option label="现金" value="现金"></el-option>
              <el-option label="银行转账" value="银行转账"></el-option>
              <el-option label="微信支付" value="微信支付"></el-option>
              <el-option label="支付宝" value="支付宝"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="!selectedBill && !(form.ownerId && unpaidBills.length === 0)">确认收费</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 账单配置对话框 -->
    <el-dialog title="账单生成配置" :visible.sync="billConfigOpen" width="800px" append-to-body>
      <el-form ref="configForm" :model="configForm" :rules="configRules" label-width="120px">
        <el-form-item label="选择小区" prop="communityId">
          <CommunitySelect
            v-model="configForm.communityId"
            @change="handleConfigCommunityChange"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="启用自动生成" prop="autoGenerate">
          <el-switch v-model="configForm.autoGenerate" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
        <el-form-item label="生成间隔天数" prop="intervalDays" v-if="configForm.autoGenerate === 1">
          <el-input-number v-model="configForm.intervalDays" :min="1" :max="365" placeholder="请输入间隔天数"></el-input-number>
          <span style="margin-left: 10px; color: #909399;">天</span>
        </el-form-item>
        <el-form-item label="生成类型" prop="generateType">
          <el-select v-model="configForm.generateType" placeholder="请选择生成类型" style="width: 100%">
            <el-option label="全部费用" value="all"></el-option>
            <el-option label="物业费" value="property"></el-option>
            <el-option label="停车费" value="parking"></el-option>
            <el-option label="卫生费" value="sanitation"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="configForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="configForm.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBillConfig">确 定</el-button>
        <el-button @click="billConfigOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="收费记录审核" :visible.sync="auditOpen" width="600px" append-to-body>
      <!-- 审核流程说明 -->
      <el-alert
        title="审核流程说明"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;">
        <div slot="description">
          <p style="margin: 0 0 8px 0;"><strong>重要提醒：</strong></p>
          <p style="margin: 0 0 5px 0;">• <strong>审核通过后</strong>，系统将自动更新业主的费用到期时间</p>
          <p style="margin: 0 0 5px 0;">• <strong>物业费</strong>：更新业主的物业费到期时间</p>
          <p style="margin: 0 0 5px 0;">• <strong>卫生费</strong>：更新业主的卫生费到期时间</p>
          <p style="margin: 0 0 5px 0;">• <strong>停车费</strong>：更新对应车牌的停车费到期时间</p>
          <p style="margin: 0; color: #E6A23C;">• <strong>审核拒绝</strong>时，不会更新任何到期时间</p>
        </div>
      </el-alert>

      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="收据编号">
          <el-input v-model="auditForm.receiptNumber" disabled></el-input>
        </el-form-item>
        <el-form-item label="业主信息">
          <el-input v-model="auditForm.ownerInfo" disabled></el-input>
        </el-form-item>
        <el-form-item label="应缴金额">
          <el-input v-model="auditForm.dueAmount" disabled>
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="实收金额" prop="actualAmount">
          <el-input-number
            v-model="auditForm.actualAmount"
            :precision="2"
            :step="0.01"
            :min="0"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        <el-form-item label="审核结果" prop="auditStatus">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio :label="1">
              <span style="color: #67C23A;">通过审核</span>
              <span style="color: #909399; font-size: 12px; margin-left: 8px;">（将更新到期时间）</span>
            </el-radio>
            <el-radio :label="2">
              <span style="color: #F56C6C;">审核拒绝</span>
              <span style="color: #909399; font-size: 12px; margin-left: 8px;">（不更新到期时间）</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditComment">
          <el-input
            v-model="auditForm.auditComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit" :loading="auditLoading">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 右侧明细弹窗 -->
    <el-drawer
      title="收费记录详情"
      :visible.sync="detailDrawerVisible"
      direction="rtl"
      size="800px"
      :before-close="handleDetailDrawerClose">
      <div class="detail-drawer-content" v-if="selectedRecord">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>收据编号：</label>
                <span class="detail-value">{{ selectedRecord.receiptNumber }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>账单编号：</label>
                <span class="detail-value">{{ selectedRecord.billNumber || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>小区名称：</label>
                <span class="detail-value">{{ selectedRecord.communityName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>楼号门牌：</label>
                <span class="detail-value">{{ selectedRecord.buildingNumber }}-{{ selectedRecord.houseNumber }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>业主姓名：</label>
                <span class="detail-value">{{ selectedRecord.ownerName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>租客姓名：</label>
                <span class="detail-value">{{ selectedRecord.tenantName || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 费用信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-money"></i>
            <span>费用信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>支付方式：</label>
                <span class="detail-value">{{ selectedRecord.paymentMethod }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>应缴金额：</label>
                <span class="detail-value amount due-amount">{{ formatCurrency(selectedRecord.dueAmount || selectedRecord.paymentAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>实收金额：</label>
                <span class="detail-value amount actual-amount">{{ formatCurrency(selectedRecord.paymentAmount) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <!-- 费用明细 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-s-order"></i>
            <span>费用明细</span>
          </div>
          <div v-loading="paymentDetailsLoading">
            <el-table
              :data="paymentDetailsList"
              border
              size="small"
              style="width: 100%"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
              <el-table-column label="费用类型" prop="fee_type" width="80" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getFeeTypeTagType(scope.row.fee_type)"
                    size="mini">
                    {{ scope.row.fee_type_name }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="缴费周期" align="center" min-width="160">
                <template slot-scope="scope">
                  <span v-if="scope.row.period_start && scope.row.period_end">
                    {{ parseTime(scope.row.period_start, '{y}-{m}-{d}') }} ~ {{ parseTime(scope.row.period_end, '{y}-{m}-{d}') }}
                  </span>
                  <span v-else style="color: #C0C4CC;">-</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="payment_amount" width="100" align="center">
                <template slot-scope="scope">
                  <span style="color: #E6A23C; font-weight: bold;">{{ formatCurrency(scope.row.payment_amount) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="缴费月数" prop="payment_months" width="100" align="center">
                <template slot-scope="scope">
                  <span>{{ scope.row.payment_months || 0 }} 个月</span>
                </template>
              </el-table-column>
              <el-table-column label="车牌号" prop="plate_number" width="100" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.plate_number">{{ scope.row.plate_number }}</span>
                  <span v-else style="color: #C0C4CC;">-</span>
                </template>
              </el-table-column>
            </el-table>

            <!-- 费用明细汇总 -->
            <div class="detail-summary" v-if="paymentDetailsList.length > 0">
              <div class="summary-item">
                <span class="summary-label">明细条数：</span>
                <span class="summary-value">{{ paymentDetailsList.length }} 条</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">费用总计：</span>
                <span class="summary-value total-amount">{{ formatCurrency(getTotalAmount()) }}</span>
              </div>
            </div>

            <!-- 无明细提示 -->
            <div v-if="paymentDetailsList.length === 0" class="no-details">
              <i class="el-icon-info"></i>
              <span>暂无费用明细记录</span>
            </div>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="detail-section" v-if="selectedRecord.auditStatus !== undefined">
          <div class="section-title">
            <i class="el-icon-check"></i>
            <span>审核信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>审核状态：</label>
                <el-tag v-if="selectedRecord.auditStatus === 0" type="warning" size="mini">待审核</el-tag>
                <el-tag v-else-if="selectedRecord.auditStatus === 1" type="success" size="mini">通过审核</el-tag>
                <el-tag v-else-if="selectedRecord.auditStatus === 2" type="danger" size="mini">审核拒绝</el-tag>
                <el-tag v-else type="info" size="mini">未知状态</el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>审核人：</label>
                <span class="detail-value">{{ selectedRecord.auditorName || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row" v-if="selectedRecord.auditTime">
            <el-col :span="24">
              <div class="detail-item">
                <label>审核时间：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.auditTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row" v-if="selectedRecord.auditComment">
            <el-col :span="24">
              <div class="detail-item">
                <label>审核意见：</label>
                <div class="detail-value audit-comment">{{ selectedRecord.auditComment }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-time"></i>
            <span>时间信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>缴费日期：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.paymentDate, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>创建时间：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>


        <!-- 备注信息 -->
        <div class="detail-section" v-if="selectedRecord.remark">
          <div class="section-title">
            <i class="el-icon-edit-outline"></i>
            <span>备注信息</span>
          </div>
          <div class="detail-remark">
            {{ selectedRecord.remark }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button
            v-if="selectedRecord.auditStatus === 0"
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="handleAuditFromDetail"
            v-hasPermi="['system:paymentRecord:audit']">
            审核
          </el-button>
          <el-button
            type="success"
            size="small"
            icon="el-icon-printer"
            @click="handlePrintFromDetail"
            v-hasPermi="['system:paymentRecord:print']">
            打印收据
          </el-button>

        </div>
      </div>
    </el-drawer>

    <!-- 打印组件 -->
    <print-component ref="printComponent">
      <payment-receipt
        :paymentData="printPaymentData"
        :paymentList="printPaymentList">
      </payment-receipt>
    </print-component>
  </div>
</template>

<script>
import { listPaymentRecord, getPaymentRecord, delPaymentRecord, addPaymentRecord, updatePaymentRecord, getUnpaidBillsForPayment, processPayment, calculateAdvanceFee, getPaymentDetails, getPaymentRecordWithDetails, generateBills, getBillConfigByCommunityId, addBillConfig, updateBillConfig, calculateMixedPaymentAmount, auditPaymentRecord } from "@/api/system/paymentRecord"
import PrintComponent from "@/components/Print/index.vue"
import PaymentReceipt from "@/components/Print/PaymentReceipt.vue"
import { OwnerSelect, CommunitySelect } from "@/components"

export default {
  name: "PaymentRecord",
  components: {
    PrintComponent,
    PaymentReceipt,
    OwnerSelect,
    CommunitySelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收费记录表格数据
      paymentRecordList: [],

      // 未支付账单列表
      unpaidBills: [],
      // 选中的账单
      selectedBill: null,
      // 业主停车位列表
      ownerParkingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        receiptNumber: null,
        paymentMethod: null,
        paymentDate: null,
        auditStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        ownerId: [
          { required: true, message: "请选择业主", trigger: "change" }
        ],
        billId: [
          { required: true, message: "请选择账单", trigger: "change" }
        ],
        paymentType: [
          { required: true, message: "请选择收费类型", trigger: "change" }
        ],
        paymentMethod: [
          { required: true, message: "请选择支付方式", trigger: "change" }
        ],
        advanceFeeTypes: [
          {
            validator: (rule, value, callback) => {
              if (this.form.paymentType === 'advance' && (!value || value.length === 0)) {
                callback(new Error('请选择费用类型'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        advanceMonths: [
          {
            validator: (rule, value, callback) => {
              if (this.form.paymentType === 'advance' && (!value || value <= 0)) {
                callback(new Error('请输入有效的预交月数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      // 当前收费记录
      currentPaymentRecord: null,
      // 费用明细
      paymentDetails: [],
      // 打印相关数据
      printPaymentData: null,
      printPaymentList: [],
      // 生成账单对话框
      generateBillOpen: false,
      generateForm: {},
      generateRules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        generateType: [
          { required: true, message: "请选择生成类型", trigger: "change" }
        ]
      },
      // 账单配置对话框
      billConfigOpen: false,
      configForm: {},
      configRules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        generateType: [
          { required: true, message: "请选择生成类型", trigger: "change" }
        ],
        intervalDays: [
          { required: true, message: "请输入间隔天数", trigger: "blur" }
        ]
      },
      // 审核对话框
      auditOpen: false,
      auditLoading: false,
      auditForm: {
        paymentId: null,
        receiptNumber: '',
        ownerInfo: '',
        dueAmount: 0,
        actualAmount: 0,
        auditStatus: 1,
        auditComment: ''
      },
      auditRules: {
        actualAmount: [
          { required: true, message: "请输入实收金额", trigger: "blur" },
          { type: 'number', min: 0, message: "实收金额不能小于0", trigger: "blur" }
        ],
        auditStatus: [
          { required: true, message: "请选择审核结果", trigger: "change" }
        ],
        auditComment: [
          { required: false, message: "请输入审核意见", trigger: "blur" },
        ]
      },
      // 汇总相关
      showDetailSummary: false,
      // 右侧明细弹窗
      detailDrawerVisible: false,
      selectedRecord: null,
      // 费用明细相关
      paymentDetailsList: [],
      paymentDetailsLoading: false
    }
  },
  computed: {
    // 计算应收金额汇总
    totalDueAmount() {
      return this.paymentRecordList.reduce((total, record) => {
        const dueAmount = parseFloat(record.dueAmount || record.paymentAmount || 0)
        return total + dueAmount
      }, 0)
    },
    // 计算实收金额汇总
    totalActualAmount() {
      return this.paymentRecordList.reduce((total, record) => {
        const actualAmount = parseFloat(record.paymentAmount || 0)
        return total + actualAmount
      }, 0)
    },
    // 计算差额
    totalDifference() {
      return this.totalDueAmount - this.totalActualAmount
    },
    // 差额样式类
    differenceClass() {
      if (this.totalDifference > 0) {
        return 'difference-positive' // 应收大于实收，显示红色
      } else if (this.totalDifference < 0) {
        return 'difference-negative' // 实收大于应收，显示蓝色
      } else {
        return 'difference-zero' // 相等，显示绿色
      }
    },
    // 差额图标样式类
    differenceIconClass() {
      if (this.totalDifference > 0) {
        return 'difference-icon-positive'
      } else if (this.totalDifference < 0) {
        return 'difference-icon-negative'
      } else {
        return 'difference-icon-zero'
      }
    },
    // 审核状态统计
    auditStatusStats() {
      const stats = { pending: 0, approved: 0, rejected: 0 }
      this.paymentRecordList.forEach(record => {
        if (record.auditStatus === 0) stats.pending++
        else if (record.auditStatus === 1) stats.approved++
        else if (record.auditStatus === 2) stats.rejected++
      })
      return stats
    },
    // 支付方式统计
    paymentMethodStats() {
      const stats = {}
      this.paymentRecordList.forEach(record => {
        const method = record.paymentMethod || '未知'
        stats[method] = (stats[method] || 0) + 1
      })
      return stats
    },
    // 平均应收金额
    avgDueAmount() {
      return this.paymentRecordList.length > 0 ? this.totalDueAmount / this.paymentRecordList.length : 0
    },
    // 平均实收金额
    avgActualAmount() {
      return this.paymentRecordList.length > 0 ? this.totalActualAmount / this.paymentRecordList.length : 0
    },
    // 是否全选车位
    isAllParkingSelected() {
      return this.ownerParkingList.length > 0 &&
             this.form.selectedParkingSpaces.length === this.ownerParkingList.length
    },
    // 预交费用是否全选车位
    isAllAdvanceParkingSelected() {
      return this.ownerParkingList.length > 0 &&
             this.form.advanceSelectedParkingSpaces.length === this.ownerParkingList.length
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询收费记录列表 */
    getList() {
      this.loading = true
      listPaymentRecord(this.queryParams).then(response => {
        this.paymentRecordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },


    /** 小区变化处理 */
    handleCommunityChange(communityId, communityData) {
      this.queryParams.ownerId = null
      this.getList()
    },
    /** 表单小区变化处理 */
    handleFormCommunityChange(communityId, communityData) {
      this.form.ownerId = null
      this.form.billId = null
      this.unpaidBills = []
      this.selectedBill = null
    },

    /** 业主变化处理 */
    handleOwnerChange(ownerId, ownerData) {
      this.form.billId = null
      this.selectedBill = null
      if (this.form.communityId && this.form.ownerId) {
        this.getUnpaidBills()
        // 获取该业主的停车位信息（无论是否有账单）
        this.getOwnerParkingList(this.form.ownerId)
      }
    },
    /** 获取未支付账单 */
    getUnpaidBills() {
      getUnpaidBillsForPayment(this.form.communityId, this.form.ownerId).then(response => {
        this.unpaidBills = response.data
      })
    },
    /** 账单选择处理 */
    handleBillSelection(selection) {
      if (selection.length > 0) {
        this.selectedBill = selection[0]
        this.form.billId = this.selectedBill.bill_id

        // 获取该业主的停车位信息
        this.getOwnerParkingList(this.selectedBill.owner_id)
      } else {
        this.selectedBill = null
        this.form.billId = null
        this.ownerParkingList = []
      }
      this.form.paymentType = null
      this.form.calculatedAmount = 0
    },
    /** 处理表格当前行变化 */
    handleCurrentBillChange(currentRow) {
      if (currentRow) {
        // 清除之前的选择
        this.$refs.billTable.clearSelection()
        // 选择当前行
        this.$refs.billTable.toggleRowSelection(currentRow, true)
      }
    },
    /** 获取业主停车位列表 */
    getOwnerParkingList(ownerId) {
      // 这里需要调用停车位API，暂时使用模拟数据
      // 实际应该调用: listParkingInfo({ ownerId: ownerId, status: 1 })
      import('@/api/system/parkingInfo').then(module => {
        module.listParkingInfo({ ownerId: ownerId, status: 1 }).then(response => {
          this.ownerParkingList = response.rows || []
        }).catch(() => {
          this.ownerParkingList = []
        })
      }).catch(() => {
        this.ownerParkingList = []
      })
    },
    /** 收费类型变化处理 */
    handlePaymentTypeChange() {
      this.form.feeType = null
      this.form.paymentMonths = 1
      this.form.advanceMonths = 1
      this.form.calculatedAmount = 0

      // 如果没有账单，自动设置为预交费用
      if (!this.selectedBill && this.form.ownerId && this.unpaidBills.length === 0) {
        this.form.paymentType = 'advance'
      }
    },
    /** 计算全额支付金额 */
    calculateTotalUnpaid() {
      if (!this.selectedBill) return 0
      return parseFloat(this.selectedBill.total_amount || 0) - parseFloat(this.selectedBill.paid_amount || 0)
    },

    /** 获取账单的月数（用于计算月费用） */
    getAccountBillMonths() {
      if (!this.selectedBill) return 1

      const startDate = new Date(this.selectedBill.bill_period_start)
      const endDate = new Date(this.selectedBill.bill_period_end)

      // 简单计算月数：年差*12 + 月差
      const yearDiff = endDate.getFullYear() - startDate.getFullYear()
      const monthDiff = endDate.getMonth() - startDate.getMonth()
      const totalMonths = yearDiff * 12 + monthDiff

      return Math.max(totalMonths, 1) // 至少1个月
    },

    /** 计算部分支付金额 */
    calculateMixedAmount() {
      if (!this.form.mixedPaymentMonths || this.form.mixedFeeTypes.length === 0 || !this.form.communityId || !this.form.ownerId) {
        this.form.mixedCalculatedAmount = 0
        this.form.mixedFeeDetails = []
        return
      }

      // 如果选择了停车费但没有选择车位，提示用户
      if (this.form.mixedFeeTypes.includes('2') && (!this.form.selectedParkingSpaces || this.form.selectedParkingSpaces.length === 0)) {
        this.$message.warning('请选择要缴费的车位')
        this.form.mixedCalculatedAmount = 0
        this.form.mixedFeeDetails = []
        return
      }

      // 调用后端API计算部分支付金额
      const params = {
        communityId: this.form.communityId,
        ownerId: this.form.ownerId,
        months: this.form.mixedPaymentMonths,
        feeTypes: this.form.mixedFeeTypes,
        plateNumber: this.form.plateNumber,
        selectedParkingSpaces: this.form.selectedParkingSpaces // 添加选中的车位
      }

      calculateMixedPaymentAmount(params).then(response => {
        const data = response.data
        this.form.mixedCalculatedAmount = data.totalAmount || 0
        this.form.mixedFeeDetails = data.feeDetails || []
      }).catch(error => {
        console.error('计算部分支付金额失败:', error)
        this.form.mixedCalculatedAmount = 0
        this.form.mixedFeeDetails = []
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        communityId: null,
        ownerId: null,
        billId: null,
        paymentType: null,
        feeType: null,
        paymentMonths: 1,
        paymentMethod: null,
        calculatedAmount: 0,
        plateNumber: null,
        paymentPeriodInfo: null,
        remark: null,
        // 部分支付相关字段
        mixedPaymentMonths: 1,
        mixedFeeTypes: [],
        selectedParkingSpaces: [], // 选中的车位列表
        mixedCalculatedAmount: 0,
        mixedFeeDetails: [],
        // 预交费用相关字段
        advanceMonths: 1,
        advanceFeeTypes: [],
        advanceSelectedParkingSpaces: [], // 预交费用选中的车位列表
        advanceCalculatedAmount: 0,
        advanceFeeDetails: []
      }
      this.unpaidBills = []
      this.selectedBill = null
      this.ownerParkingList = []
      this.formOwnerList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "收费操作"
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid && (this.selectedBill || (this.form.ownerId && this.unpaidBills.length === 0))) {
          // 验证收费类型和必填字段
          if (!this.form.paymentType) {
            this.$modal.msgError("请选择收费类型")
            return
          }

          if (!this.form.paymentMethod) {
            this.$modal.msgError("请选择支付方式")
            return
          }

          // 验证部分支付的必填字段
          if (this.form.paymentType === 'partial') {
            if (!this.form.feeType) {
              this.$modal.msgError("请选择费用类型")
              return
            }
            if (!this.form.paymentMonths || this.form.paymentMonths <= 0) {
              this.$modal.msgError("请输入有效的支付月数")
              return
            }
            // 停车费必须选择车位
            if (this.form.feeType === '2' && (!this.form.selectedParkingSpaces || this.form.selectedParkingSpaces.length === 0)) {
              this.$modal.msgError("停车费缴费时请选择车位")
              return
            }
          }

          // 验证预交费用的必填字段
          if (this.form.paymentType === 'advance') {
            if (!this.form.advanceFeeTypes || this.form.advanceFeeTypes.length === 0) {
              this.$modal.msgError("请选择费用类型")
              return
            }
            if (!this.form.advanceMonths || this.form.advanceMonths <= 0) {
              this.$modal.msgError("请输入有效的预交月数")
              return
            }
            // 停车费必须选择车位
            if (this.form.advanceFeeTypes.includes('2') && (!this.form.advanceSelectedParkingSpaces || this.form.advanceSelectedParkingSpaces.length === 0)) {
              this.$modal.msgError("停车费缴费时请选择车位")
              return
            }
            // 检查是否已计算费用明细
            if (!this.form.advanceFeeDetails || this.form.advanceFeeDetails.length === 0) {
              this.$modal.msgError("请先计算预交费用金额")
              return
            }
          }

          // 验证部分支付的必填字段
          if (this.form.paymentType === 'mixed') {
            if (!this.form.mixedFeeTypes || this.form.mixedFeeTypes.length === 0) {
              this.$modal.msgError("请选择要缴费的费用类型")
              return
            }
            if (!this.form.mixedPaymentMonths || this.form.mixedPaymentMonths <= 0) {
              this.$modal.msgError("请输入有效的支付月数")
              return
            }
            if (this.form.mixedCalculatedAmount <= 0) {
              this.$modal.msgError("请先计算应收金额")
              return
            }
            // 停车费必须选择车位
            if (this.form.mixedFeeTypes.includes('2') && (!this.form.selectedParkingSpaces || this.form.selectedParkingSpaces.length === 0)) {
              this.$modal.msgError("停车费缴费时请选择车位")
              return
            }
          }

          const paymentData = {
            paymentType: this.form.paymentType,
            paymentMethod: this.form.paymentMethod,
            remark: this.form.remark,
            communityId: this.form.communityId,
            ownerId: this.form.ownerId
          }

          // 如果有账单，添加账单ID
          if (this.selectedBill) {
            paymentData.billId = this.form.billId
          }

          if (this.form.paymentType === 'advance') {
            paymentData.advanceFeeTypes = this.form.advanceFeeTypes
            paymentData.advanceMonths = this.form.advanceMonths
            paymentData.advanceFeeDetails = this.form.advanceFeeDetails
            paymentData.advanceCalculatedAmount = this.form.advanceCalculatedAmount
            if (this.form.advanceFeeTypes.includes('2') && this.form.advanceSelectedParkingSpaces) {
              paymentData.advanceSelectedParkingSpaces = this.form.advanceSelectedParkingSpaces
            }
          } else if (this.form.paymentType === 'mixed') {
            paymentData.mixedPaymentMonths = this.form.mixedPaymentMonths
            paymentData.mixedFeeTypes = this.form.mixedFeeTypes
            paymentData.mixedFeeDetails = this.form.mixedFeeDetails
            paymentData.mixedCalculatedAmount = this.form.mixedCalculatedAmount
            if (this.form.mixedFeeTypes.includes('2') && this.form.selectedParkingSpaces) {
              paymentData.selectedParkingSpaces = this.form.selectedParkingSpaces
            }
          }

          console.log('提交收费数据:', paymentData)

          processPayment(paymentData).then(response => {
            this.$modal.msgSuccess("收费成功")
              this.open = false
              this.getList()
          }).catch(error => {
            console.error('收费失败:', error)
            this.$modal.msgError(error.msg || error.message || "收费失败，请重试")
            })
        } else if (!this.selectedBill && !(this.form.ownerId && this.unpaidBills.length === 0)) {
          this.$modal.msgError("请先选择要收费的账单或选择业主进行预交费")
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除收费记录编号为"' + ids + '"的数据项？').then(function() {
        return delPaymentRecord(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/paymentRecord/export', {
        ...this.queryParams
      }, `paymentRecord_${new Date().getTime()}.xlsx`)
    },
    /** 打印收费单 */
    handlePrint(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      // 获取收费详细信息用于打印（包含明细）
      getPaymentRecordWithDetails(row.id).then(response => {
        this.printPaymentData = response.data
        this.printPaymentList = []
        this.$nextTick(() => {
          this.$refs.printComponent.print()
        })
      }).catch(error => {
        console.error('获取收费详情失败:', error)
        this.$modal.msgError("获取收费详情失败")
      })
    },

    /** 配置小区变化处理 */
    handleConfigCommunityChange() {
      if (this.configForm.communityId) {
        // 查询该小区的现有配置
        getBillConfigByCommunityId(this.configForm.communityId).then(response => {
          if (response.data) {
            this.configForm = { ...response.data }
          }
        })
      }
    },
    /** 提交账单配置 */
    submitBillConfig() {
      this.$refs["configForm"].validate(valid => {
        if (valid) {
          if (this.configForm.id) {
            // 更新配置
            updateBillConfig(this.configForm).then(response => {
              this.$modal.msgSuccess("配置更新成功")
              this.billConfigOpen = false
            })
          } else {
            // 新增配置
            addBillConfig(this.configForm).then(response => {
              this.$modal.msgSuccess("配置保存成功")
              this.billConfigOpen = false
            })
          }
        }
      })
    },
    /** 处理审核 */
    handleAudit(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      this.resetAuditForm()
      this.auditForm.paymentId = row.id
      this.auditForm.receiptNumber = row.receiptNumber
      this.auditForm.ownerInfo = `${row.buildingNumber}-${row.houseNumber} ${row.ownerName}`
      this.auditForm.dueAmount = row.dueAmount || row.paymentAmount
      this.auditForm.actualAmount = row.paymentAmount
      this.auditOpen = true
    },
    /** 重置审核表单 */
    resetAuditForm() {
      this.auditForm = {
        paymentId: null,
        receiptNumber: '',
        ownerInfo: '',
        dueAmount: 0,
        actualAmount: 0,
        auditStatus: 1,
        auditComment: ''
      }
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          this.auditLoading = true
          const auditData = {
            paymentId: this.auditForm.paymentId,
            auditStatus: this.auditForm.auditStatus,
            auditComment: this.auditForm.auditComment
          }

          // 如果实收金额与原金额不同，需要更新实收金额
          if (this.auditForm.actualAmount !== this.auditForm.dueAmount) {
            auditData.actualAmount = this.auditForm.actualAmount
          }

          auditPaymentRecord(auditData).then(() => {
            if (this.auditForm.auditStatus === 1) {
              this.$modal.msgSuccess("审核通过成功！系统已自动更新相关费用的到期时间。")
            } else {
              this.$modal.msgSuccess("审核完成！")
            }
            this.auditOpen = false
            this.getList()
          }).catch(error => {
            console.error('审核失败:', error)
            this.$modal.msgError("审核失败：" + (error.msg || error.message || "未知错误"))
          }).finally(() => {
            this.auditLoading = false
          })
        }
      })
    },
    /** 格式化货币显示 */
    formatCurrency(amount) {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return '¥0.00'
      }
      return '¥' + parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    /** 切换详细汇总显示 */
    toggleSummaryDetail() {
      this.showDetailSummary = !this.showDetailSummary
    },
    /** 处理车位选择变化 */
    handleParkingSelectionChange(selectedSpaces) {
      // 重新计算混合支付金额
      if (this.form.mixedFeeTypes.includes('2')) {
        this.calculateMixedAmount()
      }
    },
    /** 全选/取消全选车位 */
    selectAllParkingSpaces() {
      if (this.isAllParkingSelected) {
        this.form.selectedParkingSpaces = []
      } else {
        this.form.selectedParkingSpaces = this.ownerParkingList.map(parking => parking.plateNumber)
      }
      this.handleParkingSelectionChange(this.form.selectedParkingSpaces)
    },
    /** 计算选中车位的费用 */
    calculateSelectedParkingFee() {
      let totalFee = 0
      this.form.selectedParkingSpaces.forEach(plateNumber => {
        const parking = this.ownerParkingList.find(p => p.plateNumber === plateNumber)
        if (parking) {
          totalFee += parseFloat(parking.monthlyFee || 0)
        }
      })
      return totalFee.toFixed(2)
    },
    /** 处理预交费用车位选择变化 */
    handleAdvanceParkingSelectionChange(selectedSpaces) {
      // 重新计算预交费用金额
      if (this.form.advanceFeeTypes.includes('2')) {
        this.calculateAdvanceAmount()
      }
    },
    /** 全选/取消全选预交费用车位 */
    selectAllAdvanceParkingSpaces() {
      if (this.isAllAdvanceParkingSelected) {
        this.form.advanceSelectedParkingSpaces = []
      } else {
        this.form.advanceSelectedParkingSpaces = this.ownerParkingList.map(parking => parking.plateNumber)
      }
      this.handleAdvanceParkingSelectionChange(this.form.advanceSelectedParkingSpaces)
    },
    /** 计算预交费用选中车位的费用 */
    calculateAdvanceSelectedParkingFee() {
      let totalFee = 0
      this.form.advanceSelectedParkingSpaces.forEach(plateNumber => {
        const parking = this.ownerParkingList.find(p => p.plateNumber === plateNumber)
        if (parking) {
          totalFee += parseFloat(parking.monthlyFee || 0)
        }
      })
      return totalFee.toFixed(2)
    },
    /** 计算预交费用金额 */
    calculateAdvanceAmount() {
      if (!this.form.advanceMonths || this.form.advanceFeeTypes.length === 0 || !this.form.communityId || !this.form.ownerId) {
        this.form.advanceCalculatedAmount = 0
        this.form.advanceFeeDetails = []
        return
      }

      // 如果选择了停车费但没有选择车位，提示用户
      if (this.form.advanceFeeTypes.includes('2') && (!this.form.advanceSelectedParkingSpaces || this.form.advanceSelectedParkingSpaces.length === 0)) {
        this.$message.warning('请选择要缴费的车位')
        this.form.advanceCalculatedAmount = 0
        this.form.advanceFeeDetails = []
        return
      }

      // 调用后端API计算预交费用金额
      const params = {
        communityId: this.form.communityId,
        ownerId: this.form.ownerId,
        months: this.form.advanceMonths,
        feeTypes: this.form.advanceFeeTypes,
        plateNumber: this.form.plateNumber,
        selectedParkingSpaces: this.form.advanceSelectedParkingSpaces // 添加选中的车位
      }

      calculateMixedPaymentAmount(params).then(response => {
        const data = response.data
        this.form.advanceCalculatedAmount = data.totalAmount || 0
        this.form.advanceFeeDetails = data.feeDetails || []
      }).catch(error => {
        console.error('计算预交费用金额失败:', error)
        this.form.advanceCalculatedAmount = 0
        this.form.advanceFeeDetails = []
      })
    },
    /** 处理行点击 */
    handleRowClick(row) {
      this.selectedRecord = row
      this.detailDrawerVisible = true
      // 自动加载费用明细
      this.loadPaymentDetails(row.id)
    },
    /** 关闭明细弹窗 */
    handleDetailDrawerClose() {
      this.detailDrawerVisible = false
      this.selectedRecord = null
      // 清空费用明细
      this.paymentDetailsList = []
      this.paymentDetailsLoading = false
    },
    /** 加载费用明细 */
    loadPaymentDetails(paymentId) {
      if (!paymentId) {
        console.warn('paymentId 为空，无法加载费用明细')
        return
      }

      this.paymentDetailsLoading = true
      this.paymentDetailsList = []

      console.log('开始加载费用明细，paymentId:', paymentId)

      getPaymentDetails(paymentId).then(response => {
        console.log('费用明细响应数据:', response)

        // 根据后端返回的数据结构处理
        if (response && response.data) {
          this.paymentDetailsList = Array.isArray(response.data) ? response.data : []
        } else if (Array.isArray(response)) {
          this.paymentDetailsList = response
        } else {
          this.paymentDetailsList = []
        }

        console.log('处理后的费用明细列表:', this.paymentDetailsList)
      }).catch(error => {
        console.error('获取费用明细失败:', error)
        this.$modal.msgError('获取费用明细失败: ' + (error.message || '未知错误'))
        this.paymentDetailsList = []
      }).finally(() => {
        this.paymentDetailsLoading = false
      })
    },
    /** 从明细弹窗审核 */
    handleAuditFromDetail() {
      if (this.selectedRecord) {
        this.handleAudit(this.selectedRecord)
      }
    },
    /** 从明细弹窗打印 */
    handlePrintFromDetail() {
      if (this.selectedRecord) {
        this.handlePrint(this.selectedRecord)
      }
    },

    /** 获取费用类型标签类型 */
    getFeeTypeTagType(feeType) {
      const typeMap = {
        1: 'primary',   // 物业费
        2: 'success',   // 停车费
        3: 'warning',   // 卫生费
        4: 'info'       // 电梯费
      }
      return typeMap[feeType] || ''
    },
    /** 计算费用明细总金额 */
    getTotalAmount() {
      return this.paymentDetailsList.reduce((total, item) => {
        return total + parseFloat(item.payment_amount || 0)
      }, 0)
    }
  }
}
</script>

<style scoped>

/* 应收金额显示样式 */
.el-form-item__content span {
  font-weight: bold;
}

/* 按钮区域样式 */
.dialog-footer {
  text-align: right;
  padding-top: 10px;
}

/* 费用明细样式 */
.fee-details-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
}

.fee-details-container .el-tag {
  margin: 2px;
  font-size: 11px;
}

/* 汇总区域样式 */
.summary-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.summary-header i {
  margin-right: 8px;
  color: #409EFF;
}

.summary-row {
  margin: 0;
}

.summary-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-radius: 6px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background: #f0f2f5;
  color: #606266;
}

.summary-icon.due-icon {
  background: #fdf6ec;
  color: #E6A23C;
}

.summary-icon.actual-icon {
  background: #f0f9ff;
  color: #67C23A;
}

.summary-icon.difference-icon-positive {
  background: #fef0f0;
  color: #F56C6C;
}

.summary-icon.difference-icon-negative {
  background: #f0f9ff;
  color: #409EFF;
}

.summary-icon.difference-icon-zero {
  background: #f0f9ff;
  color: #67C23A;
}

.summary-content {
  flex: 1;
}

.summary-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 400;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.summary-value.due-amount {
  color: #E6A23C;
}

.summary-value.actual-amount {
  color: #67C23A;
}

.summary-value.difference-positive {
  color: #F56C6C;
}

.summary-value.difference-negative {
  color: #409EFF;
}

.summary-value.difference-zero {
  color: #67C23A;
}

/* 详细汇总样式 */
.detail-summary {
  margin-top: 15px;
  padding-top: 15px;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.audit-stats .el-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.payment-method-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.method-stat {
  background: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.avg-amount {
  display: flex;
  gap: 15px;
}

.avg-amount span {
  background: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-container {
    padding: 10px;
  }

  .summary-item {
    flex-direction: column;
    text-align: center;
    padding: 5px 0;
  }

  .summary-label {
    margin-right: 0;
    margin-bottom: 4px;
    font-size: 12px;
  }

  .summary-value {
    font-size: 14px;
  }
}

/* 右侧明细弹窗样式 */
.detail-drawer-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f2f5;
}

.section-title i {
  margin-right: 8px;
  color: #409EFF;
  font-size: 18px;
}

.detail-row {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  margin-right: 10px;
}

.detail-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
  word-break: break-all;
}

.detail-value.amount {
  font-weight: bold;
  font-size: 16px;
}

.detail-value.due-amount {
  color: #E6A23C;
}

.detail-value.actual-amount {
  color: #67C23A;
}

.audit-comment {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  margin-top: 5px;
  line-height: 1.6;
}

.detail-remark {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #909399;
  line-height: 1.6;
  color: #606266;
}

.detail-actions {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-drawer-content {
    padding: 15px;
  }

  .detail-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .detail-item {
    flex-direction: column;
  }

  .detail-item label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .detail-actions {
    flex-direction: column;
  }

  .detail-actions .el-button {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* 费用明细计算规则样式 */
.calculation-rule {
  font-size: 12px;
  color: #606266;
}

.rule-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rule-text i {
  color: #409EFF;
}

/* 多车位选择样式 */
.parking-selection {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background: #fafafa;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.selection-title {
  font-weight: 500;
  color: #303133;
}

.parking-list {
  max-height: 300px;
  overflow-y: auto;
}

.parking-item {
  margin-bottom: 10px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.parking-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.parking-checkbox {
  width: 100%;
}

.parking-info {
  margin-left: 20px;
  flex: 1;
}

.parking-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.plate-number {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.space-number {
  color: #909399;
  font-size: 12px;
}

.parking-details {
  display: flex;
  align-items: center;
  gap: 10px;
}

.monthly-fee {
  color: #E6A23C;
  font-weight: 500;
  font-size: 12px;
}

.selection-summary {
  margin-top: 15px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  color: #409EFF;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parking-item {
    padding: 8px;
  }

  .parking-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .parking-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* 费用明细样式 */
.detail-summary {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.summary-label {
  color: #606266;
  font-size: 14px;
}

.summary-value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

.total-amount {
  color: #E6A23C;
  font-weight: bold;
  font-size: 16px;
}

.no-details {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  font-size: 14px;
}

.no-details i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

/* 费用明细表格样式 */
.detail-section .el-table {
  margin-bottom: 0;
}

.detail-section .el-table th {
  background: #f5f7fa !important;
}

.detail-section .el-table td {
  padding: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-summary {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .summary-item {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
