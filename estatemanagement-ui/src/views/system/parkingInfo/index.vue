<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <CommunitySelect
          v-model="queryParams.communityId"
          @change="handleCommunityChange"
          @clear="handleCommunityClear"
        />
      </el-form-item>
      <el-form-item label="业主" prop="ownerId">
        <OwnerSelect
          v-model="queryParams.ownerId"
          :community-id="queryParams.communityId"
          placeholder="请选择业主"
        />
      </el-form-item>
      <el-form-item label="名字" prop="ownerName">
        <el-input
          v-model="queryParams.ownerName"
          placeholder="请输入业主或租客名字"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('ownerName')"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('plateNumber')"
        />
      </el-form-item>
      <el-form-item label="三证合一" prop="isThreeCertificates">
        <el-select v-model="queryParams.isThreeCertificates" placeholder="请选择三证合一状态" clearable>
          <el-option label="是" :value="1"></el-option>
          <el-option label="否" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('remark')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:parkingInfo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:parkingInfo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:parkingInfo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:parkingInfo:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-printer"
          size="mini"
          :disabled="multiple"
          @click="handleBatchPrint"
          v-hasPermi="['system:parkingInfo:print']"
        >批量打印催缴单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-document-add"
          size="mini"
          @click="handleCommunityBatchPrint"
          v-hasPermi="['system:parkingInfo:print']"
        >小区批量打印催缴单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parkingInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="isRowSelectable" />
      <el-table-column label="小区名称" align="center" prop="communityName" />
      <el-table-column label="楼号" align="center" prop="buildingNumber" />
      <el-table-column label="门牌号" align="center" prop="houseNumber" />
      <el-table-column label="业主信息" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ getOwnerDisplayText(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center" prop="ownerPhone" />
      <el-table-column label="车牌号" align="center" prop="plateNumber" />
      <el-table-column label="车位号" align="center" prop="spaceNumber">
        <template slot-scope="scope">
          <span>{{ (scope.row.plateNumber && scope.row.spaceNumber) ? scope.row.spaceNumber : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="月租金" align="center" prop="monthlyFee">
        <template slot-scope="scope">
          <span>{{ scope.row.monthlyFee ? scope.row.monthlyFee + '元/月' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="三证合一" align="center" prop="isThreeCertificates">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isThreeCertificates === 1 ? 'success' : 'info'" size="small">
            {{ scope.row.isThreeCertificates === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始日期" align="center" prop="startDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row)">
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" width="150" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:parkingInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:parkingInfo:remove']"
          >删除</el-button>
          <el-button
            v-if="scope.row.plateNumber && scope.row.spaceNumber"
            size="mini"
            type="text"
            icon="el-icon-printer"
            @click="handlePrint(scope.row)"
            v-hasPermi="['system:parkingInfo:print']"
          >打印催缴单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改停车费信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区" prop="communityId">
          <CommunitySelect
            v-model="form.communityId"
            @change="handleFormCommunityChange"
          />
        </el-form-item>
        <el-form-item label="业主" prop="ownerId">
          <OwnerSelect
            v-model="form.ownerId"
            :community-id="form.communityId"
            @change="handleOwnerChange"
          />
        </el-form-item>
        <el-form-item label="车牌号" prop="plateNumber">
          <el-input
            v-model="form.plateNumber"
            placeholder="请输入车牌号"
            @blur="handleFormInputTrim('plateNumber')"
          />
        </el-form-item>
        <el-form-item label="车位号" prop="spaceNumber">
          <el-select
            v-model="form.spaceNumber"
            placeholder="请选择或输入车位号"
            filterable
            allow-create
            default-first-option
            @change="handleSpaceNumberChange"
            @blur="handleSpaceNumberBlur"
            style="width: 100%"
          >
            <el-option
              v-for="space in availableSpaces"
              :key="space.spaceNumber"
              :label="space.spaceNumber"
              :value="space.spaceNumber"
            >
              <span style="float: left">{{ space.spaceNumber }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ space.status === 0 ? '空闲' : '已占用' }}
              </span>
            </el-option>
          </el-select>
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            可选择现有车位或输入新车位号，不存在的车位将自动创建
          </div>
        </el-form-item>
        <el-form-item label="三证合一" prop="isThreeCertificates">
          <el-radio-group v-model="form.isThreeCertificates" @change="handleThreeCertificatesChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            三证合一状态将影响停车费的计算标准
          </div>
        </el-form-item>
<!--        <el-form-item label="月租金" prop="monthlyFee" v-if="form.id != null">-->
<!--          <el-input v-model="form.monthlyFee" placeholder="月租金将根据小区配置自动计算，也可手动修改">-->
<!--            <template slot="append">元/月</template>-->
<!--          </el-input>-->
<!--          <div style="font-size: 12px; color: #999; margin-top: 4px;">-->
<!--            月租金根据小区配置和三证合一状态自动计算，支持手动修改-->
<!--          </div>-->
<!--        </el-form-item>-->

        <!-- 使用日期期间选择器组件 -->
        <DatePeriodSelector
          :start-date.sync="form.startDate"
          :end-date.sync="form.endDate"
          :selected-period.sync="selectedPeriod"
          start-date-prop="startDate"
          end-date-prop="endDate"
          @date-calculated="handleDateCalculated"
        />

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            @blur="handleFormInputTrim('remark')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 小区批量打印催缴单对话框 -->
    <el-dialog title="小区批量打印停车催缴单" :visible.sync="communityPrintOpen" width="500px" append-to-body>
      <el-form ref="communityPrintForm" :model="communityPrintForm" :rules="communityPrintRules" label-width="120px">
        <el-form-item label="选择小区" prop="communityId">
          <CommunitySelect
            v-model="communityPrintForm.communityId"
            placeholder="请选择小区"
            style="width: 100%"
          />
        </el-form-item>
        <el-alert
          title="说明：将打印该小区所有有车牌号和车位号的停车记录催缴单。"
          type="info"
          :closable="false"
          style="margin-top: 15px;">
        </el-alert>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmCommunityPrint" :loading="communityPrintLoading">确 定</el-button>
        <el-button @click="cancelCommunityPrint">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 打印组件 -->
    <PrintComponent ref="printComponent">
      <ParkingNotice
        :parkingData="printParkingData"
        :parkingList="printParkingList"
      />
    </PrintComponent>
  </div>
</template>

<script>
import { listParkingInfo, getParkingInfo, delParkingInfo, addParkingInfo, updateParkingInfo, getUnpaidParkingByCommunity } from "@/api/system/parkingInfo"
import { listParkingManagement, getParkingManagement } from "@/api/system/parkingManagement"
import DatePeriodSelector from "@/components/DatePeriodSelector"
import PrintComponent from "@/components/Print/index.vue"
import ParkingNotice from "@/components/Print/ParkingNotice.vue"
import { OwnerSelect, CommunitySelect } from "@/components"

export default {
  name: "ParkingInfo",
  components: {
    DatePeriodSelector,
    PrintComponent,
    ParkingNotice,
    OwnerSelect,
    CommunitySelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 停车费信息表格数据
      parkingInfoList: [],

      // 可用车位列表
      availableSpaces: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        tenantId: null,
        ownerName: null,
        plateNumber: null,
        isThreeCertificates: null,
        monthlyFee: null,
        startDate: null,
        endDate: null,
        status: null,
        remark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "小区不能为空", trigger: "change" }
        ],
        ownerId: [
          { required: true, message: "业主不能为空", trigger: "change" }
        ],
        plateNumber: [
          { required: true, message: "车牌号不能为空", trigger: "blur" }
        ],
        startDate: [
          { required: true, message: "开始日期不能为空", trigger: "change" }
        ],
        endDate: [
          { required: true, message: "结束日期不能为空", trigger: "change" }
        ],
      },
      // 选择的租期（月数）
      selectedPeriod: 1,
      // 当前选择的业主信息
      currentOwner: null,
      // 当前选择的小区信息
      currentCommunity: null,
      // 打印相关数据
      printParkingData: null,
      printParkingList: [],
      // 小区批量打印相关
      communityPrintOpen: false,
      communityPrintLoading: false,
      communityPrintForm: {
        communityId: null
      },
      communityPrintRules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 获取业主显示名称 */
    getOwnerDisplayName(owner) {
      if (!owner) return ''
      let displayName = owner.ownerName
      if ((owner.rentalStatus == 1 || owner.rentalStatus === '1') && owner.tenantName) {
        displayName += `（${owner.tenantName}）`
      }
      displayName += ` ${owner.buildingNumber}${owner.houseNumber}`
      return displayName
    },
    /** 获取业主显示文本（表格用） */
    getOwnerDisplayText(row) {
      if (!row.ownerName) return ''
      let displayText = row.ownerName
      if ((row.rentalStatus == 1 || row.rentalStatus === '1') && row.tenantName) {
        displayText += `（${row.tenantName}）`
      }
      return displayText
    },
    /** 查询停车费信息列表 */
    getList() {
      this.loading = true
      listParkingInfo(this.queryParams).then(response => {
        this.parkingInfoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },


    /** 清空小区选择 */
    handleCommunityClear() {
      this.queryParams.ownerId = null
    },


    /** 查询可用车位列表 */
    getAvailableSpaces(communityId) {
      if (communityId) {
        listParkingManagement({ communityId: communityId, pageNum: 1, pageSize: 1000 }).then(response => {
          this.availableSpaces = response.rows || []
        })
      } else {
        this.availableSpaces = []
      }
    },
    /** 小区选择变化处理（搜索） */
    handleCommunityChange(communityId, communityData) {
      this.queryParams.ownerId = null
    },
    /** 小区选择变化处理（表单） */
    handleFormCommunityChange(communityId, communityData) {
      this.form.ownerId = null
      this.form.monthlyFee = null
      this.form.spaceNumber = null
      this.currentOwner = null
      this.currentCommunity = communityData

      this.getAvailableSpaces(communityId)
    },
    /** 业主选择变化处理（表单） */
    handleOwnerChange(ownerId, ownerData) {
      this.form.monthlyFee = null
      this.currentOwner = ownerData

      if (ownerId && ownerData) {
        // 重新计算月租金（以防小区配置有变化）
        this.calculateMonthlyFee()
      }
    },
    /** 三证合一状态变化处理 */
    handleThreeCertificatesChange() {
      // 当三证合一状态改变时，重新计算月租金
      this.calculateMonthlyFee()
    },
    /** 处理日期计算 */
    handleDateCalculated(dateInfo) {
      // 组件已经通过sync自动更新了form的日期字段
      // 这里可以添加额外的处理逻辑
    },
    /** 车位号选择变化处理 */
    handleSpaceNumberChange(spaceNumber) {
      if (spaceNumber) {
        // 检查是否为现有车位
        const existingSpace = this.availableSpaces.find(space => space.spaceNumber === spaceNumber)
        if (existingSpace) {
          // 如果是现有车位且已被占用，给出提示
          if (existingSpace.status === 1 && existingSpace.plateNumber) {
            this.$message.warning(`车位 ${spaceNumber} 已被车牌 ${existingSpace.plateNumber} 占用`)
          }
        }
      }
    },
    /** 车位号输入失焦处理 */
    handleSpaceNumberBlur() {
      // 可以在这里添加额外的验证逻辑
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        tenantId: null,
        plateNumber: null,
        spaceNumber: null,
        isThreeCertificates: 1, // 默认为三证合一
        monthlyFee: null,
        startDate: null,
        endDate: null,
        status: 1,
        createTime: null,
        remark: null
      }

      this.availableSpaces = []
      this.selectedPeriod = 1
      this.currentOwner = null
      this.currentCommunity = null
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['ownerName', 'plateNumber', 'remark']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 处理表单输入框失焦时的trim操作 */
    handleFormInputTrim(fieldName) {
      if (this.form[fieldName] && typeof this.form[fieldName] === 'string') {
        this.form[fieldName] = this.form[fieldName].trim()
        // 如果trim后为空字符串，设置为空字符串（表单字段通常不设置为null）
        if (this.form[fieldName] === '') {
          this.form[fieldName] = ''
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.ownerOptions = []
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 设置开始时间为当前时间
      const today = new Date()
      this.form.startDate = today.toISOString().split('T')[0] // 格式化为 YYYY-MM-DD
      this.form.endDate = this.form.startDate // 结束时间默认与开始时间相同
      this.open = true
      this.title = "添加停车费信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getParkingInfo(id).then(response => {
        this.form = response.data

        // 设置小区信息 - 小区信息现在由组件管理，这里只需要加载相关数据
        if (this.form.communityId) {
          this.getAvailableSpaces(this.form.communityId)
        }

        // 业主信息现在由组件管理，这里只需要重新计算月租金
        if (this.form.ownerId) {
          this.$nextTick(() => {
            this.calculateMonthlyFee()
          })
        }

        this.open = true
        this.title = "修改停车费信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 修改时才验证月租金
          // if (this.form.id != null && (!this.form.monthlyFee || this.form.monthlyFee <= 0)) {
          //   this.$message.error('请输入有效的月租金')
          //   return
          // }

          if (this.form.id != null) {
            updateParkingInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addParkingInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除停车费信息编号为"' + ids + '"的数据项？').then(function() {
        return delParkingInfo(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/parkingInfo/export', {
        ...this.queryParams
      }, `parkingInfo_${new Date().getTime()}.xlsx`)
    },
    /** 计算月租金 */
    calculateMonthlyFee() {
      if (!this.currentOwner || !this.currentCommunity) {
        return
      }

      let parkingFee = null

      // 根据停车位的三证合一状态选择对应的停车费
      if (this.form.isThreeCertificates == 1 || this.form.isThreeCertificates === '1') {
        // 三证合一停车费
        parkingFee = this.currentCommunity.ownerParkingFee
      } else {
        // 非三证合一停车费
        parkingFee = this.currentCommunity.tenantParkingFee
      }

      if (parkingFee && parkingFee > 0) {
        this.form.monthlyFee = parkingFee
      } else {
        this.$message.warning('该小区尚未配置停车费，请手动输入月租金或联系管理员设置')
      }
    },
    /** 获取状态标签类型 */
    getStatusTagType(row) {
      const now = new Date()
      const endDate = new Date(row.endDate)

      if (row.status === 0) {
        return 'info' // 无效状态
      } else if (endDate < now) {
        return 'danger' // 已过期
      } else {
        return 'success' // 有效
      }
    },
    /** 获取状态文本 */
    getStatusText(row) {
      const now = new Date()
      const endDate = new Date(row.endDate)

      if (row.status === 0) {
        return '无效'
      } else if (endDate < now) {
        return '已过期'
      } else {
        return '有效'
      }
    },
    /** 判断行是否可选择 */
    isRowSelectable(row, index) {
      // 只有有车牌号和车位号的记录才可以选择进行打印
      return row.plateNumber && row.spaceNumber
    },
    /** 打印停车催缴单 */
    handlePrint(row) {
      // 获取停车详细信息用于打印
      getParkingInfo(row.id).then(response => {
        this.printParkingData = response.data
        this.printParkingList = []
        this.$nextTick(() => {
          this.$refs.printComponent.print()
        })
      }).catch(error => {
        console.error('获取停车详情失败:', error)
        this.$modal.msgError("获取停车详情失败")
      })
    },
    /** 批量打印停车催缴单 */
    handleBatchPrint() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要打印的停车记录")
        return
      }
      // 过滤出有车牌号和车位号的记录
      const validIds = this.ids.filter(id => {
        const parking = this.parkingInfoList.find(p => p.id === id)
        return parking && parking.plateNumber && parking.spaceNumber
      })

      if (validIds.length === 0) {
        this.$modal.msgWarning("请选择有车牌号和车位号的停车记录")
        return
      }

      // 获取选中停车记录的详细信息
      const promises = validIds.map(id => getParkingInfo(id))

      Promise.all(promises).then(responses => {
        const parkingRecords = responses.map(response => response.data)

        // 按业主分组停车记录
        const groupedByOwner = this.groupParkingRecordsByOwner(parkingRecords)

        this.printParkingData = null
        this.printParkingList = groupedByOwner
        this.$nextTick(() => {
          this.$refs.printComponent.print()
        })
      }).catch(error => {
        console.error('获取停车详情失败:', error)
        this.$modal.msgError("获取停车详情失败")
      })
    },


    /** 判断是否到期 */
    isExpired(row) {
      if (!row.endDate) {
        return false
      }
      const now = new Date()
      const endDate = new Date(row.endDate)
      return now > endDate
    },
    /** 小区批量打印停车催缴单 */
    handleCommunityBatchPrint() {
      this.communityPrintForm = {
        communityId: null
      }
      this.communityPrintOpen = true
    },
    /** 确认小区批量打印 */
    confirmCommunityPrint() {
      this.$refs["communityPrintForm"].validate(valid => {
        if (valid) {
          this.communityPrintLoading = true

          // 获取该小区的有效停车记录
          getUnpaidParkingByCommunity(this.communityPrintForm.communityId).then(response => {
            const parkingRecords = response.data || []

            if (parkingRecords.length === 0) {
              this.$modal.msgInfo("该小区没有有效的停车记录需要打印")
              this.communityPrintLoading = false
              return
            }

            // 按业主分组停车记录
            const groupedByOwner = this.groupParkingRecordsByOwner(parkingRecords)

            // 设置打印数据
            this.printParkingData = null
            this.printParkingList = groupedByOwner

            // 关闭对话框
            this.communityPrintOpen = false

            // 执行打印
            this.$nextTick(() => {
              this.$refs.printComponent.print()
            })

            this.$modal.msgSuccess(`成功获取 ${parkingRecords.length} 条停车记录，按业主分组为 ${groupedByOwner.length} 张催缴单，正在打印...`)
          }).catch(error => {
            console.error('获取停车记录失败:', error)
            this.$modal.msgError("获取停车记录失败")
          }).finally(() => {
            this.communityPrintLoading = false
          })
        }
      })
    },
    /** 取消小区批量打印 */
    cancelCommunityPrint() {
      this.communityPrintOpen = false
    },
    /** 按业主分组停车记录 */
    groupParkingRecordsByOwner(parkingRecords) {
      // 按业主ID分组
      const groupedMap = new Map()

      parkingRecords.forEach(record => {
        const ownerId = record.ownerId
        if (!groupedMap.has(ownerId)) {
          groupedMap.set(ownerId, {
            // 使用第一条记录的业主信息作为基础信息
            communityId: record.communityId,
            communityName: record.communityName,
            ownerId: record.ownerId,
            ownerName: record.ownerName,
            buildingNumber: record.buildingNumber,
            houseNumber: record.houseNumber,
            // 停车记录列表
            parkingRecords: []
          })
        }

        // 将停车记录添加到对应业主的列表中
        groupedMap.get(ownerId).parkingRecords.push({
          id: record.id,
          plateNumber: record.plateNumber,
          spaceNumber: record.spaceNumber,
          monthlyFee: record.monthlyFee,
          startDate: record.startDate,
          endDate: record.endDate,
          isThreeCertificates: record.isThreeCertificates
        })
      })

      // 转换为数组并排序
      return Array.from(groupedMap.values()).sort((a, b) => {
        // 按楼号-门牌号排序
        const aHouse = `${a.buildingNumber}-${a.houseNumber}`
        const bHouse = `${b.buildingNumber}-${b.houseNumber}`
        return aHouse.localeCompare(bHouse)
      })
    }
  }
}
</script>
