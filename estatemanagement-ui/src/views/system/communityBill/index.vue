<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <CommunitySelect
          v-model="queryParams.communityId"
          @change="handleQueryCommunityChange"
          @clear="handleCommunityClear"
        />
      </el-form-item>
      <el-form-item label="业主" prop="ownerId">
        <OwnerSelect
          v-model="queryParams.ownerId"
          :community-id="queryParams.communityId"
          placeholder="请选择业主"
          :disabled="!queryParams.communityId"
          @clear="handleOwnerClear"
        />
      </el-form-item>
      <el-form-item label="账单编号" prop="billNumber">
        <el-input
          v-model="queryParams.billNumber"
          placeholder="请输入账单编号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('billNumber')"
        />
      </el-form-item>
      <el-form-item label="支付状态" prop="paymentStatus">
        <el-select v-model="queryParams.paymentStatus" placeholder="请选择支付状态" clearable>
          <el-option label="未支付" :value="0"></el-option>
          <el-option label="部分支付" :value="1"></el-option>
          <el-option label="已支付" :value="2"></el-option>
          <el-option label="合并到新账单" :value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:communityBill:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:communityBill:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-magic-stick"
          size="mini"
          @click="handleGenerateBill"
          v-hasPermi="['system:communityBill:add']"
        >自动生成账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-printer"
          size="mini"
          :disabled="multiple"
          @click="handleBatchPrint"
          v-hasPermi="['system:communityBill:print']"
        >批量打印催缴单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-printer"
          size="mini"
          @click="handleCommunityBatchPrint"
          v-hasPermi="['system:communityBill:print']"
        >小区批量打印催缴单</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="communityBillList" @selection-change="handleSelectionChange" @row-click="handleRowClick" :row-style="{ cursor: 'pointer' }">
      <el-table-column type="selection" width="55" align="center" :selectable="isRowSelectable" />
      <el-table-column label="小区名称" align="center" prop="communityName" min-width="120" />
      <el-table-column label="楼号" align="center" prop="buildingNumber" min-width="80" />
      <el-table-column label="门牌号" align="center" prop="houseNumber" min-width="80" />
      <el-table-column label="业主信息" align="center" min-width="160">
        <template slot-scope="scope">
          <span>{{ getOwnerDisplayText(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账单编号" align="center" prop="billNumber" min-width="180" />
      <el-table-column label="账单周期开始" align="center" prop="billPeriodStart" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.billPeriodStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账单周期结束" align="center" prop="billPeriodEnd" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.billPeriodEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物业费" align="center" prop="propertyFee" min-width="80" />
      <el-table-column label="停车费" align="center" prop="parkingFee" min-width="80" />
      <el-table-column label="卫生费" align="center" prop="sanitationFee" min-width="80" />
      <el-table-column label="总金额" align="center" prop="totalAmount" min-width="90" />
      <el-table-column label="已支付" align="center" prop="paidAmount" min-width="80" />
      <el-table-column label="支付状态" align="center" prop="paymentStatus" min-width="90">
        <template slot-scope="scope">
          <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
            {{ getPaymentStatusText(scope.row.paymentStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付日期" align="center" prop="paymentDate" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="paymentMethod" min-width="90" />
      <el-table-column label="账单类型" align="center" prop="isTenant" min-width="90">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isTenant === 1 ? 'warning' : 'primary'">
            {{ scope.row.isTenant === 1 ? '租客账单' : '业主账单' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="150" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="240">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(scope.row)"
            v-hasPermi="['system:communityBill:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-printer"
            @click.stop="handlePrint(scope.row)"
            v-hasPermi="['system:communityBill:print']"
            :disabled="scope.row.paymentStatus === 3"
          >打印催缴单</el-button>
          <span v-if="scope.row.paymentStatus === 3" style="color: #909399; font-size: 12px;">
            已合并，不支持操作
          </span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小区账单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区ID" prop="communityId">
          <el-input v-model="form.communityId" placeholder="请输入小区ID" />
        </el-form-item>
        <el-form-item label="业主ID" prop="ownerId">
          <el-input v-model="form.ownerId" placeholder="请输入业主ID" />
        </el-form-item>
        <el-form-item label="账单编号" prop="billNumber">
          <el-input v-model="form.billNumber" placeholder="请输入账单编号" />
        </el-form-item>
        <el-form-item label="账单周期开始日期" prop="billPeriodStart">
          <el-date-picker clearable
            v-model="form.billPeriodStart"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择账单周期开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="账单周期结束日期" prop="billPeriodEnd">
          <el-date-picker clearable
            v-model="form.billPeriodEnd"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择账单周期结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="物业费" prop="propertyFee">
          <el-input v-model="form.propertyFee" placeholder="请输入物业费" />
        </el-form-item>
        <el-form-item label="停车费" prop="parkingFee">
          <el-input v-model="form.parkingFee" placeholder="请输入停车费" />
        </el-form-item>
        <el-form-item label="电梯费" prop="elevatorFee">
          <el-input v-model="form.elevatorFee" placeholder="请输入电梯费" />
        </el-form-item>
        <el-form-item label="卫生费" prop="sanitationFee">
          <el-input v-model="form.sanitationFee" placeholder="请输入卫生费" />
        </el-form-item>
        <el-form-item label="总金额" prop="totalAmount">
          <el-input v-model="form.totalAmount" placeholder="请输入总金额" />
        </el-form-item>
        <el-form-item label="已支付金额" prop="paidAmount">
          <el-input v-model="form.paidAmount" placeholder="请输入已支付金额" />
        </el-form-item>
        <el-form-item label="支付日期" prop="paymentDate">
          <el-date-picker clearable
            v-model="form.paymentDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择支付日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-input v-model="form.paymentMethod" placeholder="请输入支付方式" />
        </el-form-item>
        <el-form-item label="是否为租客账单(0业主,1租客)" prop="isTenant">
          <el-input v-model="form.isTenant" placeholder="请输入是否为租客账单(0业主,1租客)" />
        </el-form-item>
        <el-form-item label="收费经理ID" prop="managerId">
          <el-input v-model="form.managerId" placeholder="请输入收费经理ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 自动生成账单对话框 -->
    <el-dialog title="自动生成账单" :visible.sync="generateBillOpen" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="120px">
        <el-form-item label="选择小区" prop="communityId">
          <CommunitySelect
            v-model="generateForm.communityId"
            placeholder="请选择小区"
            :disabled="isGenerating"
            @change="handleCommunitySelect"
          />
        </el-form-item>

        <el-form-item label="账单日期" prop="billDate">
          <el-date-picker
            v-model="generateForm.billDate"
            type="date"
            value-format="yyyy-MM-dd"
            :disabled="isGenerating"
            placeholder="请选择账单日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="生成类型" prop="generateType">
          <el-radio-group v-model="generateForm.generateType" :disabled="isGenerating">
            <el-radio label="all">全部费用</el-radio>
            <el-radio label="property">仅物业费</el-radio>
            <el-radio label="parking">仅停车费</el-radio>
            <el-radio label="sanitation">仅卫生费</el-radio>
          </el-radio-group>
        </el-form-item>

        <div v-if="generateForm.communityId && !isGenerating" style="margin-top: 20px;">
          <el-alert
            :title="`当前小区：${currentCommunityName}`"
            type="info"
            :closable="false">
          </el-alert>
          <el-alert
            v-if="currentBillDate"
            :title="`当前账单日期：${currentBillDate}`"
            type="warning"
            :closable="false"
            style="margin-top: 10px;">
          </el-alert>
        </div>

        <!-- 进度显示区域 -->
        <div v-if="isGenerating" style="margin-top: 20px;">
          <el-alert
            :title="progressInfo.message || '正在生成账单...'"
            type="info"
            :closable="false">
          </el-alert>

          <div style="margin-top: 15px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
              <span>生成进度</span>
              <span>{{ progressInfo.processedCount || 0 }} / {{ progressInfo.totalCount || 0 }}</span>
            </div>
            <el-progress
              :percentage="progressInfo.progressPercentage || 0"
              :status="progressInfo.status === 'FAILED' ? 'exception' : (progressInfo.status === 'COMPLETED' ? 'success' : null)"
              :stroke-width="20">
            </el-progress>
          </div>

          <div v-if="progressInfo.status === 'COMPLETED'" style="margin-top: 15px;">
            <el-alert
              :title="`生成完成！成功生成 ${progressInfo.successCount} 条账单`"
              type="success"
              :closable="false">
            </el-alert>
          </div>

          <div v-if="progressInfo.status === 'FAILED'" style="margin-top: 15px;">
            <el-alert
              :title="`生成失败：${progressInfo.message}`"
              type="error"
              :closable="false">
            </el-alert>
          </div>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="confirmGenerateBill"
          :loading="generateLoading"
          :disabled="isGenerating && progressInfo.status === 'RUNNING'">
          {{ isGenerating ? (progressInfo.status === 'RUNNING' ? '生成中...' : '确 定') : '确 定' }}
        </el-button>
        <el-button
          @click="cancelGenerateBill"
          :disabled="isGenerating && progressInfo.status === 'RUNNING'">
          {{ isGenerating && progressInfo.status === 'RUNNING' ? '生成中...' : '取 消' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 小区批量打印催缴单对话框 -->
    <el-dialog title="小区批量打印催缴单" :visible.sync="communityPrintOpen" width="500px" append-to-body>
      <el-form ref="communityPrintForm" :model="communityPrintForm" :rules="communityPrintRules" label-width="120px">
        <el-form-item label="选择小区" prop="communityId">
          <CommunitySelect
            v-model="communityPrintForm.communityId"
            placeholder="请选择小区"
            style="width: 100%"
          />
        </el-form-item>
        <el-alert
          title="说明：将打印该小区所有未缴费（未支付和部分支付）的催缴单，已完全缴费的账单不会打印。"
          type="info"
          :closable="false"
          style="margin-top: 15px;">
        </el-alert>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmCommunityPrint" :loading="communityPrintLoading">确 定</el-button>
        <el-button @click="cancelCommunityPrint">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 账单详情侧边栏 -->
    <el-drawer
      title="账单详情"
      :visible.sync="detailDrawerVisible"
      direction="rtl"
      size="50%"
      :before-close="handleDetailClose">
      <div v-if="billDetailData" class="bill-detail-container">
        <!-- 账单基本信息 -->
        <el-card class="detail-card">
          <div slot="header" class="card-header">
            <span class="card-title">账单基本信息</span>
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="账单编号">{{ billDetailData.bill.billNumber }}</el-descriptions-item>
            <el-descriptions-item label="小区名称">{{ billDetailData.bill.communityName }}</el-descriptions-item>
            <el-descriptions-item label="楼号">{{ billDetailData.bill.buildingNumber }}</el-descriptions-item>
            <el-descriptions-item label="门牌号">{{ billDetailData.bill.houseNumber }}</el-descriptions-item>
            <el-descriptions-item label="业主信息">{{ getOwnerDisplayText(billDetailData.bill) }}</el-descriptions-item>
            <el-descriptions-item label="账单类型">{{ billDetailData.bill.isTenant === 1 ? '租客账单' : '业主账单' }}</el-descriptions-item>
            <el-descriptions-item label="账单周期">
              {{ parseTime(billDetailData.bill.billPeriodStart, '{y}-{m}-{d}') }} 至 {{ parseTime(billDetailData.bill.billPeriodEnd, '{y}-{m}-{d}') }}
            </el-descriptions-item>
            <el-descriptions-item label="总金额">{{ billDetailData.bill.totalAmount }}元</el-descriptions-item>
            <el-descriptions-item label="已支付金额">{{ billDetailData.bill.paidAmount }}元</el-descriptions-item>
            <el-descriptions-item label="支付状态">{{ getPaymentStatusText(billDetailData.bill.paymentStatus) }}</el-descriptions-item>
            <el-descriptions-item label="支付日期">{{ parseTime(billDetailData.bill.paymentDate, '{y}-{m}-{d}') }}</el-descriptions-item>
            <el-descriptions-item label="支付方式">{{ billDetailData.bill.paymentMethod || '-' }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">{{ billDetailData.bill.remark || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 费用明细 -->
        <el-card class="detail-card">
          <div slot="header" class="card-header">
            <span class="card-title">费用明细</span>
          </div>
          <el-table :data="billDetailData.details" border>
            <el-table-column label="费用类型" align="center" prop="feeName" />
            <el-table-column label="计费基数" align="center" prop="baseAmount" />
            <el-table-column label="单价" align="center" prop="unitPrice" />
            <el-table-column label="账单周期" align="center" width="200">
              <template slot-scope="scope">
                {{ parseTime(scope.row.billPeriodStart, '{y}-{m}-{d}') }} 至 {{ parseTime(scope.row.billPeriodEnd, '{y}-{m}-{d}') }}
              </template>
            </el-table-column>
            <el-table-column label="金额" align="center" prop="amount" />
            <el-table-column label="已交金额" align="center" prop="paidAmount" width="100">
              <template slot-scope="scope">
                <span style="color: #67C23A; font-weight: bold;">{{ scope.row.paidAmount || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="欠费金额" align="center" prop="owedAmount" width="100">
              <template slot-scope="scope">
                <span :style="{ color: (scope.row.owedAmount > 0 ? '#F56C6C' : '#909399'), fontWeight: 'bold' }">
                  {{ scope.row.owedAmount || 0 }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="车牌号" align="center" prop="plateNumber">
              <template slot-scope="scope">
                {{ scope.row.plateNumber || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="车位号" align="center" prop="spaceNumber">
              <template slot-scope="scope">
                {{ scope.row.spaceNumber || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="计算公式" align="center" prop="calculationFormula" width="300" show-overflow-tooltip />
          </el-table>
        </el-card>

        <!-- 收费记录 -->
        <el-card class="detail-card">
          <div slot="header" class="card-header">
            <span class="card-title">收费记录</span>
          </div>
          <el-table :data="billDetailData.paymentRecords" border>
            <el-table-column label="收据编号" align="center" prop="receipt_number" width="180" />
            <el-table-column label="缴费日期" align="center" prop="payment_date" width="120">
              <template slot-scope="scope">
                {{ parseTime(scope.row.payment_date, '{y}-{m}-{d}') }}
              </template>
            </el-table-column>
            <el-table-column label="缴费金额" align="center" prop="payment_amount" width="100" />
            <el-table-column label="支付方式" align="center" prop="payment_method" width="100" />
            <el-table-column label="费用类型" align="center" prop="fee_type_name" width="100" />
            <el-table-column label="缴费周期" align="center" width="200">
              <template slot-scope="scope">
                <span v-if="scope.row.period_start && scope.row.period_end">
                  {{ parseTime(scope.row.period_start, '{y}-{m}-{d}') }} 至 {{ parseTime(scope.row.period_end, '{y}-{m}-{d}') }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="缴费月数" align="center" prop="payment_months" width="100">
              <template slot-scope="scope">
                {{ scope.row.payment_months || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="是否预交" align="center" prop="is_advance" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.is_advance === 1 ? 'success' : 'info'">
                  {{ scope.row.is_advance === 1 ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="车牌号" align="center" prop="plate_number" width="120">
              <template slot-scope="scope">
                {{ scope.row.plate_number || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="操作人" align="center" prop="operator_name" width="100" />
            <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
          </el-table>
        </el-card>
      </div>
    </el-drawer>

    <!-- 打印组件 -->
    <print-component ref="printComponent">
      <bill-notice
        :billData="printBillData"
        :billList="printBillList">
      </bill-notice>
    </print-component>
  </div>
</template>

<script>
import { listCommunityBill, getCommunityBill, getCommunityBillWithDetails, addCommunityBill, updateCommunityBill, delCommunityBill, generateBills, getGenerationProgress, getUnpaidBillsByCommunity } from "@/api/system/communityBill"
import { getBillPaymentRecords } from "@/api/system/paymentRecord"
import PrintComponent from "@/components/Print/index.vue"
import BillNotice from "@/components/Print/BillNotice.vue"
import { OwnerSelect, CommunitySelect } from "@/components"

export default {
  name: "CommunityBill",
  components: {
    PrintComponent,
    BillNotice,
    OwnerSelect,
    CommunitySelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小区账单表格数据
      communityBillList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        billNumber: null,
        billPeriodStart: null,
        billPeriodEnd: null,
        propertyFee: null,
        parkingFee: null,
        elevatorFee: null,
        sanitationFee: null,
        lateFee: null,
        totalAmount: null,
        paidAmount: null,
        paymentStatus: null,
        paymentDate: null,
        paymentMethod: null,
        isTenant: null,
        managerId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 自动生成账单相关
      generateBillOpen: false,
      generateForm: {
        communityId: null,
        billDate: null,
        generateType: 'all'
      },
      generateRules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        billDate: [
          { required: true, message: "请选择账单日期", trigger: "change" }
        ],
        generateType: [
          { required: true, message: "请选择生成类型", trigger: "change" }
        ]
      },

      currentCommunityName: '',
      currentBillDate: '',
      generateLoading: false,
      isGenerating: false,
      progressInfo: {},
      progressTimer: null,
      currentTaskId: null,
      detailDrawerVisible: false,
      billDetailData: null,
      // 打印相关数据
      printBillData: null,
      printBillList: [],
      // 小区批量打印相关
      communityPrintOpen: false,
      communityPrintForm: {
        communityId: null
      },
      communityPrintRules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ]
      },
      communityPrintLoading: false
    }
  },
  created() {
    this.getList()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },
  methods: {
    /** 查询小区账单列表 */
    getList() {
      this.loading = true
      listCommunityBill(this.queryParams).then(response => {
        this.communityBillList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        billNumber: null,
        billPeriodStart: null,
        billPeriodEnd: null,
        propertyFee: null,
        parkingFee: null,
        elevatorFee: null,
        sanitationFee: null,
        totalAmount: null,
        paidAmount: null,
        paymentStatus: null,
        paymentDate: null,
        paymentMethod: null,
        isTenant: null,
        managerId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['billNumber']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加小区账单"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCommunityBill(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改小区账单"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCommunityBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addCommunityBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除小区账单编号为"' + ids + '"的数据项？').then(function() {
        return delCommunityBill(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/communityBill/export', {
        ...this.queryParams
      }, `communityBill_${new Date().getTime()}.xlsx`)
    },
    /** 自动生成账单按钮操作 */
    handleGenerateBill() {
      this.generateForm = {
        communityId: null,
        billDate: null,
        generateType: 'all'
      }
      this.currentCommunityName = ''
      this.currentBillDate = ''
      this.isGenerating = false
      this.progressInfo = {}
      this.currentTaskId = null
      // 清理之前的定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
      this.generateBillOpen = true
    },
    handleCommunitySelect(communityId, communityData) {
      if (communityData) {
        this.currentCommunityName = communityData.communityName

        // 获取当前账单日期
        let currentBillDate = null
        if (communityData.statementDate) {
          currentBillDate = communityData.statementDate
        } else if (communityData.entryDate) {
          currentBillDate = communityData.entryDate
        }

        if (currentBillDate) {
          // 格式化显示当前账单日期
          this.currentBillDate = this.formatDate(currentBillDate)

          // 计算默认账单日期
          // 如果当前账单日期是2025-06-13，下次应该生成2026-06-13到2026-06-12的账单
          // 所以输入日期应该是2026-06-12（账单结束日期）
          const billDate = new Date(currentBillDate)
          billDate.setFullYear(billDate.getFullYear() + 1) // +1年，得到下年同一天
          billDate.setDate(billDate.getDate() ) // -1天，得到正确的结束日期

          // 设置默认账单日期
          this.generateForm.billDate = this.formatDate(billDate)
        } else {
          this.currentBillDate = '未设置'
          this.generateForm.billDate = null
        }
      }
    },

    // 格式化日期为 YYYY-MM-DD 格式
    formatDate(date) {
      if (!date) return null

      let d = null
      if (typeof date === 'string') {
        // 如果是字符串，直接返回（假设已经是正确格式）
        if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return date
        }
        d = new Date(date)
      } else if (date instanceof Date) {
        d = date
      } else {
        d = new Date(date)
      }

      // 检查日期是否有效
      if (isNaN(d.getTime())) {
        console.warn('Invalid date:', date)
        return null
      }

      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    confirmGenerateBill() {
      // 如果正在生成且已完成，则关闭对话框
      if (this.isGenerating && (this.progressInfo.status === 'COMPLETED' || this.progressInfo.status === 'FAILED')) {
        this.generateBillOpen = false
        if (this.progressInfo.status === 'COMPLETED') {
          this.getList() // 刷新列表
        }
        return
      }

      // 如果正在生成中，不允许重复提交
      if (this.isGenerating && this.progressInfo.status === 'RUNNING') {
        return
      }

      this.$refs["generateForm"].validate(valid => {
        if (valid) {
          this.generateLoading = true
          generateBills(this.generateForm).then(response => {
            console.log('生成账单响应:', response)

            // 处理不同的响应格式
            let taskId = null
            if (response.taskId) {
              taskId = response.taskId
            } else if (response.data && response.data.taskId) {
              taskId = response.data.taskId
            } else if (response.data && typeof response.data === 'string') {
              taskId = response.data
            }

            if (taskId) {
              this.currentTaskId = taskId
              this.isGenerating = true
              this.progressInfo = {
                status: 'RUNNING',
                message: '正在初始化...',
                processedCount: 0,
                totalCount: 0,
                successCount: 0,
                progressPercentage: 0
              }
              // 开始轮询进度
              this.startProgressPolling()
              this.$modal.msgSuccess("账单生成任务已启动")
            } else {
              this.$modal.msgError("无法获取任务ID，请重试")
            }
          }).catch(error => {
            console.error('生成账单失败:', error)
            this.$modal.msgError(error.msg || error.message || "启动生成任务失败")
          }).finally(() => {
            this.generateLoading = false
          })
        }
      })
    },
    cancelGenerateBill() {
      // 如果正在生成中，不允许关闭
      if (this.isGenerating && this.progressInfo.status === 'RUNNING') {
        this.$modal.msgWarning("账单正在生成中，请等待完成")
        return
      }

      this.generateBillOpen = false
      // 清理定时器
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    },
    startProgressPolling() {
      if (!this.currentTaskId) return

      this.progressTimer = setInterval(() => {
        this.checkProgress()
      }, 1000) // 每秒查询一次进度
    },
    checkProgress() {
      if (!this.currentTaskId) return

      getGenerationProgress(this.currentTaskId).then(response => {
        this.progressInfo = response.data

        // 如果任务完成或失败，停止轮询
        if (this.progressInfo.status === 'COMPLETED' || this.progressInfo.status === 'FAILED') {
          if (this.progressTimer) {
            clearInterval(this.progressTimer)
            this.progressTimer = null
          }
        }
      }).catch(error => {
        console.error('查询进度失败:', error)
        // 查询失败也停止轮询
        if (this.progressTimer) {
          clearInterval(this.progressTimer)
          this.progressTimer = null
        }
      })
    },


    /** 清空小区选择 */
    handleCommunityClear() {
      this.queryParams.ownerId = null
    },

    /** 清空业主选择 */
    handleOwnerClear() {
      // 组件内部处理清空逻辑
    },
    /** 查询条件小区变化处理 */
    handleQueryCommunityChange(communityId, communityData) {
      this.queryParams.ownerId = null
      this.handleQuery()
    },

    getOwnerDisplayText(row) {
      if (!row.ownerName) return ''
      let displayText = row.ownerName
      if (row.rentalStatus === '1' && row.tenantName) {
        displayText += `（${row.tenantName}）`
      }
      return displayText
    },
    getPaymentStatusText(status) {
      const statusText = {
        0: '未支付',
        1: '部分支付',
        2: '已支付',
        3: '合并到新账单'
      }
      return statusText[status] || '未知状态'
    },
    getPaymentStatusType(status) {
      const statusType = {
        0: 'info',
        1: 'warning',
        2: 'success',
        3: 'danger'
      }
      return statusType[status] || 'info'
    },
    /** 判断行是否可选择 */
    isRowSelectable(row, index) {
      // 合并状态的账单不可选择
      return row.paymentStatus !== 3
    },
    handleRowClick(row) {
      console.log('点击账单行：', row)
      console.log('账单ID：', row.id)
      console.log('账单编号：', row.billNumber)

      // 同时调用API获取账单详情和收费记录
      Promise.all([
        getCommunityBillWithDetails(row.id),
        getBillPaymentRecords(row.id)
      ]).then(([billResponse, paymentResponse]) => {
        console.log('账单详情响应：', billResponse)
        console.log('收费记录响应：', paymentResponse)
        console.log('收费记录API URL：', `/system/paymentRecord/bill/${row.id}`)

        this.billDetailData = billResponse.data
        // 添加收费记录到详情数据中
        this.billDetailData.paymentRecords = paymentResponse.data || []

        console.log('设置的账单详情数据：', this.billDetailData)
        console.log('收费记录数量：', this.billDetailData.paymentRecords.length)
        console.log('收费记录详情：', this.billDetailData.paymentRecords)

        this.detailDrawerVisible = true
      }).catch(error => {
        console.error('获取数据失败：', error)
        console.error('错误详情：', error.response)

        // 即使收费记录获取失败，也尝试只显示账单详情
        getCommunityBillWithDetails(row.id).then(response => {
          this.billDetailData = response.data
          this.billDetailData.paymentRecords = []
          this.detailDrawerVisible = true
          this.$modal.msgWarning('账单详情获取成功，但收费记录获取失败：' + (error.message || '未知错误'))
        }).catch(detailError => {
          this.$modal.msgError('获取账单详情失败：' + (detailError.message || '未知错误'))
        })
      })
    },
    handleDetailClose() {
      this.detailDrawerVisible = false
      this.billDetailData = null
    },
    /** 打印催缴单 */
    handlePrint(row) {
      // 获取账单详细信息用于打印
      getCommunityBillWithDetails(row.id).then(response => {
        this.printBillData = response.data
        this.printBillList = []
        this.$nextTick(() => {
          this.$refs.printComponent.print()
        })
      }).catch(error => {
        console.error('获取账单详情失败:', error)
        this.$modal.msgError("获取账单详情失败")
      })
    },
    /** 批量打印催缴单 */
    handleBatchPrint() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要打印的账单")
        return
      }

      // 获取选中账单的详细信息
      const promises = this.ids.map(id => getCommunityBillWithDetails(id))

      Promise.all(promises).then(responses => {
        this.printBillData = null
        this.printBillList = responses.map(response => response.data)
        this.$nextTick(() => {
          this.$refs.printComponent.print()
        })
      }).catch(error => {
        console.error('获取账单详情失败:', error)
        this.$modal.msgError("获取账单详情失败")
      })
    },
    /** 小区批量打印催缴单 */
    handleCommunityBatchPrint() {
      this.communityPrintForm = {
        communityId: null
      }
      this.communityPrintOpen = true
    },
    /** 确认小区批量打印 */
    confirmCommunityPrint() {
      this.$refs["communityPrintForm"].validate(valid => {
        if (valid) {
          this.communityPrintLoading = true

          // 获取该小区的未缴费账单
          getUnpaidBillsByCommunity(this.communityPrintForm.communityId).then(response => {
            const unpaidBills = response.data || []

            if (unpaidBills.length === 0) {
              this.$modal.msgInfo("该小区没有未缴费的账单需要打印")
              this.communityPrintLoading = false
              return
            }

            // 设置打印数据
            this.printBillData = null
            this.printBillList = unpaidBills

            // 关闭对话框
            this.communityPrintOpen = false

            // 执行打印
            this.$nextTick(() => {
              this.$refs.printComponent.print()
            })

            this.$modal.msgSuccess(`成功获取 ${unpaidBills.length} 条未缴费账单，正在打印...`)
          }).catch(error => {
            console.error('获取未缴费账单失败:', error)
            this.$modal.msgError("获取未缴费账单失败")
          }).finally(() => {
            this.communityPrintLoading = false
          })
        }
      })
    },
    /** 取消小区批量打印 */
    cancelCommunityPrint() {
      this.communityPrintOpen = false
    }
  }
}
</script>

<style scoped>
.bill-detail-container {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.el-descriptions {
  margin-top: 10px;
}

.el-table {
  margin-top: 10px;
}

/* 表格行鼠标悬停效果 */
.el-table__row:hover {
  background-color: #f5f7fa;
}

/* 抽屉内容样式 */
.el-drawer__body {
  padding: 0;
}
</style>
