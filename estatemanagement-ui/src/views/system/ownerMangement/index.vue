<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <el-select
          v-model="queryParams.communityId"
          placeholder="请选择小区"
          clearable
          filterable
          remote
          reserve-keyword
          :remote-method="remoteCommunitySearch"
          :loading="communityLoading"
          @clear="handleQueryCommunityClear">
          <el-option v-for="item in communityOptions" :key="item.id" :label="item.communityName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="楼号" prop="buildingNumber">
        <el-input
          v-model="queryParams.buildingNumber"
          placeholder="请输入楼号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('buildingNumber')"
        />
      </el-form-item>
      <el-form-item label="门牌号" prop="houseNumber">
        <el-input
          v-model="queryParams.houseNumber"
          placeholder="请输入门牌号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('houseNumber')"
        />
      </el-form-item>
      <el-form-item label="业主名字" prop="ownerName">
        <el-input
          v-model="queryParams.ownerName"
          placeholder="请输入业主名字"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('ownerName')"
        />
      </el-form-item>
      <el-form-item label="业主电话" prop="ownerPhone">
        <el-input
          v-model="queryParams.ownerPhone"
          placeholder="请输入业主电话"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('ownerPhone')"
        />
      </el-form-item>
      <el-form-item label="租客名字" prop="tenantName">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租客名字"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('tenantName')"
        />
      </el-form-item>
      <el-form-item label="租客电话" prop="tenantPhone">
        <el-input
          v-model="queryParams.tenantPhone"
          placeholder="请输入租客电话"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('tenantPhone')"
        />
      </el-form-item>
      <el-form-item label="是否出租" prop="rentalStatus">
        <el-select v-model="queryParams.rentalStatus" placeholder="请选择是否出租" clearable>
          <el-option
            v-for="dict in dict.type.rental_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('remark')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:ownerMangement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:ownerMangement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:ownerMangement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:ownerMangement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ownerMangementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="小区" align="center" prop="communityId">
        <template slot-scope="scope">
          <span>{{ getCommunityName(scope.row.communityId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="楼号" align="center" prop="buildingNumber" />
      <el-table-column label="门牌号" align="center" prop="houseNumber" />
      <el-table-column label="房子面积" align="center" prop="houseArea" />
      <el-table-column label="房子类型" align="center" prop="houseType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.houseType === 1" type="success">房子</el-tag>
          <el-tag v-else-if="scope.row.houseType === 0" type="info">车库</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="业主名字" align="center" prop="ownerName" />
      <el-table-column label="业主电话" align="center" prop="ownerPhone" />
      <el-table-column label="是否出租" align="center" prop="rentalStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rental_status" :value="scope.row.rentalStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="租客名字" align="center" prop="tenantName">
        <template slot-scope="scope">
          <span>{{ scope.row.rentalStatus == 1 ? scope.row.tenantName : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="租客电话" align="center" prop="tenantPhone">
        <template slot-scope="scope">
          <span>{{ scope.row.rentalStatus == 1 ? scope.row.tenantPhone : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌信息" align="center" width="400">
        <template slot-scope="scope">
          <div v-if="scope.row.plateInfoList && scope.row.plateInfoList.length > 0">
            <div v-for="(plate, index) in scope.row.plateInfoList" :key="index" class="plate-info-display">
              <el-tag
                :type="getPlateTagType(plate)"
                size="small"
                style="margin: 2px;"
              >
                {{ plate.plateNumber }}
                <span v-if="plate.spaceNumber"> ({{ plate.spaceNumber }})</span>
              </el-tag>
              <el-tag
                :type="plate.isThreeCertificates === 1 ? 'success' : 'info'"
                size="mini"
                style="margin-left: 5px;"
              >
                {{ plate.isThreeCertificates === 1 ? '三证合一' : '非三证合一' }}
              </el-tag>
              <el-tag
                v-if="plate.expired"
                type="danger"
                size="mini"
                style="margin-left: 5px;"
              >
                已到期
              </el-tag>
              <el-tag
                type="info"
                size="mini"
                style="margin-left: 5px;"
              >
                {{plate.endDate ? plate.endDate : '未知'}}
              </el-tag>
            </div>
          </div>
          <span v-else style="color: #999;">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="人脸信息" align="center" width="300">
        <template slot-scope="scope">
          <div v-if="scope.row.faceInfoList && scope.row.faceInfoList.length > 0">
            <div v-for="(face, index) in scope.row.faceInfoList" :key="index" class="face-info-display">
              <el-tag
                :type="getFaceTagType(face)"
                size="small"
                style="margin: 2px;"
              >
                {{ face.personName }}
              </el-tag>
              <el-tag
                :type="getPersonTypeTagType(face.personType)"
                size="mini"
                style="margin-left: 5px;"
              >
                {{ getPersonTypeText(face.personType) }}
              </el-tag>
              <el-tag
                v-if="face.expired"
                type="danger"
                size="mini"
                style="margin-left: 5px;"
              >
                已到期
              </el-tag>
              <el-tag
                v-if="face.expireTime"
                type="info"
                size="mini"
                style="margin-left: 5px;"
              >
                {{ parseTime(face.expireTime, '{y}-{m}-{d}') }}
              </el-tag>
            </div>
          </div>
          <span v-else style="color: #999;">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="物业费到期时间" align="center" prop="communityPriceEnddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.communityPriceEnddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卫生费到期时间" align="center" prop="publicSanitationFeeEnddate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publicSanitationFeeEnddate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:ownerMangement:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:ownerMangement:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改业主管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区" prop="communityId">
          <el-select
            v-model="form.communityId"
            placeholder="请选择小区"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteCommunitySearch"
            :loading="communityLoading"
            @change="handleCommunityChange"
            @clear="handleFormCommunityClear">
            <el-option v-for="item in communityOptions" :key="item.id" :label="item.communityName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="楼号" prop="buildingNumber">
          <el-input v-model="form.buildingNumber" placeholder="请输入楼号" />
        </el-form-item>
        <el-form-item label="门牌号" prop="houseNumber">
          <el-input v-model="form.houseNumber" placeholder="请输入门牌号" />
        </el-form-item>
        <el-form-item label="房子面积" prop="houseArea">
          <el-input v-model="form.houseArea" placeholder="请输入房子面积" />
        </el-form-item>
        <el-form-item label="房子类型" prop="houseType">
          <el-radio-group v-model="form.houseType">
            <el-radio :label="1">房子</el-radio>
            <el-radio :label="0">车库</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="业主名字" prop="ownerName">
          <el-input v-model="form.ownerName" placeholder="请输入业主名字" />
        </el-form-item>
        <el-form-item label="业主电话" prop="ownerPhone">
          <el-input v-model="form.ownerPhone" placeholder="请输入业主电话" />
        </el-form-item>
        <el-form-item label="是否出租" prop="rentalStatus">
          <el-radio-group v-model="form.rentalStatus" @change="handleRentalStatusChange">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="租客名字" prop="tenantName" v-show="form.rentalStatus == 1">
          <el-input v-model="form.tenantName" placeholder="请输入租客名字" />
        </el-form-item>
        <el-form-item label="租客电话" prop="tenantPhone" v-show="form.rentalStatus == 1">
          <el-input v-model="form.tenantPhone" placeholder="请输入租客电话" />
        </el-form-item>
        <el-form-item label="车牌信息" v-if="form.id != null">
          <div class="plate-info-display-container">
            <div v-if="form.plateInfoList && form.plateInfoList.length > 0">
              <div v-for="(plate, index) in form.plateInfoList" :key="index" class="plate-info-display">
                <el-tag
                  :type="getPlateTagType(plate)"
                  size="small"
                  style="margin: 2px;"
                >
                  {{ plate.plateNumber }}
                  <span v-if="plate.spaceNumber"> ({{ plate.spaceNumber }})</span>
                  <span v-if="plate.isThreeCertificates === 1"> [三证合一]</span>
                  <span v-if="plate.expired" style="color: #F56C6C;"> [已到期]</span>
                </el-tag>
              </div>
            </div>
            <div v-else class="no-plate-info">
              <span style="color: #999;">暂无车牌信息</span>
            </div>
            <div style="font-size: 12px; color: #999; margin-top: 8px;">
              车牌信息请在停车信息管理中进行维护
            </div>
          </div>
        </el-form-item>
        <el-form-item label="人脸信息">
          <div class="face-info-container">
            <div v-for="(faceInfo, index) in form.faceInfoList" :key="index" class="face-info-item">
              <el-row :gutter="10" type="flex" align="middle">
                <el-col :span="6">
                  <el-input
                    v-model="faceInfo.personName"
                    placeholder="请输入人员姓名"
                  />
                </el-col>
                <el-col :span="6">
                  <el-select v-model="faceInfo.personType" placeholder="选择人员类型">
                    <el-option :label="'业主'" :value="1"></el-option>
                    <el-option :label="'租客'" :value="2"></el-option>
                    <el-option :label="'家属'" :value="3"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-date-picker
                    v-model="faceInfo.expireTime"
                    type="date"
                    placeholder="到期时间（默认卫生费/物业费）"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    style="width: 100%"
                  />
                  <div class="field-hint">留空则使用卫生费或物业费到期时间</div>
                </el-col>
                <el-col :span="4">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="small"
                    @click="removeFaceInfo(index)"
                    v-if="form.faceInfoList.length > 1"
                  >删除</el-button>
                </el-col>
              </el-row>
            </div>
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="addFaceInfo"
              style="margin-top: 15px;"
            >添加人脸</el-button>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOwnerMangement, getOwnerMangement, delOwnerMangement, addOwnerMangement, updateOwnerMangement } from "@/api/system/ownerMangement"
import { listCommunityMangement, searchCommunityMangement } from "@/api/system/communityMangement"

export default {
  name: "OwnerMangement",
  dicts: ['rental_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 业主管理表格数据
      ownerMangementList: [],
      // 小区列表
      communityOptions: [],

      // 远程搜索相关
      communityLoading: false,
      communitySearchKeyword: '',
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        buildingNumber: null,
        houseNumber: null,
        houseArea: null,
        ownerName: null,
        ownerPhone: null,
        tenantName: null,
        tenantPhone: null,
        rentalStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        buildingNumber: [
          { required: true, message: "请输入楼号", trigger: "blur" }
        ],
        houseNumber: [
          { required: true, message: "请输入门牌号", trigger: "blur" }
        ],
        houseType: [
          { required: true, message: "请选择房子类型", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getCommunityList()
  },
  methods: {
    /** 获取小区名称 */
    getCommunityName(communityId) {
      const community = this.communityOptions.find(item => item.id === communityId)
      return community ? community.communityName : ''
    },
    /** 查询业主管理列表 */
    getList() {
      this.loading = true
      listOwnerMangement(this.queryParams).then(response => {
        this.ownerMangementList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询小区列表 */
    getCommunityList() {
      // 初始加载前20条小区数据
      const searchParams = {
        pageNum: 1,
        pageSize: 20
      }
      listCommunityMangement(searchParams).then(response => {
        this.communityOptions = response.rows
      })
    },
    /** 远程搜索小区 */
    remoteCommunitySearch(query) {
      if (query !== '') {
        this.communityLoading = true
        this.communitySearchKeyword = query
        searchCommunityMangement(query).then(response => {
          this.communityOptions = response.rows || response.data || []
          this.communityLoading = false
        }).catch(() => {
          this.communityLoading = false
        })
      } else {
        this.communityOptions = []
      }
    },
    /** 查询条件清空小区选择 */
    handleQueryCommunityClear() {
      this.communityOptions = []
    },
    /** 表单清空小区选择 */
    handleFormCommunityClear() {
      this.communityOptions = []
      this.form.communityId = null

    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        buildingNumber: null,
        houseNumber: null,
        houseArea: null,
        houseType: 1,
        ownerName: null,
        ownerPhone: null,
        tenantName: null,
        tenantPhone: null,
        rentalStatus: 0,
        plateInfoList: [],
        faceInfoList: [{ personName: '', personType: 1, expireTime: null }],
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['buildingNumber', 'houseNumber', 'ownerName', 'ownerPhone', 'tenantName', 'tenantPhone', 'remark']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加业主管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getOwnerMangement(id).then(response => {
        this.form = response.data
        // 确保 rentalStatus 是数字类型
        if (this.form.rentalStatus !== null && this.form.rentalStatus !== undefined) {
          this.form.rentalStatus = Number(this.form.rentalStatus)
        } else {
          this.form.rentalStatus = 0
        }
        // 确保 houseType 是数字类型
        if (this.form.houseType !== null && this.form.houseType !== undefined) {
          this.form.houseType = Number(this.form.houseType)
        } else {
          this.form.houseType = 1
        }
        // 确保车牌信息列表存在（只读显示）
        if (!this.form.plateInfoList) {
          this.form.plateInfoList = []
        }
        // 确保人脸信息列表存在
        if (!this.form.faceInfoList || this.form.faceInfoList.length === 0) {
          this.form.faceInfoList = [{ personName: '', personType: 1, expireTime: null }]
        } else {
          // 确保每个人脸信息的字段类型正确
          this.form.faceInfoList.forEach(faceInfo => {
            if (faceInfo.personType !== null && faceInfo.personType !== undefined) {
              faceInfo.personType = Number(faceInfo.personType)
            } else {
              faceInfo.personType = 1
            }
            // 确保到期时间字段存在
            if (!faceInfo.hasOwnProperty('expireTime')) {
              faceInfo.expireTime = null
            }
          })
        }
        this.open = true
        this.title = "修改业主管理"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 过滤掉空的人脸信息，车牌信息不在此处维护
          const formData = { ...this.form }
          // 移除车牌信息，不在业主管理中维护
          delete formData.plateInfoList
          formData.faceInfoList = formData.faceInfoList.filter(faceInfo =>
            faceInfo.personName && faceInfo.personName.trim() !== ''
          )

          if (this.form.id != null) {
            updateOwnerMangement(formData).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addOwnerMangement(formData).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除业主管理编号为"' + ids + '"的数据项？').then(function() {
        return delOwnerMangement(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/ownerMangement/export', {
        ...this.queryParams
      }, `ownerMangement_${new Date().getTime()}.xlsx`)
    },
    /** 小区选择变化处理 */
    handleCommunityChange() {
      this.getList()
    },
    /** 出租状态变化处理 */
    handleRentalStatusChange(value) {
      if (value === 0) {
        // 如果选择不出租，清空租客信息
        this.form.tenantName = null
        this.form.tenantPhone = null
      }
    },


    /** 获取车牌标签类型 */
    getPlateTagType(plate) {
      if (plate.expired) {
        return 'danger' // 已到期显示红色
      } else if (plate.spaceNumber) {
        return 'success' // 有车位显示绿色
      } else {
        return 'warning' // 无车位显示橙色
      }
    },
    /** 添加人脸信息 */
    addFaceInfo() {
      this.form.faceInfoList.push({
        personName: '',
        personType: 1,
        expireTime: null
      })
    },
    /** 删除人脸信息 */
    removeFaceInfo(index) {
      if (this.form.faceInfoList.length > 1) {
        this.form.faceInfoList.splice(index, 1)
      }
    },
    /** 获取人脸标签类型 */
    getFaceTagType(face) {
      if (face.expired) {
        return 'danger' // 已到期显示红色
      } else if (face.status === 1) {
        return 'success' // 有效显示绿色
      } else {
        return 'info' // 禁用显示灰色
      }
    },
    /** 获取人员类型标签类型 */
    getPersonTypeTagType(personType) {
      switch (personType) {
        case 1:
          return 'success' // 业主显示绿色
        case 2:
          return 'warning' // 租客显示橙色
        case 3:
          return 'info' // 家属显示蓝色
        default:
          return 'info'
      }
    },
    /** 获取人员类型文本 */
    getPersonTypeText(personType) {
      switch (personType) {
        case 1:
          return '业主'
        case 2:
          return '租客'
        case 3:
          return '家属'
        default:
          return '未知'
      }
    }
  }
}
</script>

<style scoped>
.plate-info-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  background-color: #fafafa;
  min-height: 120px;
}

.plate-info-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.plate-info-item:last-child {
  margin-bottom: 0;
}

.plate-info-item:hover {
  border-color: #c0c4cc;
}

.plate-info-display {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.plate-info-display:last-child {
  margin-bottom: 0;
}

.face-info-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  background-color: #fafafa;
  min-height: 120px;
}

.face-info-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.face-info-item:last-child {
  margin-bottom: 0;
}

.face-info-item:hover {
  border-color: #c0c4cc;
}

.face-info-display {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.face-info-display:last-child {
  margin-bottom: 0;
}

.field-hint {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  line-height: 1.2;
}

/* 车牌信息显示样式 */
.plate-info-display-container {
  min-height: 40px;
}

.plate-info-display {
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.plate-info-display:last-child {
  margin-bottom: 0;
}

.no-plate-info {
  padding: 10px;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}
</style>
