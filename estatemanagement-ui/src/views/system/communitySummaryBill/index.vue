<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="汇总账单编号" prop="summaryNumber">
        <el-input
          v-model="queryParams.summaryNumber"
          placeholder="请输入汇总账单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="汇总周期" prop="summaryPeriodStart">
        <el-date-picker
          v-model="queryParams.summaryPeriodStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="开始日期"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="至" prop="summaryPeriodEnd">
        <el-date-picker
          v-model="queryParams.summaryPeriodEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="结束日期"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item label="财务确认状态" prop="financeConfirm">
        <el-select v-model="queryParams.financeConfirm" placeholder="请选择财务确认状态" clearable>
          <el-option label="未确认" value="0" />
          <el-option label="已确认" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="收钱确认状态" prop="receiveStatus">
        <el-select v-model="queryParams.receiveStatus" placeholder="请选择收钱确认状态" clearable>
          <el-option label="未确认" value="0" />
          <el-option label="已确认" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-s-data"
          size="mini"
          @click="handleGenerateSummary"
          v-hasPermi="['system:communitySummaryBill:add']"
        >生成汇总账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-setting"
          size="mini"
          @click="handleSummaryConfig"
          v-hasPermi="['system:communitySummaryBill:add']"
        >汇总配置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:communitySummaryBill:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:communitySummaryBill:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="communitySummaryBillList"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      highlight-current-row>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="汇总账单编号" align="center" prop="summaryNumber" min-width="140" />
      <el-table-column label="小区名称" align="center" prop="communityName" min-width="120"/>
      <el-table-column label="汇总周期" align="center" min-width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.summaryPeriodStart, '{y}-{m}-{d}') }} ~ {{ parseTime(scope.row.summaryPeriodEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应收总额" align="center" prop="totalAmount" min-width="120">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">{{ formatCurrency(scope.row.totalAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实收总额" align="center" prop="actualAmount" min-width="120">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">{{ formatCurrency(scope.row.actualAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款总额" align="center" prop="totalRefundAmount" min-width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.totalRefundAmount > 0" style="color: #F56C6C; font-weight: bold;">{{ formatCurrency(scope.row.totalRefundAmount) }}</span>
          <span v-else style="color: #C0C4CC;">¥0.00</span>
        </template>
      </el-table-column>
      <el-table-column label="收缴率" align="center" prop="collectionRate" min-width="100">
        <template slot-scope="scope">
          <el-progress
            :percentage="parseFloat(scope.row.collectionRate || 0)"
            :color="getProgressColor(scope.row.collectionRate)"
            :stroke-width="8"
            :show-text="true">
          </el-progress>
        </template>
      </el-table-column>
      <el-table-column label="财务确认" align="center" prop="financeConfirm" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getFinanceConfirmStatusTagType(scope.row.financeConfirm)" size="mini">
            {{ getFinanceConfirmStatusText(scope.row.financeConfirm) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="收钱确认" align="center" prop="receiveStatus" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="getReceiveStatusTagType(scope.row.receiveStatus)" size="mini">
            {{ getReceiveStatusText(scope.row.receiveStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleFinanceConfirm(scope.row, $event)"
            v-hasPermi="['system:communitySummaryBill:edit']"
            v-if="scope.row.financeConfirm == 0 || scope.row.financeConfirm == null"
          >财务确认</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-money"
            @click="handleConfirmReceive(scope.row, $event)"
            v-hasPermi="['system:communitySummaryBill:edit']"
            v-if="scope.row.receiveStatus == 0 || scope.row.receiveStatus == null"
          >确认收钱</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row, $event)"
            v-hasPermi="['system:communitySummaryBill:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 确认收钱对话框 -->
    <el-dialog title="确认收钱" :visible.sync="receiveOpen" width="500px" append-to-body>
      <el-form ref="receiveForm" :model="receiveForm" :rules="receiveRules" label-width="120px">
        <el-form-item label="汇总账单编号">
          <el-input v-model="receiveForm.summaryNumber" disabled />
        </el-form-item>
        <el-form-item label="实收总额">
          <el-input v-model="receiveForm.actualAmount" disabled />
        </el-form-item>
        <el-form-item label="实际收到金额" prop="actualReceivedAmount">
          <el-input v-model="receiveForm.actualReceivedAmount" placeholder="请输入实际收到金额" type="number" />
        </el-form-item>
        <el-form-item label="上交日期" prop="submitDate">
          <el-date-picker
            v-model="receiveForm.submitDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择上交日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="上交人" prop="submitBy">
          <el-input v-model="receiveForm.submitBy" placeholder="请输入上交人" />
        </el-form-item>
        <el-form-item label="收钱备注" prop="receiveRemark">
          <el-input v-model="receiveForm.receiveRemark" type="textarea" placeholder="请输入收钱备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReceiveForm">确 定</el-button>
        <el-button @click="cancelReceive">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 财务确认对话框 -->
    <el-dialog title="财务确认" :visible.sync="financeConfirmOpen" width="500px" append-to-body>
      <el-form ref="financeConfirmForm" :model="financeConfirmForm" :rules="financeConfirmRules" label-width="120px">
        <el-form-item label="汇总账单编号">
          <el-input v-model="financeConfirmForm.summaryNumber" disabled />
        </el-form-item>
        <el-form-item label="应收总额">
          <el-input v-model="financeConfirmForm.totalAmount" disabled />
        </el-form-item>
        <el-form-item label="实收总额">
          <el-input v-model="financeConfirmForm.actualAmount" disabled />
        </el-form-item>
        <el-form-item label="财务确认日期" prop="financeConfirmDate">
          <el-date-picker
            v-model="financeConfirmForm.financeConfirmDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择财务确认日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="财务确认人" prop="financeConfirmBy">
          <el-input v-model="financeConfirmForm.financeConfirmBy" placeholder="请输入财务确认人" />
        </el-form-item>
        <el-form-item label="确认备注" prop="financeRemark">
          <el-input v-model="financeConfirmForm.financeRemark" type="textarea" placeholder="请输入确认备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFinanceConfirmForm">确 定</el-button>
        <el-button @click="cancelFinanceConfirm">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 生成汇总账单对话框 -->
    <el-dialog title="生成汇总账单" :visible.sync="generateSummaryOpen" width="500px" append-to-body>
      <el-form ref="generateSummaryForm" :model="generateSummaryForm" :rules="generateSummaryRules" label-width="120px">
        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="generateSummaryForm.communityId" placeholder="请选择小区（不选择表示全部小区）" clearable style="width: 100%">
            <el-option
              v-for="community in communityList"
              :key="community.id"
              :label="community.communityName"
              :value="community.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="汇总开始日期" prop="startDate">
          <el-date-picker
            v-model="generateSummaryForm.startDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择汇总开始日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="汇总结束日期" prop="endDate">
          <el-date-picker
            v-model="generateSummaryForm.endDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择汇总结束日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGenerateSummary">确 定</el-button>
        <el-button @click="cancelGenerateSummary">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 汇总配置对话框 -->
    <el-dialog title="汇总账单自动生成配置" :visible.sync="summaryConfigOpen" width="600px" append-to-body>
      <el-form ref="summaryConfigForm" :model="summaryConfigForm" :rules="summaryConfigRules" label-width="140px">
        <el-form-item label="选择小区" prop="communityId">
          <el-select v-model="summaryConfigForm.communityId" placeholder="请选择小区（不选择表示全部小区）" clearable style="width: 100%" @change="handleConfigCommunityChange">
            <el-option label="全部小区" :value="null"></el-option>
            <el-option
              v-for="community in communityList"
              :key="community.id"
              :label="community.communityName"
              :value="community.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="启用自动生成">
          <el-switch v-model="summaryConfigForm.autoGenerate" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
        <el-form-item label="生成间隔天数" prop="intervalDays">
          <el-input-number v-model="summaryConfigForm.intervalDays" :min="1" :max="365" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="配置状态">
          <el-switch v-model="summaryConfigForm.status" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="summaryConfigForm.remark" type="textarea" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSummaryConfig">确 定</el-button>
        <el-button @click="cancelSummaryConfig">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 右侧明细弹窗 -->
    <el-drawer
      title="汇总账单详情"
      :visible.sync="detailDrawerVisible"
      direction="rtl"
      size="700px"
      :before-close="handleDetailDrawerClose">
      <div class="detail-drawer-content" v-if="selectedRecord">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-document"></i>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>汇总账单编号：</label>
                <span class="detail-value">{{ selectedRecord.summaryNumber }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>小区名称：</label>
                <span class="detail-value">{{ selectedRecord.communityName }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="24">
              <div class="detail-item">
                <label>汇总周期：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.summaryPeriodStart, '{y}-{m}-{d}') }} ~ {{ parseTime(selectedRecord.summaryPeriodEnd, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 费用明细 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-money"></i>
            <span>费用明细</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>物业费：</label>
                <span class="detail-value amount">{{ formatCurrency(selectedRecord.totalPropertyFee) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>停车费：</label>
                <span class="detail-value amount">{{ formatCurrency(selectedRecord.totalParkingFee) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>电梯费：</label>
                <span class="detail-value amount">{{ formatCurrency(selectedRecord.totalElevatorFee) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>卫生费：</label>
                <span class="detail-value amount">{{ formatCurrency(selectedRecord.totalSanitationFee) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>应收总额：</label>
                <span class="detail-value amount due-amount">{{ formatCurrency(selectedRecord.totalAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>实收总额：</label>
                <span class="detail-value amount actual-amount">{{ formatCurrency(selectedRecord.actualAmount) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 退费信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-refresh-left"></i>
            <span>退费信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>退费总额：</label>
                <span v-if="selectedRecord.totalRefundAmount > 0" class="detail-value amount refund-amount">
                  {{ formatCurrency(selectedRecord.totalRefundAmount) }}
                </span>
                <span v-else class="detail-value" style="color: #C0C4CC;">¥0.00</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>退费笔数：</label>
                <span class="detail-value">{{ selectedRecord.refundCount || 0 }} 笔</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="24">
              <div class="detail-item">
                <label>退费明细：</label>
                <div class="refund-details" v-if="selectedRecord.totalRefundAmount > 0">
                  <el-tag v-if="selectedRecord.propertyRefundAmount > 0" type="primary" size="mini">
                    物业费: {{ formatCurrency(selectedRecord.propertyRefundAmount) }}
                  </el-tag>
                  <el-tag v-if="selectedRecord.parkingRefundAmount > 0" type="success" size="mini">
                    停车费: {{ formatCurrency(selectedRecord.parkingRefundAmount) }}
                  </el-tag>
                  <el-tag v-if="selectedRecord.sanitationRefundAmount > 0" type="warning" size="mini">
                    卫生费: {{ formatCurrency(selectedRecord.sanitationRefundAmount) }}
                  </el-tag>
                  <el-tag v-if="selectedRecord.elevatorRefundAmount > 0" type="info" size="mini">
                    电梯费: {{ formatCurrency(selectedRecord.elevatorRefundAmount) }}
                  </el-tag>
                  <el-tag v-if="selectedRecord.lateFeeRefundAmount > 0" type="danger" size="mini">
                    滞纳金: {{ formatCurrency(selectedRecord.lateFeeRefundAmount) }}
                  </el-tag>
                </div>
                <div v-else class="no-refund-info">
                  <span style="color: #C0C4CC; font-style: italic;">暂无退费记录</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 收缴统计 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-pie-chart"></i>
            <span>收缴统计</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="8">
              <div class="detail-item">
                <label>总户数：</label>
                <span class="detail-value">{{ selectedRecord.houseCount }} 户</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>已缴费户数：</label>
                <span class="detail-value">{{ selectedRecord.paidHouseCount }} 户</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>未缴费户数：</label>
                <span class="detail-value">{{ selectedRecord.unpaidHouseCount }} 户</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>未收总额：</label>
                <span class="detail-value amount unpaid-amount">{{ formatCurrency(selectedRecord.unpaidAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>收缴率：</label>
                <span class="detail-value">{{ selectedRecord.collectionRate }}%</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 上交信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-upload"></i>
            <span>上交信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>上交状态：</label>
                <el-tag :type="getSubmitStatusTagType(selectedRecord.submitStatus)" size="mini">
                  {{ getSubmitStatusText(selectedRecord.submitStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>上交人：</label>
                <span class="detail-value">{{ selectedRecord.submitBy || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row" v-if="selectedRecord.submitDate">
            <el-col :span="24">
              <div class="detail-item">
                <label>上交日期：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.submitDate, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 财务确认信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-check"></i>
            <span>财务确认信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>财务确认：</label>
                <el-tag :type="getFinanceConfirmStatusTagType(selectedRecord.financeConfirm)" size="mini">
                  {{ getFinanceConfirmStatusText(selectedRecord.financeConfirm) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>财务确认人：</label>
                <span class="detail-value">{{ selectedRecord.financeConfirmBy || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row" v-if="selectedRecord.financeConfirmDate">
            <el-col :span="24">
              <div class="detail-item">
                <label>财务确认日期：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.financeConfirmDate, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 收钱确认信息 -->
        <div class="detail-section">
          <div class="section-title">
            <i class="el-icon-wallet"></i>
            <span>收钱确认信息</span>
          </div>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>收钱确认状态：</label>
                <el-tag :type="getReceiveStatusTagType(selectedRecord.receiveStatus)" size="mini">
                  {{ getReceiveStatusText(selectedRecord.receiveStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>收钱确认人：</label>
                <span class="detail-value">{{ selectedRecord.receivedBy || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row">
            <el-col :span="12">
              <div class="detail-item">
                <label>实际收到金额：</label>
                <span class="detail-value amount actual-amount">{{ formatCurrency(selectedRecord.actualReceivedAmount) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <label>收钱差额：</label>
                <span class="detail-value amount" :class="getDifferenceClass(selectedRecord.receiveDifference)">{{ formatCurrency(selectedRecord.receiveDifference) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="detail-row" v-if="selectedRecord.receivedDate">
            <el-col :span="24">
              <div class="detail-item">
                <label>收钱确认日期：</label>
                <span class="detail-value">{{ parseTime(selectedRecord.receivedDate, '{y}-{m}-{d}') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section" v-if="selectedRecord.remark">
          <div class="section-title">
            <i class="el-icon-edit-outline"></i>
            <span>备注信息</span>
          </div>
          <div class="detail-remark">
            {{ selectedRecord.remark }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button
            v-if="selectedRecord.financeConfirm == 0 || selectedRecord.financeConfirm == null"
            type="success"
            size="small"
            icon="el-icon-check"
            @click="handleFinanceConfirmFromDetail"
            v-hasPermi="['system:communitySummaryBill:edit']">
            财务确认
          </el-button>
          <el-button
            v-if="selectedRecord.receiveStatus == 0 || selectedRecord.receiveStatus == null"
            type="warning"
            size="small"
            icon="el-icon-money"
            @click="handleConfirmReceiveFromDetail"
            v-hasPermi="['system:communitySummaryBill:edit']">
            确认收钱
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { listCommunitySummaryBill, getCommunitySummaryBill, delCommunitySummaryBill, addCommunitySummaryBill, updateCommunitySummaryBill, confirmReceive, generateSummaryBills, getSummaryBillGenerationConfigByCommunityId, addSummaryBillGenerationConfig, updateSummaryBillGenerationConfig } from "@/api/system/communitySummaryBill"
import { listCommunityMangement } from "@/api/system/communityMangement"
import { SUBMIT_STATUS, FINANCE_CONFIRM_STATUS, RECEIVE_STATUS, STATUS_TEXT, STATUS_TAG_TYPE } from "@/constants/summaryBill"

export default {
  name: "CommunitySummaryBill",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小区汇总账单表格数据
      communitySummaryBillList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示确认收钱对话框
      receiveOpen: false,
      // 是否显示财务确认对话框
      financeConfirmOpen: false,
      // 是否显示生成汇总账单对话框
      generateSummaryOpen: false,
      // 是否显示汇总配置对话框
      summaryConfigOpen: false,
      // 小区列表
      communityList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        summaryNumber: null,
        summaryPeriodStart: null,
        summaryPeriodEnd: null,
        financeConfirm: null,
        receiveStatus: null,
      },
      // 表单参数
      form: {},
      // 确认收钱表单参数
      receiveForm: {},
      // 财务确认表单参数
      financeConfirmForm: {},
      // 生成汇总账单表单参数
      generateSummaryForm: {},
      // 汇总配置表单参数
      summaryConfigForm: {},
      // 右侧明细弹窗
      detailDrawerVisible: false,
      selectedRecord: null,
      // 表单校验
      rules: {
      },
      // 确认收钱表单校验
      receiveRules: {
        actualReceivedAmount: [
          { required: true, message: "实际收到金额不能为空", trigger: "blur" }
        ],
        submitDate: [
          { required: true, message: "上交日期不能为空", trigger: "change" }
        ],
        submitBy: [
          { required: true, message: "上交人不能为空", trigger: "blur" }
        ]
      },
      // 财务确认表单校验
      financeConfirmRules: {
        financeConfirmDate: [
          { required: true, message: "财务确认日期不能为空", trigger: "change" }
        ],
        financeConfirmBy: [
          { required: true, message: "财务确认人不能为空", trigger: "blur" }
        ]
      },
      // 生成汇总账单表单校验
      generateSummaryRules: {
        startDate: [
          { required: true, message: "汇总开始日期不能为空", trigger: "change" }
        ],
        endDate: [
          { required: true, message: "汇总结束日期不能为空", trigger: "change" }
        ]
      },
      // 汇总配置表单校验
      summaryConfigRules: {
        intervalDays: [
          { required: true, message: "生成间隔天数不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getCommunityList()
  },
  methods: {
    /** 查询小区汇总账单列表 */
    getList() {
      this.loading = true
      listCommunitySummaryBill(this.queryParams).then(response => {
        this.communitySummaryBillList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        summaryNumber: null,
        summaryPeriodStart: null,
        summaryPeriodEnd: null,
        totalPropertyFee: null,
        totalParkingFee: null,
        totalElevatorFee: null,
        totalSanitationFee: null,
        totalAmount: null,
        actualAmount: null,
        unpaidAmount: null,
        collectionRate: null,
        houseCount: null,
        paidHouseCount: null,
        unpaidHouseCount: null,
        submitStatus: null,
        submitDate: null,
        submitBy: null,
        financeConfirm: null,
        financeConfirmDate: null,
        financeConfirmBy: null,
        actualReceivedAmount: null,
        receivedDate: null,
        receivedBy: null,
        receiveDifference: null,
        receiveRemark: null,
        receiveStatus: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    // 重置确认收钱表单
    resetReceiveForm() {
      this.receiveForm = {
        id: null,
        summaryNumber: null,
        actualAmount: null,
        actualReceivedAmount: null,
        submitDate: null,
        submitBy: null,
        receiveRemark: null
      }
      this.resetForm("receiveForm")
    },
    // 重置财务确认表单
    resetFinanceConfirmForm() {
      this.financeConfirmForm = {
        id: null,
        summaryNumber: null,
        totalAmount: null,
        actualAmount: null,
        financeConfirmDate: null,
        financeConfirmBy: null,
        financeRemark: null
      }
      this.resetForm("financeConfirmForm")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加小区汇总账单"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCommunitySummaryBill(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改小区汇总账单"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCommunitySummaryBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addCommunitySummaryBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除小区汇总账单编号为"' + ids + '"的数据项？').then(function() {
        return delCommunitySummaryBill(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/communitySummaryBill/export', {
        ...this.queryParams
      }, `communitySummaryBill_${new Date().getTime()}.xlsx`)
    },
    /** 确认收钱按钮操作 */
    handleConfirmReceive(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      this.resetReceiveForm()
      this.receiveForm.id = row.id
      this.receiveForm.summaryNumber = row.summaryNumber
      this.receiveForm.actualAmount = row.actualAmount
      this.receiveOpen = true
    },
    /** 取消确认收钱 */
    cancelReceive() {
      this.receiveOpen = false
      this.resetReceiveForm()
    },
    /** 提交确认收钱表单 */
    submitReceiveForm() {
      this.$refs["receiveForm"].validate(valid => {
        if (valid) {
          confirmReceive(this.receiveForm).then(response => {
            this.$modal.msgSuccess("确认收钱成功")
            this.receiveOpen = false
            this.getList()
          })
        }
      })
    },
    /** 财务确认按钮操作 */
    handleFinanceConfirm(row, event) {
      if (event) {
        event.stopPropagation() // 阻止事件冒泡
      }
      this.resetFinanceConfirmForm()
      this.financeConfirmForm.id = row.id
      this.financeConfirmForm.summaryNumber = row.summaryNumber
      this.financeConfirmForm.totalAmount = row.totalAmount
      this.financeConfirmForm.actualAmount = row.actualAmount
      this.financeConfirmOpen = true
    },
    /** 取消财务确认 */
    cancelFinanceConfirm() {
      this.financeConfirmOpen = false
      this.resetFinanceConfirmForm()
    },
    /** 提交财务确认表单 */
    submitFinanceConfirmForm() {
      this.$refs["financeConfirmForm"].validate(valid => {
        if (valid) {
          // 调用财务确认API
          this.$http.post('/system/communitySummaryBill/financeConfirm', {
            id: this.financeConfirmForm.id,
            financeConfirmDate: this.financeConfirmForm.financeConfirmDate,
            financeConfirmBy: this.financeConfirmForm.financeConfirmBy,
            financeRemark: this.financeConfirmForm.financeRemark
          }).then(response => {
            this.$modal.msgSuccess("财务确认成功")
            this.financeConfirmOpen = false
            this.getList()
          })
        }
      })
    },
    /** 获取小区列表 */
    getCommunityList() {
      listCommunityMangement({}).then(response => {
        this.communityList = response.rows
      })
    },
    /** 生成汇总账单按钮操作 */
    handleGenerateSummary() {
      this.resetGenerateSummaryForm()
      this.generateSummaryOpen = true
    },
    /** 重置生成汇总账单表单 */
    resetGenerateSummaryForm() {
      this.generateSummaryForm = {
        communityId: null,
        startDate: null,
        endDate: null
      }
      this.resetForm("generateSummaryForm")
    },
    /** 取消生成汇总账单 */
    cancelGenerateSummary() {
      this.generateSummaryOpen = false
      this.resetGenerateSummaryForm()
    },
    /** 提交生成汇总账单 */
    submitGenerateSummary() {
      this.$refs["generateSummaryForm"].validate(valid => {
        if (valid) {
          // 验证日期范围
          if (new Date(this.generateSummaryForm.startDate) > new Date(this.generateSummaryForm.endDate)) {
            this.$modal.msgError("开始日期不能晚于结束日期")
            return
          }

          generateSummaryBills(this.generateSummaryForm).then(response => {
            this.$modal.msgSuccess("汇总账单生成任务已启动")
            this.generateSummaryOpen = false
            this.getList()
          })
        }
      })
    },
    /** 汇总配置按钮操作 */
    handleSummaryConfig() {
      this.resetSummaryConfigForm()
      this.summaryConfigOpen = true
      // 如果有选中的小区，查询该小区的配置
      if (this.queryParams.communityId) {
        this.summaryConfigForm.communityId = this.queryParams.communityId
        this.handleConfigCommunityChange()
      }
    },
    /** 重置汇总配置表单 */
    resetSummaryConfigForm() {
      this.summaryConfigForm = {
        communityId: null,
        autoGenerate: 0,
        intervalDays: 30,
        status: 1,
        remark: null
      }
      this.resetForm("summaryConfigForm")
    },
    /** 配置小区变化处理 */
    handleConfigCommunityChange() {
      // 查询该小区的现有配置
      if (this.summaryConfigForm.communityId) {
        getSummaryBillGenerationConfigByCommunityId(this.summaryConfigForm.communityId).then(response => {
          if (response.data) {
            // 如果存在配置，填充表单
            this.summaryConfigForm = {
              ...this.summaryConfigForm,
              ...response.data
            }
          }
        }).catch(error => {
          // 如果没有配置，保持默认值
          console.log('该小区暂无配置')
        })
      }
    },
    /** 取消汇总配置 */
    cancelSummaryConfig() {
      this.summaryConfigOpen = false
      this.resetSummaryConfigForm()
    },
    /** 提交汇总配置 */
    submitSummaryConfig() {
      this.$refs["summaryConfigForm"].validate(valid => {
        if (valid) {
          // 判断是新增还是修改
          if (this.summaryConfigForm.id) {
            // 修改配置
            updateSummaryBillGenerationConfig(this.summaryConfigForm).then(response => {
              this.$modal.msgSuccess("汇总配置修改成功")
              this.summaryConfigOpen = false
            })
          } else {
            // 新增配置
            addSummaryBillGenerationConfig(this.summaryConfigForm).then(response => {
              this.$modal.msgSuccess("汇总配置保存成功")
              this.summaryConfigOpen = false
            })
          }
        }
      })
    },
    /** 获取上交状态文本 */
    getSubmitStatusText(status) {
      return STATUS_TEXT.SUBMIT_STATUS[status] || '未知'
    },
    /** 获取上交状态标签类型 */
    getSubmitStatusTagType(status) {
      return STATUS_TAG_TYPE.SUBMIT_STATUS[status] || 'info'
    },
    /** 获取财务确认状态文本 */
    getFinanceConfirmStatusText(status) {
      return STATUS_TEXT.FINANCE_CONFIRM_STATUS[status] || '未知'
    },
    /** 获取财务确认状态标签类型 */
    getFinanceConfirmStatusTagType(status) {
      return STATUS_TAG_TYPE.FINANCE_CONFIRM_STATUS[status] || 'info'
    },
    /** 获取收钱确认状态文本 */
    getReceiveStatusText(status) {
      return STATUS_TEXT.RECEIVE_STATUS[status] || '未知'
    },
    /** 获取收钱确认状态标签类型 */
    getReceiveStatusTagType(status) {
      return STATUS_TAG_TYPE.RECEIVE_STATUS[status] || 'info'
    },
    /** 处理行点击 */
    handleRowClick(row) {
      this.selectedRecord = row
      this.detailDrawerVisible = true
    },
    /** 关闭明细弹窗 */
    handleDetailDrawerClose() {
      this.detailDrawerVisible = false
      this.selectedRecord = null
    },
    /** 从明细弹窗修改 */
    handleUpdateFromDetail() {
      if (this.selectedRecord) {
        this.handleUpdate(this.selectedRecord)
      }
    },
    /** 从明细弹窗财务确认 */
    handleFinanceConfirmFromDetail() {
      if (this.selectedRecord) {
        this.handleFinanceConfirm(this.selectedRecord)
      }
    },
    /** 从明细弹窗确认收钱 */
    handleConfirmReceiveFromDetail() {
      if (this.selectedRecord) {
        this.handleConfirmReceive(this.selectedRecord)
      }
    },
    /** 格式化货币显示 */
    formatCurrency(amount) {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return '¥0.00'
      }
      return '¥' + parseFloat(amount).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    /** 获取进度条颜色 */
    getProgressColor(rate) {
      const percentage = parseFloat(rate || 0)
      if (percentage >= 90) return '#67C23A'
      if (percentage >= 70) return '#E6A23C'
      if (percentage >= 50) return '#F56C6C'
      return '#909399'
    },
    /** 获取差额样式类 */
    getDifferenceClass(difference) {
      const diff = parseFloat(difference || 0)
      if (diff > 0) return 'difference-positive'
      if (diff < 0) return 'difference-negative'
      return 'difference-zero'
    }
  }
}
</script>

<style scoped>
/* 右侧明细弹窗样式 */
.detail-drawer-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 30px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f2f5;
}

.section-title i {
  margin-right: 8px;
  color: #409EFF;
  font-size: 18px;
}

.detail-row {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.detail-item label {
  min-width: 100px;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
  margin-right: 10px;
}

.detail-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
  word-break: break-all;
}

.detail-value.amount {
  font-weight: bold;
  font-size: 16px;
}

.detail-value.due-amount {
  color: #E6A23C;
}

.detail-value.actual-amount {
  color: #67C23A;
}

.detail-value.refund-amount {
  color: #F56C6C;
}

.detail-value.unpaid-amount {
  color: #909399;
}

.detail-value.difference-positive {
  color: #F56C6C;
}

.detail-value.difference-negative {
  color: #409EFF;
}

.detail-value.difference-zero {
  color: #67C23A;
}

.refund-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.refund-details .el-tag {
  margin: 0;
  margin-right: 8px;
  margin-bottom: 4px;
  font-size: 11px;
}

.no-refund-info {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  text-align: center;
  border: 1px dashed #e9ecef;
}

.detail-remark {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #909399;
  line-height: 1.6;
  color: #606266;
}

.detail-actions {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover {
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-drawer-content {
    padding: 15px;
  }

  .detail-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .detail-item {
    flex-direction: column;
  }

  .detail-item label {
    min-width: auto;
    margin-bottom: 5px;
  }

  .detail-actions {
    flex-direction: column;
  }

  .detail-actions .el-button {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
