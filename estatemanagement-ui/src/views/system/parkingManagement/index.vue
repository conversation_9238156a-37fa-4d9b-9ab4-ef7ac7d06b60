<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <CommunitySelect
          v-model="queryParams.communityId"
          placeholder="请选择小区"
          clearable
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('plateNumber')"
        />
      </el-form-item>
      <el-form-item label="车位号" prop="spaceNumber">
        <el-input
          v-model="queryParams.spaceNumber"
          placeholder="请输入车位号"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('spaceNumber')"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="空闲" value="0"></el-option>
          <el-option label="占用" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="三证合一" prop="isThreeCertificates">
        <el-select v-model="queryParams.isThreeCertificates" placeholder="请选择" clearable>
          <el-option label="是" value="1"></el-option>
          <el-option label="否" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:parkingManagement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:parkingManagement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:parkingManagement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:parkingManagement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="parkingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="小区名称" align="center" prop="communityName" />
      <el-table-column label="车位号" align="center" prop="spaceNumber" />
      <el-table-column label="车牌号" align="center" prop="plateNumber">
        <template slot-scope="scope">
          <span>{{ scope.row.plateNumber || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="业主信息" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.ownerName">
            {{ scope.row.ownerName }}
            <br/>
            {{ scope.row.buildingNumber }}栋{{ scope.row.houseNumber }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center" prop="ownerPhone" />
      <el-table-column label="月租金" align="center" prop="monthlyFee">
        <template slot-scope="scope">
          <span>{{ scope.row.monthlyFee ? scope.row.monthlyFee + '元/月' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="三证合一" align="center" prop="isThreeCertificates">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isThreeCertificates === 1 ? 'success' : 'warning'">
            {{ scope.row.isThreeCertificates === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row)">
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开始日期" align="center" prop="startDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" prop="endDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:parkingManagement:edit']"
          >修改</el-button>

          <el-button
            v-if="scope.row.status === 1"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleRelease(scope.row)"
            v-hasPermi="['system:parkingManagement:release']"
          >释放</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:parkingManagement:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改车位管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区" prop="communityId">
          <CommunitySelect
            v-model="form.communityId"
            :disabled="form.id != null"
            @change="handleFormCommunityChange"
            @clear="handleFormCommunityClear"
          />
        </el-form-item>
        <el-form-item label="车位号" prop="spaceNumber">
          <el-input
            v-model="form.spaceNumber"
            :placeholder="form.id != null ? '请输入车位号' : '单个车位：A001 或 批量车位：A001-A999'" />
          <div v-if="form.id == null" style="font-size: 12px; color: #999; margin-top: 5px;">
            支持批量创建：输入格式如 A001-A999，将自动创建从A001到A999的所有车位
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listParkingManagement, getParkingManagement, delParkingManagement, addParkingManagement, updateParkingManagement, releaseParking, batchCreateParking } from "@/api/system/parkingManagement";
import { OwnerSelect, CommunitySelect } from "@/components";

export default {
  name: "ParkingManagement",
  components: {
    OwnerSelect,
    CommunitySelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车位管理表格数据
      parkingList: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否批量创建
      isBatchCreate: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        plateNumber: null,
        spaceNumber: null,
        status: null,
        isThreeCertificates: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "小区不能为空", trigger: "change" }
        ],
        spaceNumber: [
          { required: true, message: "车位号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询车位管理列表 */
    getList() {
      this.loading = true;
      listParkingManagement(this.queryParams).then(response => {
        this.parkingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },


    /** 表单小区变化处理 */
    handleFormCommunityChange(communityId, communityData) {
      // 清空业主选择
      this.form.ownerId = null
    },
    /** 表单小区清空处理 */
    handleFormCommunityClear() {
      this.form.communityId = null
      this.form.ownerId = null
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        plateNumber: null,
        spaceNumber: null,
        monthlyFee: null,
        startDate: null,
        endDate: null,
        status: 0,
        isThreeCertificates: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['plateNumber', 'spaceNumber']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isBatchCreate = false;
      // 设置开始时间为当前时间
      const today = new Date();
      this.form.startDate = today.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
      this.form.endDate = this.form.startDate; // 结束时间默认与开始时间相同
      this.open = true;
      this.title = "添加车位管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isBatchCreate = false;
      const id = row.id || this.ids
      getParkingManagement(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车位管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateParkingManagement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 检查是否为批量创建
            if (this.isBatchSpaceNumber(this.form.spaceNumber)) {
              this.batchCreateParkingSpaces();
            } else {
              addParkingManagement(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        }
      });
    },
    /** 检查是否为批量车位号格式 */
    isBatchSpaceNumber(spaceNumber) {
      return spaceNumber && spaceNumber.includes('-');
    },
    /** 批量创建车位 */
    batchCreateParkingSpaces() {
      const spaceRange = this.form.spaceNumber;
      const [startSpace, endSpace] = spaceRange.split('-');

      if (!startSpace || !endSpace) {
        this.$modal.msgError("车位号格式错误，请使用格式：A001-A999");
        return;
      }

      // 解析车位号格式
      const startMatch = startSpace.match(/^([A-Za-z]*)(\d+)$/);
      const endMatch = endSpace.match(/^([A-Za-z]*)(\d+)$/);

      if (!startMatch || !endMatch || startMatch[1] !== endMatch[1]) {
        this.$modal.msgError("车位号格式错误，前缀必须相同，如：A001-A999");
        return;
      }

      const prefix = startMatch[1];
      const startNum = parseInt(startMatch[2]);
      const endNum = parseInt(endMatch[2]);
      const numLength = startMatch[2].length;

      if (startNum >= endNum) {
        this.$modal.msgError("起始车位号必须小于结束车位号");
        return;
      }

      const totalSpaces = endNum - startNum + 1;
      if (totalSpaces > 1000) {
        this.$modal.msgError("批量创建车位数量不能超过1000个");
        return;
      }

      // 确认批量创建
      this.$modal.confirm(`将创建 ${totalSpaces} 个车位（${startSpace} 到 ${endSpace}），是否继续？`).then(() => {
        const batchData = {
          communityId: this.form.communityId,
          prefix: prefix,
          startNum: startNum,
          endNum: endNum,
          numLength: numLength
        };

        // 调用批量创建接口
        this.batchAddParkingSpaces(batchData);
      }).catch(() => {});
    },
    /** 调用批量创建接口 */
    batchAddParkingSpaces(batchData) {
      batchCreateParking(batchData).then(response => {
        this.$modal.msgSuccess(`成功创建 ${response.data} 个车位`);
        this.open = false;
        this.getList();
      }).catch(error => {
        this.$modal.msgError("批量创建失败：" + (error.msg || error.message));
      });
    },
    /** 业主变化事件 */
    onOwnerChange(ownerId) {
      // 业主变化时可以做一些处理
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除车位管理编号为"' + ids + '"的数据项？').then(function() {
        return delParkingManagement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 释放车位 */
    handleRelease(row) {
      this.$modal.confirm('是否确认释放车位"' + row.spaceNumber + '"？').then(function() {
        return releaseParking(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("释放成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/parkingManagement/export', {
        ...this.queryParams
      }, `parkingManagement_${new Date().getTime()}.xlsx`)
    },
    /** 获取状态文本 */
    getStatusText(row) {
      if (!row.status || row.status === 0) {
        return '空闲'
      }
      if (this.isExpired(row)) {
        return '到期'
      }
      return '占用'
    },
    /** 获取状态标签类型 */
    getStatusTagType(row) {
      if (!row.status || row.status === 0) {
        return 'info'
      }
      if (this.isExpired(row)) {
        return 'warning'
      }
      return 'success'
    },
    /** 判断是否到期 */
    isExpired(row) {
      if (!row.endDate || !row.plateNumber) {
        return false
      }
      const now = new Date()
      const endDate = new Date(row.endDate)
      return now > endDate
    }
  }
};
</script>
