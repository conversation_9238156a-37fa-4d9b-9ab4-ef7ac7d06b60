<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区名字" prop="communityName">
        <el-input
          v-model="queryParams.communityName"
          placeholder="请输入小区名字"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('communityName')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:communityMangement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:communityMangement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:communityMangement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:communityMangement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="communityMangementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="主键" align="center" prop="id" />-->
      <el-table-column label="小区名字" align="center" prop="communityName" />
      <el-table-column label="物业费单价" align="center" prop="communityPrice" />
      <el-table-column label="三证合一停车费" align="center" prop="ownerParkingFee" />
      <el-table-column label="非三证合一停车费" align="center" prop="tenantParkingFee" />
<!--      <el-table-column label="电梯费" align="center" prop="elevatorFee" />-->
      <el-table-column label="卫生费类型" align="center" prop="sanitationFeeType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.sanitationFeeType === 1 ? 'success' : 'primary'" size="mini">
            {{ scope.row.sanitationFeeType === 1 ? '固定费用' : '阶梯费用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="固定卫生费" align="center" prop="fixedSanitationFee">
        <template slot-scope="scope">
          <span v-if="scope.row.sanitationFeeType === 1">{{ scope.row.fixedSanitationFee || 0 }}</span>
          <span v-else style="color: #C0C4CC;">-</span>
        </template>
      </el-table-column>
      <el-table-column label="阶梯费用明细" align="center" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.sanitationFeeType === 2">
            <el-button
              type="text"
              size="small"
              @click="showTieredFeeDetail(scope.row)"
              style="color: #409EFF;">
              查看明细
            </el-button>
            <div v-if="scope.row.tieredFeeConfig" class="tiered-fee-summary">
              <el-tag
                v-for="(range, index) in getTieredFeeSummary(scope.row.tieredFeeConfig)"
                :key="index"
                size="mini"
                style="margin: 1px;">
                {{ range.name }}: {{ range.fee }}元
              </el-tag>
            </div>
            <span v-else style="color: #F56C6C; font-size: 12px;">未配置</span>
          </div>
          <span v-else style="color: #C0C4CC;">-</span>
        </template>
      </el-table-column>
      <el-table-column label="入园日期" align="center" prop="entryDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.entryDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前账单日期" align="center" prop="statementDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.statementDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:communityMangement:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:communityMangement:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小区管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="小区名字" prop="communityName">
          <el-input v-model="form.communityName" placeholder="请输入小区名字" />
        </el-form-item>
        <el-form-item label="物业费单价" prop="communityPrice">
          <el-input v-model="form.communityPrice" placeholder="平方(月)" type="number" />
        </el-form-item>
        <el-form-item label="三证合一停车费" prop="ownerParkingFee">
          <el-input v-model="form.ownerParkingFee" placeholder="月价格"  type="number"/>
        </el-form-item>
        <el-form-item label="非三证合一停车费" prop="tenantParkingFee">
          <el-input v-model="form.tenantParkingFee" placeholder="月价格" type="number"/>
        </el-form-item>
        <el-form-item label="卫生费类型" prop="sanitationFeeType">
          <el-radio-group v-model="form.sanitationFeeType" @change="handleSanitationFeeTypeChange">
            <el-radio :label="1">固定卫生费</el-radio>
            <el-radio :label="2">阶梯卫生费</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="固定卫生费" prop="fixedSanitationFee" v-show="form.sanitationFeeType === 1">
          <el-input v-model="form.fixedSanitationFee" placeholder="月价格" type="number"/>
        </el-form-item>
        <div v-show="form.sanitationFeeType === 2">
          <el-form-item label="阶梯费用配置">
            <el-button type="primary" size="small" @click="openTieredFeeConfig">配置阶梯费用</el-button>
            <span style="margin-left: 10px; color: #909399; font-size: 12px;">
              点击配置灵活的阶梯费用区间
            </span>
          </el-form-item>
        </div>
        <el-form-item label="入园日期" prop="entryDate">
          <el-date-picker clearable
            v-model="form.entryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择入园日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 阶梯费用配置对话框 -->
    <el-dialog title="阶梯费用配置" :visible.sync="tieredFeeConfigOpen" width="800px" append-to-body>
      <div class="tiered-fee-config">
        <div class="config-header">
          <el-button type="primary" size="small" @click="addTieredRange">添加区间</el-button>
          <el-button type="success" size="small" @click="loadDefaultConfig">加载默认配置</el-button>
          <span style="margin-left: 10px; color: #909399; font-size: 12px;">
            配置不同面积区间的卫生费标准
          </span>
        </div>

        <el-table :data="tieredRanges" border style="margin-top: 15px;">
          <el-table-column label="区间名称" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.name" placeholder="如：0-49㎡" size="small"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="最小面积" width="150">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.minArea" :min="0" :precision="2" size="small" style="width: 100%"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="最大面积" width="150">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.maxArea" :min="0" :precision="2" size="small" style="width: 100%" placeholder="留空表示无上限"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="费用(元/月)" width="150">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.fee" :min="0" :precision="2" size="small" style="width: 100%"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="描述" min-width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.description" placeholder="区间描述" size="small"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="removeTieredRange(scope.$index)" style="color: #f56c6c;">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="config-preview" style="margin-top: 20px;">
          <h4>配置预览</h4>
          <el-table :data="tieredRanges" border size="small">
            <el-table-column label="区间" prop="name" width="100"></el-table-column>
            <el-table-column label="面积范围" width="150">
              <template slot-scope="scope">
                <span>{{ formatRange(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="费用" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.fee || 0 }}元/月</span>
              </template>
            </el-table-column>
            <el-table-column label="描述" prop="description"></el-table-column>
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveTieredFeeConfig">保存配置</el-button>
        <el-button @click="cancelTieredFeeConfig">取消</el-button>
      </div>
    </el-dialog>

    <!-- 阶梯费用明细查看对话框 -->
    <el-dialog title="阶梯费用明细" :visible.sync="tieredFeeDetailOpen" width="800px" append-to-body>
      <div class="tiered-fee-detail">
        <div class="detail-header">
          <h4>{{ currentCommunityName }} - 阶梯费用配置</h4>
        </div>

        <el-table :data="currentTieredRanges" border size="small" style="margin-top: 15px;">
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column label="区间名称" prop="name" width="120" align="center"></el-table-column>
          <el-table-column label="面积范围" width="150" align="center">
            <template slot-scope="scope">
              <span>{{ formatRangeDisplay(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="费用标准" prop="fee" width="100" align="center">
            <template slot-scope="scope">
              <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.fee || 0 }}元/月</span>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="description" min-width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.description || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="detail-summary" style="margin-top: 20px;">
          <div class="summary-item">
            <span class="summary-label">配置区间数：</span>
            <span class="summary-value">{{ currentTieredRanges.length }} 个</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">费用范围：</span>
            <span class="summary-value">{{ getFeeRange() }}</span>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="tieredFeeDetailOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCommunityMangement, getCommunityMangement, delCommunityMangement, addCommunityMangement, updateCommunityMangement } from "@/api/system/communityMangement"

export default {
  name: "CommunityMangement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小区管理表格数据
      communityMangementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityName: null,
        communityPrice: null,
        ownerParkingFee: null,
        tenantParkingFee: null,
        elevatorFee: null,
        sanitationFeeType: null,
        fixedSanitationFee: null,
        tieredFeeConfig: null,
        entryDate: null,
        statementDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 阶梯费用配置相关
      tieredFeeConfigOpen: false,
      tieredRanges: [],
      // 阶梯费用明细查看相关
      tieredFeeDetailOpen: false,
      currentTieredRanges: [],
      currentCommunityName: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询小区管理列表 */
    getList() {
      this.loading = true
      console.log('查询参数:', this.queryParams)
      listCommunityMangement(this.queryParams).then(response => {
        console.log('查询结果:', response)
        console.log('小区数据:', response.rows)
        this.communityMangementList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(error => {
        console.error('查询失败:', error)
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityName: null,
        communityPrice: null,
        ownerParkingFee: null,
        tenantParkingFee: null,
        elevatorFee: null,
        sanitationFeeType: 2,
        fixedSanitationFee: null,
        tieredFeeConfig: null,
        entryDate: null,
        statementDate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
      // 确保响应性
      this.$nextTick(() => {
        this.$forceUpdate()
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['communityName']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加小区管理"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCommunityMangement(id).then(response => {
        this.form = response.data

        console.log('从数据库获取的原始数据:', response.data)

        // 确保新字段存在，但不覆盖数据库中的正确值
        if (this.form.sanitationFeeType === null || this.form.sanitationFeeType === undefined) {
          this.$set(this.form, 'sanitationFeeType', 2) // 默认为阶梯费用
        }

        // 确保字段存在
        if (!this.form.hasOwnProperty('fixedSanitationFee')) {
          this.$set(this.form, 'fixedSanitationFee', null)
        }
        if (!this.form.hasOwnProperty('tieredFeeConfig')) {
          this.$set(this.form, 'tieredFeeConfig', null)
        }

        console.log('处理后的表单数据:', this.form)
        console.log('卫生费类型:', this.form.sanitationFeeType)
        console.log('固定卫生费:', this.form.fixedSanitationFee)
        console.log('阶梯费用配置:', this.form.tieredFeeConfig)

        this.open = true
        this.title = "修改小区管理"
      }).catch(error => {
        console.error('获取小区数据失败:', error)
        this.$modal.msgError('获取小区数据失败')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log('提交前的表单数据:', JSON.parse(JSON.stringify(this.form)))

          // 数据验证
          if (this.form.sanitationFeeType === 1) {
            // 固定费用：验证固定费用金额
            if (this.form.fixedSanitationFee && this.form.fixedSanitationFee < 0) {
              this.$modal.msgError("请输入有效的固定卫生费金额")
              return
            }
            // 清空阶梯费用配置
            this.form.tieredFeeConfig = null
            console.log('固定费用模式，清空阶梯费用配置')
          } else if (this.form.sanitationFeeType === 2) {
            // 阶梯费用：验证JSON配置
            if (!this.form.tieredFeeConfig) {
              this.$modal.msgError("请配置阶梯费用")
              return
            }
            // 清空固定费用
            this.form.fixedSanitationFee = null
            console.log('阶梯费用模式，清空固定费用')
          }

          console.log('提交的最终数据:', JSON.parse(JSON.stringify(this.form)))

          if (this.form.id != null) {
            updateCommunityMangement(this.form).then(response => {
              console.log('更新响应:', response)
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error('更新失败:', error)
            })
          } else {
            addCommunityMangement(this.form).then(response => {
              console.log('新增响应:', response)
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            }).catch(error => {
              console.error('新增失败:', error)
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除小区管理编号为"' + ids + '"的数据项？').then(function() {
        return delCommunityMangement(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/communityMangement/export', {
        ...this.queryParams
      }, `communityMangement_${new Date().getTime()}.xlsx`)
    },
    /** 卫生费类型变化处理 */
    handleSanitationFeeTypeChange(value) {
      if (value === 1) {
        // 选择固定费用时，清空阶梯费用配置
        this.form.tieredFeeConfig = null
      } else if (value === 2) {
        // 选择阶梯费用时，清空固定费用
        this.form.fixedSanitationFee = null
      }
    },

    /** 打开阶梯费用配置 */
    openTieredFeeConfig() {
      this.tieredFeeConfigOpen = true
      this.loadTieredFeeConfig()
    },

    /** 加载阶梯费用配置 */
    loadTieredFeeConfig() {
      if (this.form.tieredFeeConfig) {
        try {
          const config = JSON.parse(this.form.tieredFeeConfig)
          this.tieredRanges = config.ranges || []
        } catch (e) {
          console.error('解析阶梯费用配置失败:', e)
          this.loadDefaultConfig()
        }
      } else {
        this.loadDefaultConfig()
      }
    },

    /** 加载默认配置 */
    loadDefaultConfig() {
      this.tieredRanges = [
        { name: '0-49㎡', minArea: 0, maxArea: 49, fee: 0, description: '0-49平方米' },
        { name: '50-59㎡', minArea: 50, maxArea: 59, fee: 0, description: '50-59平方米' },
        { name: '60-69㎡', minArea: 60, maxArea: 69, fee: 0, description: '60-69平方米' },
        { name: '70-79㎡', minArea: 70, maxArea: 79, fee: 0, description: '70-79平方米' },
        { name: '80-89㎡', minArea: 80, maxArea: 89, fee: 0, description: '80-89平方米' },
        { name: '90㎡以上', minArea: 90, maxArea: 9999, fee: 0, description: '90平方米以上' }
      ]
    },

    /** 添加阶梯区间 */
    addTieredRange() {
      this.tieredRanges.push({
        name: '',
        minArea: null,
        maxArea: null,
        fee: 0,
        description: ''
      })
    },

    /** 删除阶梯区间 */
    removeTieredRange(index) {
      this.tieredRanges.splice(index, 1)
    },

    /** 格式化区间显示 */
    formatRange(row) {
      if (row.minArea == null && row.maxArea == null) {
        return '全部'
      } else if (row.minArea == null) {
        return row.maxArea + '㎡以下'
      } else if (row.maxArea == null) {
        return row.minArea + '㎡以上'
      } else {
        return row.minArea + '-' + row.maxArea + '㎡'
      }
    },

    /** 保存阶梯费用配置 */
    saveTieredFeeConfig() {
      // 验证配置
      for (let i = 0; i < this.tieredRanges.length; i++) {
        const range = this.tieredRanges[i]
        if (!range.name) {
          this.$modal.msgError(`第${i + 1}个区间的名称不能为空`)
          return
        }
        if (range.fee == null || range.fee < 0) {
          this.$modal.msgError(`第${i + 1}个区间的费用不能为空且不能小于0`)
          return
        }
      }

      // 生成JSON配置
      const config = {
        ranges: this.tieredRanges
      }
      this.form.tieredFeeConfig = JSON.stringify(config)
      this.tieredFeeConfigOpen = false
      this.$modal.msgSuccess('阶梯费用配置保存成功')
    },

    /** 取消阶梯费用配置 */
    cancelTieredFeeConfig() {
      this.tieredFeeConfigOpen = false
    },

    /** 获取阶梯费用摘要（用于表格显示） */
    getTieredFeeSummary(tieredFeeConfig) {
      if (!tieredFeeConfig) return []

      try {
        const config = JSON.parse(tieredFeeConfig)
        if (config.ranges && config.ranges.length > 0) {
          // 显示全部区间
          return config.ranges.map(range => ({
            name: range.name,
            fee: range.fee || 0
          }))
        }
      } catch (e) {
        console.error('解析阶梯费用配置失败:', e)
      }

      return []
    },

    /** 显示阶梯费用明细 */
    showTieredFeeDetail(row) {
      this.currentCommunityName = row.communityName
      this.currentTieredRanges = []

      if (row.tieredFeeConfig) {
        try {
          const config = JSON.parse(row.tieredFeeConfig)
          this.currentTieredRanges = config.ranges || []
        } catch (e) {
          console.error('解析阶梯费用配置失败:', e)
          this.$modal.msgError('阶梯费用配置格式错误')
          return
        }
      }

      this.tieredFeeDetailOpen = true
    },

    /** 格式化区间显示（用于明细对话框） */
    formatRangeDisplay(row) {
      if (row.minArea == null && row.maxArea == null) {
        return '全部面积'
      } else if (row.minArea == null) {
        return `${row.maxArea}㎡以下`
      } else if (row.maxArea == null) {
        return `${row.minArea}㎡以上`
      } else {
        return `${row.minArea}-${row.maxArea}㎡`
      }
    },

    /** 获取费用范围 */
    getFeeRange() {
      if (this.currentTieredRanges.length === 0) {
        return '未配置'
      }

      const fees = this.currentTieredRanges.map(range => parseFloat(range.fee || 0))
      const minFee = Math.min(...fees)
      const maxFee = Math.max(...fees)

      if (minFee === maxFee) {
        return `${minFee}元/月`
      } else {
        return `${minFee}-${maxFee}元/月`
      }
    }
  }
}
</script>

<style scoped>
/* 阶梯费用摘要样式 */
.tiered-fee-summary {
  margin-top: 5px;
  max-width: 200px;
}

.tiered-fee-summary .el-tag {
  margin: 1px;
  font-size: 11px;
}

/* 阶梯费用明细对话框样式 */
.tiered-fee-detail .detail-header h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.detail-summary {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.summary-label {
  color: #606266;
  font-size: 14px;
}

.summary-value {
  color: #303133;
  font-weight: 500;
  font-size: 14px;
}

/* 表格中的阶梯费用列样式 */
.el-table .tiered-fee-summary {
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-summary {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .summary-item {
    width: 100%;
    justify-content: space-between;
  }

  .tiered-fee-summary {
    max-width: 150px;
  }
}
</style>
