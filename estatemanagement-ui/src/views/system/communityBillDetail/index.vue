<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账单ID" prop="billId">
        <el-input
          v-model="queryParams.billId"
          placeholder="请输入账单ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用名称" prop="feeName">
        <el-input
          v-model="queryParams.feeName"
          placeholder="请输入费用名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计费基数(面积、车位数等)" prop="baseAmount">
        <el-input
          v-model="queryParams.baseAmount"
          placeholder="请输入计费基数(面积、车位数等)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单价" prop="unitPrice">
        <el-input
          v-model="queryParams.unitPrice"
          placeholder="请输入单价"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计费天数" prop="billingDays">
        <el-input
          v-model="queryParams.billingDays"
          placeholder="请输入计费天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用金额" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入费用金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号(停车费时使用)" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号(停车费时使用)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车位号(停车费时使用)" prop="spaceNumber">
        <el-input
          v-model="queryParams.spaceNumber"
          placeholder="请输入车位号(停车费时使用)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:communityBillDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:communityBillDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:communityBillDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:communityBillDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="communityBillDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="账单ID" align="center" prop="billId" />
      <el-table-column label="费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)" align="center" prop="feeType" />
      <el-table-column label="费用名称" align="center" prop="feeName" />
      <el-table-column label="计费基数(面积、车位数等)" align="center" prop="baseAmount" />
      <el-table-column label="单价" align="center" prop="unitPrice" />
      <el-table-column label="计费天数" align="center" prop="billingDays" />
      <el-table-column label="费用金额" align="center" prop="amount" />
      <el-table-column label="车牌号(停车费时使用)" align="center" prop="plateNumber" />
      <el-table-column label="车位号(停车费时使用)" align="center" prop="spaceNumber" />
      <el-table-column label="计算公式说明" align="center" prop="calculationFormula" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:communityBillDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:communityBillDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改小区账单明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账单ID" prop="billId">
          <el-input v-model="form.billId" placeholder="请输入账单ID" />
        </el-form-item>
        <el-form-item label="费用名称" prop="feeName">
          <el-input v-model="form.feeName" placeholder="请输入费用名称" />
        </el-form-item>
        <el-form-item label="计费基数(面积、车位数等)" prop="baseAmount">
          <el-input v-model="form.baseAmount" placeholder="请输入计费基数(面积、车位数等)" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input v-model="form.unitPrice" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="计费天数" prop="billingDays">
          <el-input v-model="form.billingDays" placeholder="请输入计费天数" />
        </el-form-item>
        <el-form-item label="费用金额" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入费用金额" />
        </el-form-item>
        <el-form-item label="车牌号(停车费时使用)" prop="plateNumber">
          <el-input v-model="form.plateNumber" placeholder="请输入车牌号(停车费时使用)" />
        </el-form-item>
        <el-form-item label="车位号(停车费时使用)" prop="spaceNumber">
          <el-input v-model="form.spaceNumber" placeholder="请输入车位号(停车费时使用)" />
        </el-form-item>
        <el-form-item label="计算公式说明" prop="calculationFormula">
          <el-input v-model="form.calculationFormula" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCommunityBillDetail, getCommunityBillDetail, delCommunityBillDetail, addCommunityBillDetail, updateCommunityBillDetail } from "@/api/system/communityBillDetail"

export default {
  name: "CommunityBillDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 小区账单明细表格数据
      communityBillDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        billId: null,
        feeType: null,
        feeName: null,
        baseAmount: null,
        unitPrice: null,
        billingDays: null,
        amount: null,
        plateNumber: null,
        spaceNumber: null,
        calculationFormula: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        billId: [
          { required: true, message: "账单ID不能为空", trigger: "blur" }
        ],
        feeType: [
          { required: true, message: "费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)不能为空", trigger: "change" }
        ],
        feeName: [
          { required: true, message: "费用名称不能为空", trigger: "blur" }
        ],
        amount: [
          { required: true, message: "费用金额不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询小区账单明细列表 */
    getList() {
      this.loading = true
      listCommunityBillDetail(this.queryParams).then(response => {
        this.communityBillDetailList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        billId: null,
        feeType: null,
        feeName: null,
        baseAmount: null,
        unitPrice: null,
        billingDays: null,
        amount: null,
        plateNumber: null,
        spaceNumber: null,
        calculationFormula: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加小区账单明细"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCommunityBillDetail(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改小区账单明细"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCommunityBillDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addCommunityBillDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除小区账单明细编号为"' + ids + '"的数据项？').then(function() {
        return delCommunityBillDetail(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/communityBillDetail/export', {
        ...this.queryParams
      }, `communityBillDetail_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
