<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>退费申请</span>
      </div>

      <!-- 选择收费记录 -->
      <el-form :model="searchForm" ref="searchForm" size="small" :inline="true" label-width="100px">
        <el-form-item label="小区" prop="communityId">
          <el-select
            v-model="searchForm.communityId"
            placeholder="请选择小区"
            clearable
            filterable
            @change="handleCommunityChange"
            style="width: 200px"
          >
            <el-option
              v-for="community in communityList"
              :key="community.id"
              :label="community.communityName"
              :value="community.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="业主" prop="ownerId">
          <el-select
            v-model="searchForm.ownerId"
            placeholder="请选择业主"
            clearable
            filterable
            :disabled="!searchForm.communityId"
            style="width: 200px"
          >
            <el-option
              v-for="owner in ownerList"
              :key="owner.id"
              :label="`${owner.ownerName} (${owner.houseNumber})`"
              :value="owner.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收据号" prop="receiptNumber">
          <el-input
            v-model="searchForm.receiptNumber"
            placeholder="请输入收据号"
            clearable
            @keyup.enter.native="searchPaymentRecord"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="searchPaymentRecord">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 收费记录列表 -->
      <el-table v-loading="paymentLoading" :data="paymentRecordList" @selection-change="handlePaymentSelection" v-if="showPaymentList">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="收据号" align="center" prop="receiptNumber" width="180" />
        <el-table-column label="业主信息" align="center" width="200">
          <template slot-scope="scope">
            <div>{{ scope.row.ownerName }}</div>
            <div style="color: #999; font-size: 12px;">{{ scope.row.houseNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column label="费用类型" align="center" width="250">
          <template slot-scope="scope">
            <div class="fee-types">
              <el-tag v-if="scope.row.propertyFeeAmount > 0" type="primary" size="mini" style="margin: 2px;">
                物业费: {{ scope.row.propertyFeeAmount }}
              </el-tag>
              <el-tag v-if="scope.row.parkingFeeAmount > 0" type="success" size="mini" style="margin: 2px;">
                停车费: {{ scope.row.parkingFeeAmount }}
              </el-tag>
              <el-tag v-if="scope.row.sanitationFeeAmount > 0" type="warning" size="mini" style="margin: 2px;">
                卫生费: {{ scope.row.sanitationFeeAmount }}
              </el-tag>
              <el-tag v-if="scope.row.elevatorFeeAmount > 0" type="info" size="mini" style="margin: 2px;">
                电梯费: {{ scope.row.elevatorFeeAmount }}
              </el-tag>
              <el-tag v-if="scope.row.lateFeeAmount > 0" type="danger" size="mini" style="margin: 2px;">
                滞纳金: {{ scope.row.lateFeeAmount }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="收费金额" align="center" prop="paymentAmount" width="120">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: bold;">{{ scope.row.paymentAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收费日期" align="center" prop="paymentDate" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.paymentDate }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-money"
              @click="selectPaymentRecord(scope.row)"
            >申请退费</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 退费申请表单 -->
    <el-card class="box-card" v-if="selectedPayment">
      <div slot="header" class="clearfix">
        <span>退费申请表单</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="cancelRefund">取消</el-button>
      </div>

      <!-- 原收费记录信息 -->
      <el-descriptions title="原收费记录信息" :column="3" border>
        <el-descriptions-item label="收据号">{{ selectedPayment.receiptNumber }}</el-descriptions-item>
        <el-descriptions-item label="业主姓名">{{ selectedPayment.ownerName }}</el-descriptions-item>
        <el-descriptions-item label="房号">{{ selectedPayment.houseNumber }}</el-descriptions-item>
        <el-descriptions-item label="收费金额">
          <span style="color: #67C23A; font-weight: bold;">{{ selectedPayment.paymentAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="收费日期">{{ selectedPayment.paymentDate}}</el-descriptions-item>
        <el-descriptions-item label="收费方式">{{ selectedPayment.paymentMethod }}</el-descriptions-item>
      </el-descriptions>

      <!-- 退费信息 -->
      <el-divider content-position="left">退费信息</el-divider>
      <el-form ref="refundForm" :model="refundForm" :rules="refundRules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="退费类型" prop="refundType">
              <el-radio-group v-model="refundForm.refundType" @change="handleRefundTypeChange">
                <el-radio :label="1">全额退费</el-radio>
                <el-radio :label="2">部分退费</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退费方式" prop="refundMethod">
              <el-select v-model="refundForm.refundMethod" placeholder="请选择退费方式">
                <el-option label="现金" value="cash"></el-option>
                <el-option label="银行转账" value="bank_transfer"></el-option>
                <el-option label="微信" value="wechat"></el-option>
                <el-option label="支付宝" value="alipay"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="退费原因" prop="refundReason">
              <el-input v-model="refundForm.refundReason" type="textarea" placeholder="请输入退费原因" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 收费明细和退费金额 -->
      <el-divider content-position="left">收费明细</el-divider>
      <el-table :data="paymentDetails" border v-if="paymentDetails.length > 0">
        <el-table-column label="费用类型" align="center" width="120">
          <template slot-scope="scope">
            <el-tag :type="getFeeTypeTagType(scope.row.fee_type)" size="small">
              {{ getFeeTypeName(scope.row.fee_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="费用名称" align="center" prop="fee_type_name" width="120" />
        <el-table-column label="原支付金额" align="center" prop="payment_amount" width="120">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: bold;">{{ scope.row.payment_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付月数" align="center" prop="payment_months" width="100" />
        <el-table-column label="缴费周期" align="center" width="200">
          <template slot-scope="scope">
            <span v-if="scope.row.period_start && scope.row.period_end">
              {{ parseTime(scope.row.period_start, '{y}-{m}-{d}') }} 至 {{ parseTime(scope.row.period_end, '{y}-{m}-{d}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="可退费金额" align="center" width="120">
          <template slot-scope="scope">
            <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.calculatedRefundAmount || scope.row.payment_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="退费金额" align="center" width="180">
          <template slot-scope="scope">
            <div v-if="refundForm.refundType === 1">
              <!-- 全额退费显示可退费金额 -->
              <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.calculatedRefundAmount || scope.row.payment_amount }}</span>
            </div>
            <div v-else>
              <!-- 部分退费可手动输入 -->
              <el-input-number
                v-model="scope.row.refundAmount"
                :min="0"
                :max="parseFloat(scope.row.calculatedRefundAmount || scope.row.payment_amount)"
                :precision="2"
                size="small"
                @change="calculateTotalRefundAmount"
                style="width: 150px"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="车牌号" align="center" prop="plate_number" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.fee_type === 2">{{ scope.row.plate_number || '-' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 退费金额汇总 -->
      <el-row style="margin-top: 20px;">
        <el-col :span="24">
          <div style="text-align: right; font-size: 18px;">
            <span>总退费金额：</span>
            <span style="color: #E6A23C; font-weight: bold;">{{ totalRefundAmount }} 元</span>
          </div>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-row style="margin-top: 30px;">
        <el-col :span="24" style="text-align: center;">
          <el-button type="primary" @click="submitRefund" :loading="submitting">提交退费申请</el-button>
          <el-button @click="cancelRefund">取消</el-button>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { listPaymentRecord, getPaymentRecordWithDetails } from "@/api/system/paymentRecord";
import { processRefund, calculateRefundAmount, getPaymentRefundInfo } from "@/api/system/refundRecord";
import { listCommunityMangement } from "@/api/system/communityMangement";
import { listOwnerMangement } from "@/api/system/ownerMangement";

export default {
  name: "RefundApply",
  data() {
    return {
      // 费用类型选项
      feeTypeOptions: [
        { label: '物业费', value: 1 },
        { label: '停车费', value: 2 },
        { label: '卫生费', value: 3 },
        { label: '电梯费', value: 4 },
        { label: '滞纳金', value: 5 }
      ],
      // 搜索表单
      searchForm: {
        communityId: null,
        ownerId: null,
        receiptNumber: ''
      },
      // 小区列表
      communityList: [],
      // 业主列表
      ownerList: [],
      // 收费记录列表
      paymentRecordList: [],
      paymentLoading: false,
      showPaymentList: false,
      // 选中的收费记录
      selectedPayment: null,
      // 收费明细
      paymentDetails: [],
      // 退费表单
      refundForm: {
        refundType: 1,
        refundMethod: '',
        refundReason: ''
      },
      // 总退费金额
      totalRefundAmount: 0,
      // 提交状态
      submitting: false,
      // 表单校验
      refundRules: {
        refundType: [
          { required: true, message: "退费类型不能为空", trigger: "change" }
        ],
        refundMethod: [
          { required: true, message: "退费方式不能为空", trigger: "change" }
        ],
        refundReason: [
          { required: true, message: "退费原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.loadCommunityList();
  },
  methods: {
    /** 加载小区列表 */
    loadCommunityList() {
      listCommunityMangement({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.communityList = response.rows || [];
      });
    },

    /** 加载业主列表 */
    loadOwnerList(communityId) {
      if (!communityId) {
        this.ownerList = [];
        return;
      }

      const queryParams = {
        communityId: communityId,
        pageNum: 1,
        pageSize: 1000
      };

      listOwnerMangement(queryParams).then(response => {
        this.ownerList = response.rows || [];
      });
    },

    /** 小区改变事件 */
    handleCommunityChange(communityId) {
      this.searchForm.ownerId = null;
      this.ownerList = [];
      if (communityId) {
        this.loadOwnerList(communityId);
      }
    },

    /** 搜索收费记录 */
    searchPaymentRecord() {
      // 验证必填项
      if (!this.searchForm.communityId) {
        this.$modal.msgWarning("请先选择小区");
        return;
      }

      this.paymentLoading = true;
      const queryParams = {
        communityId: this.searchForm.communityId,
        ownerId: this.searchForm.ownerId,
        receiptNumber: this.searchForm.receiptNumber,
        pageNum: 1,
        pageSize: 50
      };

      listPaymentRecord(queryParams).then(response => {
        this.paymentRecordList = response.rows || [];
        this.showPaymentList = true;
        this.paymentLoading = false;

        if (this.paymentRecordList.length === 0) {
          this.$modal.msgWarning("未找到符合条件的收费记录");
        }
      }).catch(() => {
        this.paymentLoading = false;
      });
    },

    /** 重置搜索 */
    resetSearch() {
      this.searchForm = {
        communityId: null,
        ownerId: null,
        receiptNumber: ''
      };
      this.ownerList = [];
      this.paymentRecordList = [];
      this.showPaymentList = false;
      this.selectedPayment = null;
      this.paymentDetails = [];
    },

    /** 选择收费记录 */
    selectPaymentRecord(row) {
      this.selectedPayment = row;
      this.loadPaymentDetails(row.id);
      this.loadRefundInfo(row.id);
    },

    /** 加载收费明细 */
    loadPaymentDetails(paymentId) {
      getPaymentRecordWithDetails(paymentId).then(response => {
        console.log('收费明细响应数据:', response.data);
        // 根据后端返回的数据结构，明细在 details 字段中
        this.paymentDetails = response.data.details || [];
        console.log('加载的收费明细:', this.paymentDetails);

        if (this.paymentDetails.length === 0) {
          this.$modal.msgWarning('该收费记录没有明细数据');
          return;
        }

        this.calculateRefundableAmount();
      }).catch(error => {
        console.error('加载收费明细失败:', error);
        this.$modal.msgError('加载收费明细失败');
      });
    },

    /** 加载退费信息 */
    loadRefundInfo(paymentId) {
      getPaymentRefundInfo(paymentId).then(response => {
        const refundInfo = response.data;
        if (!refundInfo.canRefund) {
          this.$modal.msgWarning("该收费记录不可退费或已全额退费");
          return;
        }
        // 处理退费信息
      });
    },

    /** 计算可退费金额 */
    calculateRefundableAmount() {
      console.log('开始计算可退费金额，收费明细数量:', this.paymentDetails.length);

      this.paymentDetails.forEach((detail, index) => {
        console.log(`处理明细 ${index}:`, detail);

        // 使用后端返回的字段名（下划线格式）
        const periodStart = detail.period_start;
        const periodEnd = detail.period_end;
        const paymentAmount = parseFloat(detail.payment_amount || 0);

        // 计算每个明细的可退费金额
        if (periodStart && periodEnd) {
          const now = new Date();
          const endDate = new Date(periodEnd);
          const startDate = new Date(periodStart);

          if (endDate > now) {
            // 计算剩余天数比例
            const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
            const remainingDays = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
            if (remainingDays > 0 && totalDays > 0) {
              const refundRatio = remainingDays / totalDays;
              detail.calculatedRefundAmount = (paymentAmount * refundRatio).toFixed(2);
            } else {
              detail.calculatedRefundAmount = "0.00";
            }
          } else {
            detail.calculatedRefundAmount = "0.00";
          }
        } else {
          // 如果没有缴费周期，默认可全额退费
          detail.calculatedRefundAmount = paymentAmount.toFixed(2);
        }

        // 统一字段名，方便模板使用
        detail.paymentAmount = paymentAmount;
        detail.periodStart = periodStart;
        detail.periodEnd = periodEnd;
        detail.feeType = detail.fee_type;
        detail.feeName = detail.fee_type_name;
        detail.paymentMonths = detail.payment_months;
        detail.plateNumber = detail.plate_number;

        // 初始化退费金额
        detail.refundAmount = parseFloat(detail.calculatedRefundAmount || paymentAmount);

        console.log(`明细 ${index} 计算结果:`, {
          paymentAmount: detail.paymentAmount,
          calculatedRefundAmount: detail.calculatedRefundAmount,
          refundAmount: detail.refundAmount,
          periodStart: detail.periodStart,
          periodEnd: detail.periodEnd
        });
      });

      this.calculateTotalRefundAmount();
    },

    /** 退费类型改变 */
    handleRefundTypeChange() {
      // 重新设置退费金额
      this.paymentDetails.forEach(detail => {
        if (this.refundForm.refundType === 1) {
          // 全额退费：使用可退费金额
          detail.refundAmount = parseFloat(detail.calculatedRefundAmount || detail.payment_amount);
        } else {
          // 部分退费：重置为0，让用户手动输入
          detail.refundAmount = 0;
        }
      });

      this.calculateTotalRefundAmount();
    },

    /** 计算总退费金额 */
    calculateTotalRefundAmount() {
      console.log('计算总退费金额，退费类型:', this.refundForm.refundType);
      console.log('收费明细:', this.paymentDetails);

      let total = 0;

      if (this.refundForm.refundType === 1) {
        // 全额退费：使用可退费金额
        this.paymentDetails.forEach(detail => {
          const amount = parseFloat(detail.calculatedRefundAmount || detail.payment_amount || 0);
          console.log('全额退费明细金额:', amount, detail);
          total += amount;
        });
      } else {
        // 部分退费：使用用户输入的退费金额
        this.paymentDetails.forEach(detail => {
          const amount = parseFloat(detail.refundAmount || 0);
          console.log('部分退费明细金额:', amount, detail);
          total += amount;
        });
      }

      this.totalRefundAmount = total.toFixed(2);
      console.log('计算得出的总退费金额:', this.totalRefundAmount);
    },

    /** 提交退费申请 */
    submitRefund() {
      this.$refs["refundForm"].validate(valid => {
        if (valid) {
          if (this.totalRefundAmount <= 0) {
            this.$modal.msgWarning("退费金额必须大于0");
            return;
          }

          this.submitting = true;

          // 验证部分退费时的金额
          if (this.refundForm.refundType === 2) {
            for (let detail of this.paymentDetails) {
              if (detail.refundAmount > parseFloat(detail.calculatedRefundAmount || detail.payment_amount)) {
                this.$modal.msgWarning(`${detail.feeName || '费用'}的退费金额不能超过可退费金额`);
                this.submitting = false;
                return;
              }
            }
          }

          const refundData = {

            originalPaymentId: this.selectedPayment.id, // 原收费记录ID
            refundType: this.refundForm.refundType, // 退费类型 全部 部分
            refundReason: this.refundForm.refundReason,
            refundMethod: this.refundForm.refundMethod, // 退费方式 现金 银行转账
            operatorId: this.$store.state.user.id,
            operatorName: this.$store.state.user.name,
            totalRefundAmount: this.totalRefundAmount,
            refundItems: this.paymentDetails.map(detail => ({
              detailId: detail.id,
              refundAmount: this.refundForm.refundType === 1
                ? (detail.calculatedRefundAmount || detail.payment_amount)
                : detail.refundAmount
            })).filter(item => item.refundAmount > 0) // 只包含有退费金额的项目
          };

          processRefund(refundData).then(response => {
            this.$modal.msgSuccess("退费申请提交成功");
            this.resetForm();
            this.submitting = false;
          }).catch(() => {
            this.submitting = false;
          });
        }
      });
    },

    /** 取消退费 */
    cancelRefund() {
      this.selectedPayment = null;
      this.paymentDetails = [];
      this.refundForm = {
        refundType: 1,
        refundMethod: '',
        refundReason: ''
      };
      this.totalRefundAmount = 0;
    },

    /** 重置表单 */
    resetForm() {
      this.resetSearch();
      this.cancelRefund();
      // 重新加载小区列表
      this.loadCommunityList();
    },

    /** 收费记录选择 */
    handlePaymentSelection(selection) {
      // 处理多选逻辑（如果需要）
    },

    /** 获取费用类型名称 */
    getFeeTypeName(feeType) {
      const feeTypeMap = {
        1: '物业费',
        2: '停车费',
        3: '卫生费',
        4: '电梯费',
        5: '滞纳金'
      };
      return feeTypeMap[feeType] || '未知';
    },

    /** 获取费用类型标签类型 */
    getFeeTypeTagType(feeType) {
      const tagTypeMap = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info',
        5: 'danger'
      };
      return tagTypeMap[feeType] || 'info';
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.fee-types {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.fee-types .el-tag {
  margin: 2px;
  font-size: 12px;
}
</style>
