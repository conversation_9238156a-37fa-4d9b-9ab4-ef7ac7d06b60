<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="退费单号" prop="refundNumber">
        <el-input
          v-model="queryParams.refundNumber"
          placeholder="请输入退费单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="原收据号" prop="originalReceiptNumber">
        <el-input
          v-model="queryParams.originalReceiptNumber"
          placeholder="请输入原收据号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="退费类型" prop="refundType">
        <el-select v-model="queryParams.refundType" placeholder="请选择退费类型" clearable>
          <el-option label="全额退费" value="1"></el-option>
          <el-option label="部分退费" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="退费方式" prop="refundMethod">
        <el-select v-model="queryParams.refundMethod" placeholder="请选择退费方式" clearable>
          <el-option label="现金" value="cash"></el-option>
          <el-option label="银行转账" value="bank_transfer"></el-option>
          <el-option label="微信" value="wechat"></el-option>
          <el-option label="支付宝" value="alipay"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="待审核" value="0"></el-option>
          <el-option label="已完成" value="1"></el-option>
          <el-option label="已取消" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="退费日期">
        <el-date-picker
          v-model="daterangeRefundDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:refundRecord:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:refundRecord:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:refundRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:refundRecord:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStatistics"
          v-hasPermi="['system:refundRecord:query']"
        >统计</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="refundRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退费单号" align="center" prop="refund_number" width="180" />
      <el-table-column label="原收据号" align="center" prop="original_receipt_number" width="180" />
      <el-table-column label="业主信息" align="center" width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.owner_name }}</div>
          <div style="color: #999; font-size: 12px;">{{ scope.row.house_number }}</div>
        </template>
      </el-table-column>
      <el-table-column label="小区名称" align="center" prop="community_name" width="150" />
      <el-table-column label="退费类型" align="center" prop="refund_type" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.refund_type" :value="scope.row.refund_type"/>
        </template>
      </el-table-column>
      <el-table-column label="退费金额" align="center" prop="total_refund_amount" width="120">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">{{ scope.row.total_refund_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退费方式" align="center" prop="refund_method" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.refund_method" :value="scope.row.refund_method"/>
        </template>
      </el-table-column>
      <el-table-column label="退费日期" align="center" prop="refund_date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.refund_date, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作员" align="center" prop="operator_name" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.refund_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="退费原因" align="center" prop="refund_reason" width="200" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:refundRecord:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:refundRecord:edit']"
            v-if="scope.row.status == 0"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleAudit(scope.row)"
            v-hasPermi="['system:refundRecord:audit']"
            v-if="scope.row.status == 0"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:refundRecord:remove']"
            v-if="scope.row.status == 0"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改退费记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="退费单号" prop="refundNumber">
              <el-input v-model="form.refundNumber" placeholder="请输入退费单号" :disabled="form.id != null" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原收据号" prop="originalReceiptNumber">
              <el-input v-model="form.originalReceiptNumber" placeholder="请输入原收据号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="退费类型" prop="refundType">
              <el-select v-model="form.refundType" placeholder="请选择退费类型">
                <el-option label="全额退费" :value="1"></el-option>
                <el-option label="部分退费" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退费方式" prop="refundMethod">
              <el-select v-model="form.refundMethod" placeholder="请选择退费方式">
                <el-option label="现金" value="cash"></el-option>
                <el-option label="银行转账" value="bank_transfer"></el-option>
                <el-option label="微信" value="wechat"></el-option>
                <el-option label="支付宝" value="alipay"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="退费原因" prop="refundReason">
              <el-input v-model="form.refundReason" type="textarea" placeholder="请输入退费原因" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看退费记录详情对话框 -->
    <el-dialog title="退费记录详情" :visible.sync="viewOpen" width="1200px" append-to-body>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="退费单号">{{ viewData.refundNumber }}</el-descriptions-item>
        <el-descriptions-item label="原收据号">{{ viewData.originalReceiptNumber }}</el-descriptions-item>
        <el-descriptions-item label="退费类型">
          <dict-tag :options="dict.type.refund_type" :value="viewData.refundType"/>
        </el-descriptions-item>
        <el-descriptions-item label="退费金额">
          <span style="color: #E6A23C; font-weight: bold;">{{ viewData.totalRefundAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="退费方式">
          <dict-tag :options="dict.type.refund_method" :value="viewData.refundMethod"/>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.refund_status" :value="viewData.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="退费日期">{{ parseTime(viewData.refundDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="操作员">{{ viewData.operatorName }}</el-descriptions-item>
        <el-descriptions-item label="审核人">{{ viewData.auditUserName }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">退费原因</el-divider>
      <p>{{ viewData.refundReason }}</p>

      <el-divider content-position="left">退费明细</el-divider>
      <el-table :data="viewData.refundDetailList" border>
        <el-table-column label="费用类型" align="center" prop="feeType">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.feeType === 1" type="primary">物业费</el-tag>
            <el-tag v-else-if="scope.row.feeType === 2" type="success">停车费</el-tag>
            <el-tag v-else-if="scope.row.feeType === 3" type="warning">卫生费</el-tag>
            <el-tag v-else-if="scope.row.feeType === 4" type="info">电梯费</el-tag>
            <el-tag v-else type="">混合支付</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="费用名称" align="center" prop="feeName" />
        <el-table-column label="原支付金额" align="center" prop="originalPaymentAmount" />
        <el-table-column label="退费金额" align="center" prop="refundAmount" />
        <el-table-column label="退费月数" align="center" prop="refundMonths" />
        <el-table-column label="退费周期" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.refundPeriodStart && scope.row.refundPeriodEnd">
              {{ parseTime(scope.row.refundPeriodStart, '{y}-{m}-{d}') }} 至 {{ parseTime(scope.row.refundPeriodEnd, '{y}-{m}-{d}') }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="退费审核" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="auditRemark">
          <el-input v-model="auditForm.auditRemark" type="textarea" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 统计对话框 -->
    <el-dialog title="退费统计" :visible.sync="statisticsOpen" width="800px" append-to-body>
      <el-form :model="statisticsParams" ref="statisticsForm" size="small" :inline="true" label-width="80px">
        <el-form-item label="统计日期">
          <el-date-picker
            v-model="statisticsDateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="getStatistics">查询</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="20" v-if="statisticsData">
        <el-col :span="8">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>退费总笔数</span>
            </div>
            <div class="text item">
              <span style="font-size: 24px; color: #409EFF;">{{ statisticsData.total_count || 0 }}</span> 笔
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>退费总金额</span>
            </div>
            <div class="text item">
              <span style="font-size: 24px; color: #E6A23C;">{{ statisticsData.total_refund_amount || 0 }}</span> 元
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>物业费退费</span>
            </div>
            <div class="text item">
              <span style="font-size: 24px; color: #67C23A;">{{ statisticsData.total_property_fee_amount || 0 }}</span> 元
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listRefundRecord, getRefundRecord, delRefundRecord, addRefundRecord, updateRefundRecord, auditRefund, getRefundStatistics } from "@/api/system/refundRecord";

export default {
  name: "RefundRecord",
  dicts: ['refund_type', 'refund_method', 'refund_status', 'fee_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 退费记录表格数据
      refundRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      viewOpen: false,
      // 审核弹出层
      auditOpen: false,
      // 统计弹出层
      statisticsOpen: false,
      // 退费日期范围
      daterangeRefundDate: [],
      // 统计日期范围
      statisticsDateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        refundNumber: null,
        originalReceiptNumber: null,
        refundType: null,
        refundMethod: null,
        status: null,
        operatorName: null,
      },
      // 表单参数
      form: {},
      // 查看数据
      viewData: {},
      // 审核表单
      auditForm: {},
      // 统计参数
      statisticsParams: {},
      // 统计数据
      statisticsData: null,
      // 表单校验
      rules: {
        refundNumber: [
          { required: true, message: "退费单号不能为空", trigger: "blur" }
        ],
        refundType: [
          { required: true, message: "退费类型不能为空", trigger: "change" }
        ],
        refundMethod: [
          { required: true, message: "退费方式不能为空", trigger: "change" }
        ],
        refundReason: [
          { required: true, message: "退费原因不能为空", trigger: "blur" }
        ]
      },
      // 审核表单校验
      auditRules: {
        status: [
          { required: true, message: "审核结果不能为空", trigger: "change" }
        ],
        auditRemark: [
          { required: true, message: "审核备注不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询退费记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeRefundDate && '' != this.daterangeRefundDate) {
        this.queryParams.params["beginRefundDate"] = this.daterangeRefundDate[0];
        this.queryParams.params["endRefundDate"] = this.daterangeRefundDate[1];
      }
      listRefundRecord(this.queryParams).then(response => {
        this.refundRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        refundNumber: null,
        originalPaymentId: null,
        originalReceiptNumber: null,
        communityId: null,
        ownerId: null,
        refundType: null,
        refundReason: null,
        totalRefundAmount: null,
        propertyFeeAmount: null,
        parkingFeeAmount: null,
        sanitationFeeAmount: null,
        elevatorFeeAmount: null,
        lateFeeAmount: null,
        refundMethod: null,
        refundDate: null,
        operatorId: null,
        operatorName: null,
        status: null,
        auditUserId: null,
        auditUserName: null,
        auditTime: null,
        auditRemark: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeRefundDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加退费记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getRefundRecord(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改退费记录";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id;
      getRefundRecord(id).then(response => {
        this.viewData = response.data;
        this.viewOpen = true;
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: null,
        auditRemark: '',
        auditUserId: this.$store.state.user.id,
        auditUserName: this.$store.state.user.name
      };
      this.auditOpen = true;
    },
    /** 提交审核 */
    submitAudit() {
      this.$refs["auditForm"].validate(valid => {
        if (valid) {
          auditRefund(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 统计按钮操作 */
    handleStatistics() {
      this.statisticsOpen = true;
      this.getStatistics();
    },
    /** 获取统计数据 */
    getStatistics() {
      const params = {};
      if (this.statisticsDateRange && this.statisticsDateRange.length === 2) {
        params.beginDate = this.statisticsDateRange[0];
        params.endDate = this.statisticsDateRange[1];
      }
      getRefundStatistics(params).then(response => {
        this.statisticsData = response.data;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRefundRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRefundRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除退费记录编号为"' + ids + '"的数据项？').then(function() {
        return delRefundRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/refundRecord/export', {
        ...this.queryParams
      }, `refundRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}
.item {
  padding: 18px 0;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}
</style>
