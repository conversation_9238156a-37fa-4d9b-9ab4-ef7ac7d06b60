<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <CommunitySelect
          v-model="queryParams.communityId"
          @change="handleQueryCommunityChange"
          @clear="handleCommunityClear"
        />
      </el-form-item>
      <el-form-item label="业主" prop="ownerId">
        <OwnerSelect
          v-model="queryParams.ownerId"
          :community-id="queryParams.communityId"
          placeholder="请选择业主"
          :disabled="!queryParams.communityId"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="requestBy">
        <el-input
          v-model="queryParams.requestBy"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('requestBy')"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="requestTime">
        <el-date-picker clearable
          v-model="queryParams.requestTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择申请时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="申请类型" prop="requestType">
        <el-select v-model="queryParams.requestType" placeholder="请选择申请类型" clearable>
          <el-option label="信息修改" value="信息修改"></el-option>
          <el-option label="房屋过户" value="房屋过户"></el-option>
          <el-option label="租赁变更" value="租赁变更"></el-option>
          <el-option label="其他" value="其他"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择处理状态" clearable>
          <el-option label="待处理" value="待处理"></el-option>
          <el-option label="处理中" value="处理中"></el-option>
          <el-option label="已完成" value="已完成"></el-option>
          <el-option label="已拒绝" value="已拒绝"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="processBy">
        <el-input
          v-model="queryParams.processBy"
          placeholder="请输入处理人"
          clearable
          @keyup.enter.native="handleQuery"
          @blur="handleInputTrim('processBy')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:modificationRequest:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:modificationRequest:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:modificationRequest:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:modificationRequest:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="modificationRequestList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="小区名称" align="center" prop="communityName" min-width="120" />
      <el-table-column label="业主信息" align="center" min-width="150">
        <template slot-scope="scope">
          <span>{{ getOwnerDisplayText(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请类型" align="center" prop="requestType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.modification_request_type" :value="scope.row.requestType"/>
        </template>
      </el-table-column>
      <el-table-column label="申请内容" align="center" prop="requestContent" min-width="200" show-overflow-tooltip />
      <el-table-column label="申请人" align="center" prop="requestBy" width="100" />
      <el-table-column label="申请时间" align="center" prop="requestTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.modification_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="processBy" width="100" />
      <el-table-column label="处理时间" align="center" prop="processTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.processTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理结果" align="center" prop="processResult" min-width="150" show-overflow-tooltip />
      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:modificationRequest:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:modificationRequest:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改修改申请工单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="小区" prop="communityId">
              <CommunitySelect
                v-model="form.communityId"
                @change="handleFormCommunityChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业主" prop="ownerId">
              <OwnerSelect
                v-model="form.ownerId"
                :community-id="form.communityId"
                placeholder="请选择业主"
                :disabled="!form.communityId"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="申请类型" prop="requestType">
          <el-radio-group v-model="form.requestType">
            <el-radio
              v-for="dict in dict.type.modification_request_type"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="申请内容">
          <editor v-model="form.requestContent" :min-height="150"/>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请人" prop="requestBy">
              <el-input
                v-model="form.requestBy"
                placeholder="请输入申请人"
                @blur="handleFormInputTrim('requestBy')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请时间" prop="requestTime">
              <el-date-picker clearable
                v-model="form.requestTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择申请时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="处理状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.modification_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处理人" prop="processBy">
              <el-input
                v-model="form.processBy"
                placeholder="请输入处理人"
                @blur="handleFormInputTrim('processBy')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理时间" prop="processTime">
              <el-date-picker clearable
                v-model="form.processTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择处理时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="处理结果" prop="processResult">
          <el-input v-model="form.processResult" type="textarea" :rows="3" placeholder="请输入处理结果" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listModificationRequest, getModificationRequest, delModificationRequest, addModificationRequest, updateModificationRequest } from "@/api/system/modificationRequest"
import { OwnerSelect, CommunitySelect } from "@/components"

export default {
  name: "ModificationRequest",
  dicts: ['modification_request_type','modification_status'],
  components: {
    OwnerSelect,
    CommunitySelect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 修改申请工单表格数据
      modificationRequestList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        requestType: null,
        requestContent: null,
        requestBy: null,
        requestTime: null,
        status: null,
        processBy: null,
        processTime: null,
        processResult: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "请选择小区", trigger: "change" }
        ],
        ownerId: [
          { required: true, message: "请选择业主", trigger: "change" }
        ],
        requestType: [
          { required: true, message: "请选择申请类型", trigger: "change" }
        ],
        requestBy: [
          { required: true, message: "申请人不能为空", trigger: "blur" }
        ],
        requestTime: [
          { required: true, message: "请选择申请时间", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询修改申请工单列表 */
    getList() {
      this.loading = true
      listModificationRequest(this.queryParams).then(response => {
        this.modificationRequestList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取业主显示文本 */
    getOwnerDisplayText(row) {
      if (!row.ownerName) return '-'
      let displayText = row.ownerName
      if (row.buildingNumber && row.houseNumber) {
        displayText += ` ${row.buildingNumber}${row.houseNumber}`
      }
      if (row.rentalStatus === '1' && row.tenantName) {
        displayText += `（${row.tenantName}）`
      }
      return displayText
    },
    /** 获取申请类型标签类型 */
    getRequestTypeTagType(requestType) {
      const typeMap = {
        '信息修改': 'primary',
        '房屋过户': 'warning',
        '租赁变更': 'success',
        '其他': 'info'
      }
      return typeMap[requestType] || 'info'
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        '待处理': 'info',
        '处理中': 'warning',
        '已完成': 'success',
        '已拒绝': 'danger'
      }
      return statusMap[status] || 'info'
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        requestType: null,
        requestContent: null,
        requestBy: null,
        requestTime: null,
        status: null,
        processBy: null,
        processTime: null,
        processResult: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 对搜索参数进行trim处理，去除前后空格
      this.trimQueryParams()
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 去除查询参数的前后空格 */
    trimQueryParams() {
      // 处理字符串类型的搜索参数
      const stringFields = ['requestBy', 'processBy']
      stringFields.forEach(field => {
        if (this.queryParams[field] && typeof this.queryParams[field] === 'string') {
          this.queryParams[field] = this.queryParams[field].trim()
          // 如果trim后为空字符串，设置为null以避免无效查询
          if (this.queryParams[field] === '') {
            this.queryParams[field] = null
          }
        }
      })
    },
    /** 处理输入框失焦时的trim操作 */
    handleInputTrim(fieldName) {
      if (this.queryParams[fieldName] && typeof this.queryParams[fieldName] === 'string') {
        this.queryParams[fieldName] = this.queryParams[fieldName].trim()
        // 如果trim后为空字符串，设置为null
        if (this.queryParams[fieldName] === '') {
          this.queryParams[fieldName] = null
        }
      }
    },
    /** 处理表单输入框失焦时的trim操作 */
    handleFormInputTrim(fieldName) {
      if (this.form[fieldName] && typeof this.form[fieldName] === 'string') {
        this.form[fieldName] = this.form[fieldName].trim()
        // 如果trim后为空字符串，设置为空字符串（表单字段通常不设置为null）
        if (this.form[fieldName] === '') {
          this.form[fieldName] = ''
        }
      }
    },
    /** 查询条件小区变化处理 */
    handleQueryCommunityChange(communityId, communityData) {
      this.queryParams.ownerId = null
    },
    /** 清空小区选择 */
    handleCommunityClear() {
      this.queryParams.ownerId = null
    },
    /** 表单小区变化处理 */
    handleFormCommunityChange(communityId, communityData) {
      this.form.ownerId = null
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加修改申请工单"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getModificationRequest(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改修改申请工单"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateModificationRequest(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addModificationRequest(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除修改申请工单编号为"' + ids + '"的数据项？').then(function() {
        return delModificationRequest(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/modificationRequest/export', {
        ...this.queryParams
      }, `modificationRequest_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
