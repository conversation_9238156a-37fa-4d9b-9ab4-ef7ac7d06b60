<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="收费记录ID" prop="paymentId">
        <el-input
          v-model="queryParams.paymentId"
          placeholder="请输入收费记录ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用名称" prop="feeName">
        <el-input
          v-model="queryParams.feeName"
          placeholder="请输入费用名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付金额" prop="paymentAmount">
        <el-input
          v-model="queryParams.paymentAmount"
          placeholder="请输入支付金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付月数" prop="paymentMonths">
        <el-input
          v-model="queryParams.paymentMonths"
          placeholder="请输入支付月数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="支付天数" prop="paymentDays">
        <el-input
          v-model="queryParams.paymentDays"
          placeholder="请输入支付天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联账单明细ID" prop="billDetailId">
        <el-input
          v-model="queryParams.billDetailId"
          placeholder="请输入关联账单明细ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否预交(0否,1是)" prop="isAdvance">
        <el-input
          v-model="queryParams.isAdvance"
          placeholder="请输入是否预交(0否,1是)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="缴费周期开始" prop="periodStart">
        <el-date-picker clearable
          v-model="queryParams.periodStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择缴费周期开始">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="缴费周期结束" prop="periodEnd">
        <el-date-picker clearable
          v-model="queryParams.periodEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择缴费周期结束">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:paymentDetail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:paymentDetail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:paymentDetail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:paymentDetail:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="paymentDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="收费记录ID" align="center" prop="paymentId" />
      <el-table-column label="费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)" align="center" prop="feeType" />
      <el-table-column label="费用名称" align="center" prop="feeName" />
      <el-table-column label="支付金额" align="center" prop="paymentAmount" />
      <el-table-column label="支付月数" align="center" prop="paymentMonths" />
      <el-table-column label="支付天数" align="center" prop="paymentDays" />
      <el-table-column label="关联账单明细ID" align="center" prop="billDetailId" />
      <el-table-column label="是否预交(0否,1是)" align="center" prop="isAdvance" />
      <el-table-column label="缴费周期开始" align="center" prop="periodStart" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.periodStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="缴费周期结束" align="center" prop="periodEnd" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.periodEnd, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:paymentDetail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:paymentDetail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收费明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="收费记录ID" prop="paymentId">
          <el-input v-model="form.paymentId" placeholder="请输入收费记录ID" />
        </el-form-item>
        <el-form-item label="费用名称" prop="feeName">
          <el-input v-model="form.feeName" placeholder="请输入费用名称" />
        </el-form-item>
        <el-form-item label="支付金额" prop="paymentAmount">
          <el-input v-model="form.paymentAmount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="支付月数" prop="paymentMonths">
          <el-input v-model="form.paymentMonths" placeholder="请输入支付月数" />
        </el-form-item>
        <el-form-item label="支付天数" prop="paymentDays">
          <el-input v-model="form.paymentDays" placeholder="请输入支付天数" />
        </el-form-item>
        <el-form-item label="关联账单明细ID" prop="billDetailId">
          <el-input v-model="form.billDetailId" placeholder="请输入关联账单明细ID" />
        </el-form-item>
        <el-form-item label="是否预交(0否,1是)" prop="isAdvance">
          <el-input v-model="form.isAdvance" placeholder="请输入是否预交(0否,1是)" />
        </el-form-item>
        <el-form-item label="缴费周期开始" prop="periodStart">
          <el-date-picker clearable
            v-model="form.periodStart"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择缴费周期开始">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="缴费周期结束" prop="periodEnd">
          <el-date-picker clearable
            v-model="form.periodEnd"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择缴费周期结束">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPaymentDetail, getPaymentDetail, delPaymentDetail, addPaymentDetail, updatePaymentDetail } from "@/api/system/paymentDetail"

export default {
  name: "PaymentDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收费明细表格数据
      paymentDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        paymentId: null,
        feeType: null,
        feeName: null,
        paymentAmount: null,
        paymentMonths: null,
        paymentDays: null,
        billDetailId: null,
        isAdvance: null,
        periodStart: null,
        periodEnd: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        paymentId: [
          { required: true, message: "收费记录ID不能为空", trigger: "blur" }
        ],
        feeType: [
          { required: true, message: "费用类型(1物业费,2停车费,3卫生费,4电梯费,5滞纳金)不能为空", trigger: "change" }
        ],
        feeName: [
          { required: true, message: "费用名称不能为空", trigger: "blur" }
        ],
        paymentAmount: [
          { required: true, message: "支付金额不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询收费明细列表 */
    getList() {
      this.loading = true
      listPaymentDetail(this.queryParams).then(response => {
        this.paymentDetailList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        paymentId: null,
        feeType: null,
        feeName: null,
        paymentAmount: null,
        paymentMonths: null,
        paymentDays: null,
        billDetailId: null,
        isAdvance: null,
        periodStart: null,
        periodEnd: null,
        createTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加收费明细"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getPaymentDetail(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改收费明细"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePaymentDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addPaymentDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除收费明细编号为"' + ids + '"的数据项？').then(function() {
        return delPaymentDetail(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/paymentDetail/export', {
        ...this.queryParams
      }, `paymentDetail_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
