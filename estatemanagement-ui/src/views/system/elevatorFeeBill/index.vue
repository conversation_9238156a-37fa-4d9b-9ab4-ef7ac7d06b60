<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <el-select
          v-model="queryParams.communityId"
          placeholder="请选择小区"
          clearable
          filterable
          remote
          reserve-keyword
          :remote-method="remoteCommunitySearch"
          :loading="communityLoading"
          @change="handleQueryCommunityChange"
          @clear="handleCommunityClear">
          <el-option
            v-for="community in communityOptions"
            :key="community.id"
            :label="community.communityName"
            :value="community.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="业主" prop="ownerId">
        <el-select v-model="queryParams.ownerId" placeholder="请选择业主" clearable filterable>
          <el-option
            v-for="owner in queryOwnerOptions"
            :key="owner.id"
            :label="`${owner.buildingNumber}-${owner.houseNumber} ${owner.ownerName}`"
            :value="owner.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账单月份(YYYY-MM-01)" prop="billMonth">
        <el-date-picker clearable
          v-model="queryParams.billMonth"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择账单月份(YYYY-MM-01)">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="房屋面积(按面积计费时使用)" prop="houseArea">
        <el-input
          v-model="queryParams.houseArea"
          placeholder="请输入房屋面积(按面积计费时使用)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="缴费日期" prop="paymentDate">
        <el-date-picker clearable
          v-model="queryParams.paymentDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择缴费日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="收据编号" prop="receiptNumber">
        <el-input
          v-model="queryParams.receiptNumber"
          placeholder="请输入收据编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:elevatorFeeBill:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:elevatorFeeBill:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:elevatorFeeBill:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:elevatorFeeBill:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="elevatorFeeBillList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="小区ID" align="center" prop="communityId" />
      <el-table-column label="业主ID" align="center" prop="ownerId" />
      <el-table-column label="账单月份(YYYY-MM-01)" align="center" prop="billMonth" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.billMonth, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="房屋面积(按面积计费时使用)" align="center" prop="houseArea" />
      <el-table-column label="金额" align="center" prop="amount" />
      <el-table-column label="状态(0未缴,1已缴)" align="center" prop="status" />
      <el-table-column label="缴费日期" align="center" prop="paymentDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收据编号" align="center" prop="receiptNumber" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:elevatorFeeBill:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:elevatorFeeBill:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改电梯费对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小区ID" prop="communityId">
          <el-input v-model="form.communityId" placeholder="请输入小区ID" />
        </el-form-item>
        <el-form-item label="业主ID" prop="ownerId">
          <el-input v-model="form.ownerId" placeholder="请输入业主ID" />
        </el-form-item>
        <el-form-item label="账单月份(YYYY-MM-01)" prop="billMonth">
          <el-date-picker clearable
            v-model="form.billMonth"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择账单月份(YYYY-MM-01)">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="房屋面积(按面积计费时使用)" prop="houseArea">
          <el-input v-model="form.houseArea" placeholder="请输入房屋面积(按面积计费时使用)" />
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item label="缴费日期" prop="paymentDate">
          <el-date-picker clearable
            v-model="form.paymentDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择缴费日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="收据编号" prop="receiptNumber">
          <el-input v-model="form.receiptNumber" placeholder="请输入收据编号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listElevatorFeeBill, getElevatorFeeBill, delElevatorFeeBill, addElevatorFeeBill, updateElevatorFeeBill } from "@/api/system/elevatorFeeBill"
import { listCommunityMangement, searchCommunityMangement } from "@/api/system/communityMangement"
import { listOwnerMangement } from "@/api/system/ownerMangement"

export default {
  name: "ElevatorFeeBill",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 电梯费表格数据
      elevatorFeeBillList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        ownerId: null,
        billMonth: null,
        houseArea: null,
        amount: null,
        status: null,
        paymentDate: null,
        receiptNumber: null,
      },
      // 小区选项
      communityOptions: [],
      // 查询条件业主选项
      queryOwnerOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "小区ID不能为空", trigger: "blur" }
        ],
        ownerId: [
          { required: true, message: "业主ID不能为空", trigger: "blur" }
        ],
        billMonth: [
          { required: true, message: "账单月份(YYYY-MM-01)不能为空", trigger: "blur" }
        ],
        amount: [
          { required: true, message: "金额不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getCommunityList()
  },
  methods: {
    /** 查询电梯费列表 */
    getList() {
      this.loading = true
      listElevatorFeeBill(this.queryParams).then(response => {
        this.elevatorFeeBillList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        billMonth: null,
        houseArea: null,
        amount: null,
        status: null,
        paymentDate: null,
        receiptNumber: null,
        createTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加电梯费"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getElevatorFeeBill(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改电梯费"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateElevatorFeeBill(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addElevatorFeeBill(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除电梯费编号为"' + ids + '"的数据项？').then(function() {
        return delElevatorFeeBill(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/elevatorFeeBill/export', {
        ...this.queryParams
      }, `elevatorFeeBill_${new Date().getTime()}.xlsx`)
    },
    /** 获取小区列表 */
    getCommunityList() {
      listCommunityMangement().then(response => {
        this.communityOptions = response.rows || response.data || []
      })
    },
    /** 查询条件小区变化处理 */
    handleQueryCommunityChange() {
      this.queryParams.ownerId = null
      this.getQueryOwnerList(this.queryParams.communityId)
      this.handleQuery()
    },
    /** 获取查询条件业主列表 */
    getQueryOwnerList(communityId) {
      if (communityId) {
        listOwnerMangement({ communityId: communityId }).then(response => {
          this.queryOwnerOptions = response.rows || []
        })
      } else {
        this.queryOwnerOptions = []
      }
    }
  }
}
</script>
