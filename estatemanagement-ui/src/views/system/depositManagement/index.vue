<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小区" prop="communityId">
        <el-select v-model="queryParams.communityId" placeholder="请选择小区" clearable>
          <el-option
            v-for="community in communityList"
            :key="community.id"
            :label="community.communityName"
            :value="community.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="押金类型" prop="depositType">
        <el-select v-model="queryParams.depositType" placeholder="请选择押金类型" clearable>
          <el-option
            v-for="dict in dict.type.property_deposit_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="已收取" value="0"></el-option>
          <el-option label="已退还" value="1"></el-option>
          <el-option label="部分退还" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="押金名称" prop="depositName">
        <el-input
          v-model="queryParams.depositName"
          placeholder="请输入押金名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:depositManagement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:depositManagement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:depositManagement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:depositManagement:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="depositList" @selection-change="handleSelectionChange" @row-click="handleRowClick" style="cursor: pointer;">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="收据编号" align="center" prop="receiptNumber" />
      <el-table-column label="押金名称" align="center" prop="depositName" />
      <el-table-column label="押金类型" align="center" prop="depositType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.property_deposit_type" :value="scope.row.depositType"/>
        </template>
      </el-table-column>
      <el-table-column label="小区名称" align="center" prop="communityName" />
      <el-table-column label="业主信息" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.ownerName }}</div>
          <div style="color: #999; font-size: 12px;">
            {{ scope.row.buildingNumber }}栋{{ scope.row.houseNumber }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="押金金额" align="center" prop="depositAmount" />
      <el-table-column label="收取日期" align="center" prop="collectDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.collectDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收取人" align="center" prop="collectUserName" />
      <el-table-column label="退还金额" align="center" prop="refundAmount" />
      <el-table-column label="退还日期" align="center" prop="refundDate" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.refundDate ? parseTime(scope.row.refundDate, '{y}-{m}-{d}') : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="装修信息" align="center" v-if="showRenovationInfo">
        <template slot-scope="scope">
          <div v-if="scope.row.depositType === 1 && scope.row.renovationManager">
            <div style="font-size: 12px;">负责人：{{ scope.row.renovationManager }}</div>
            <div style="font-size: 12px; color: #999;">
              {{ scope.row.renovationStartDate ? parseTime(scope.row.renovationStartDate, '{y}-{m}-{d}') : '' }}
              {{ scope.row.renovationStartDate && scope.row.renovationEndDate ? ' ~ ' : '' }}
              {{ scope.row.renovationEndDate ? parseTime(scope.row.renovationEndDate, '{y}-{m}-{d}') : '' }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click.stop="handleViewDetail(scope.row)"
            v-hasPermi="['system:depositManagement:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(scope.row)"
            v-hasPermi="['system:depositManagement:edit']"
          >修改</el-button>
          <el-button
            v-if="scope.row.status !== 1"
            size="mini"
            type="text"
            icon="el-icon-money"
            @click.stop="handleRefund(scope.row)"
            v-hasPermi="['system:depositManagement:refund']"
          >退还</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(scope.row)"
            v-hasPermi="['system:depositManagement:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改押金管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="小区" prop="communityId">
              <el-select v-model="form.communityId" placeholder="请选择小区" @change="onCommunityChange" style="width: 100%">
                <el-option
                  v-for="community in communityList"
                  :key="community.id"
                  :label="community.communityName"
                  :value="community.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业主" prop="ownerId">
              <el-select v-model="form.ownerId" placeholder="请选择业主" filterable style="width: 100%">
                <el-option
                  v-for="owner in ownerList"
                  :key="owner.id"
                  :label="`${owner.ownerName} (${owner.buildingNumber}栋${owner.houseNumber})`"
                  :value="owner.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="押金类型" prop="depositType">
              <el-select v-model="form.depositType" placeholder="请选择押金类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.property_deposit_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="押金名称" prop="depositName">
              <el-input v-model="form.depositName" placeholder="请输入押金名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="押金金额" prop="depositAmount">
              <el-input v-model="form.depositAmount" placeholder="请输入押金金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收取日期" prop="collectDate">
              <el-date-picker clearable
                v-model="form.collectDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择收取日期"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select v-model="form.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option label="现金" value="现金"></el-option>
                <el-option label="银行转账" value="银行转账"></el-option>
                <el-option label="支付宝" value="支付宝"></el-option>
                <el-option label="微信" value="微信"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收据编号" prop="receiptNumber">
              <el-input v-model="form.receiptNumber" placeholder="自动生成" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="form.depositType === 1">
          <el-row>
            <el-col :span="12">
              <el-form-item label="装修开始日期" prop="renovationStartDate">
                <el-date-picker clearable
                  v-model="form.renovationStartDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择装修开始日期"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="装修结束日期" prop="renovationEndDate">
                <el-date-picker clearable
                  v-model="form.renovationEndDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择装修结束日期"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="装修负责人" prop="renovationManager">
                <el-input v-model="form.renovationManager" placeholder="请输入装修负责人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="负责人电话" prop="renovationManagerPhone">
                <el-input v-model="form.renovationManagerPhone" placeholder="请输入负责人电话" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 押金详情对话框 -->
    <el-dialog title="押金详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="收据编号">
          <el-tag type="info">{{ detailData.receiptNumber || '-' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="押金状态">
          <el-tag :type="getStatusType(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="押金类型">

          <dict-tag :options="dict.type.property_deposit_type" :value="detailData.depositType"/>

        </el-descriptions-item>
        <el-descriptions-item label="押金名称">
          {{ detailData.depositName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="押金金额">
          <span style="color: #E6A23C; font-weight: bold; font-size: 16px;">
            ¥{{ detailData.depositAmount || '0.00' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="已退还金额">
          <span style="color: #67C23A; font-weight: bold;">
            ¥{{ detailData.refundAmount || '0.00' }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="小区名称">
          {{ detailData.communityName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="业主信息">
          <div>
            <div>{{ detailData.ownerName || '-' }}</div>
            <div style="color: #999; font-size: 12px;">
              {{ detailData.buildingNumber }}栋{{ detailData.houseNumber }}
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="收取日期">
          {{ detailData.collectDate ? parseTime(detailData.collectDate, '{y}-{m}-{d}') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="收取人">
          {{ detailData.collectUserName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          {{ detailData.paymentMethod || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="退还日期">
          {{ detailData.refundDate ? parseTime(detailData.refundDate, '{y}-{m}-{d}') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="退还人" v-if="detailData.refundUserName">
          {{ detailData.refundUserName }}
        </el-descriptions-item>
        <el-descriptions-item label="退还原因" v-if="detailData.refundReason" span="2">
          {{ detailData.refundReason }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 装修信息 -->
      <div v-if="detailData.depositType === 1" style="margin-top: 20px;">
        <el-divider content-position="left">
          <i class="el-icon-house"></i> 装修信息
        </el-divider>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="装修负责人">
            {{ detailData.renovationManager || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人电话">
            {{ detailData.renovationManagerPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="装修开始日期">
            {{ detailData.renovationStartDate ? parseTime(detailData.renovationStartDate, '{y}-{m}-{d}') : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="装修结束日期">
            {{ detailData.renovationEndDate ? parseTime(detailData.renovationEndDate, '{y}-{m}-{d}') : '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 备注信息 -->
      <div v-if="detailData.remark" style="margin-top: 20px;">
        <el-divider content-position="left">
          <i class="el-icon-document"></i> 备注信息
        </el-divider>
        <el-card shadow="never" style="background-color: #f5f7fa;">
          <p style="margin: 0; line-height: 1.6;">{{ detailData.remark }}</p>
        </el-card>
      </div>

      <!-- 操作记录 -->
      <div style="margin-top: 20px;">
        <el-divider content-position="left">
          <i class="el-icon-time"></i> 操作记录
        </el-divider>
        <el-timeline>
          <el-timeline-item
            :timestamp="detailData.createTime ? parseTime(detailData.createTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-'"
            color="#0bbd87">
            <h4>押金收取</h4>
            <p>收取人：{{ detailData.collectUserName || '-' }}</p>
            <p>收取金额：¥{{ detailData.depositAmount || '0.00' }}</p>
            <p>支付方式：{{ detailData.paymentMethod || '-' }}</p>
          </el-timeline-item>
          <el-timeline-item
            v-if="detailData.refundDate"
            :timestamp="detailData.refundDate ? parseTime(detailData.refundDate, '{y}-{m}-{d}') : '-'"
            color="#e4e7ed">
            <h4>押金退还</h4>
            <p>退还人：{{ detailData.refundUserName || '-' }}</p>
            <p>退还金额：¥{{ detailData.refundAmount || '0.00' }}</p>
            <p>退还原因：{{ detailData.refundReason || '-' }}</p>
          </el-timeline-item>
        </el-timeline>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
        <el-button
          v-if="detailData.status !== 1"
          type="warning"
          @click="handleRefundFromDetail"
          v-hasPermi="['system:depositManagement:refund']"
        >退还押金</el-button>
        <el-button
          type="primary"
          @click="handleUpdateFromDetail"
          v-hasPermi="['system:depositManagement:edit']"
        >编辑信息</el-button>
      </div>
    </el-dialog>

    <!-- 退还押金对话框 -->
    <el-dialog title="退还押金" :visible.sync="refundOpen" width="500px" append-to-body>
      <el-form ref="refundForm" :model="refundForm" :rules="refundRules" label-width="100px">
        <el-form-item label="押金信息">
          <div>
            <p><strong>押金名称：</strong>{{ refundForm.depositName }}</p>
            <p><strong>押金金额：</strong>{{ refundForm.depositAmount }}</p>
            <p><strong>已退还金额：</strong>{{ refundForm.alreadyRefunded || 0 }}</p>
            <p><strong>可退还金额：</strong>{{ refundForm.availableRefund }}</p>
          </div>
        </el-form-item>
        <el-form-item label="退还金额" prop="refundAmount">
          <el-input v-model="refundForm.refundAmount" placeholder="请输入退还金额" />
        </el-form-item>
        <el-form-item label="退还日期" prop="refundDate">
          <el-date-picker clearable
            v-model="refundForm.refundDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择退还日期"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="退还原因" prop="refundReason">
          <el-input v-model="refundForm.refundReason" type="textarea" placeholder="请输入退还原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRefund">确 定</el-button>
        <el-button @click="cancelRefund">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDepositManagement, getDepositManagement, delDepositManagement, addDepositManagement, updateDepositManagement, refundDeposit } from "@/api/system/depositManagement";
import { listCommunityMangement } from "@/api/system/communityMangement";
import { listOwnerMangement } from "@/api/system/ownerMangement";

export default {
  name: "DepositManagement",
  dicts: ['property_deposit_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 显示装修信息列
      showRenovationInfo: true,
      // 总条数
      total: 0,
      // 押金管理表格数据
      depositList: [],
      // 小区列表
      communityList: [],
      // 业主列表
      ownerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示退还弹出层
      refundOpen: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        communityId: null,
        depositType: null,
        depositName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 退还表单参数
      refundForm: {},
      // 表单校验
      rules: {
        communityId: [
          { required: true, message: "小区不能为空", trigger: "change" }
        ],
        ownerId: [
          { required: true, message: "业主不能为空", trigger: "change" }
        ],
        depositType: [
          { required: true, message: "押金类型不能为空", trigger: "change" }
        ],
        depositName: [
          { required: true, message: "押金名称不能为空", trigger: "blur" }
        ],
        depositAmount: [
          { required: true, message: "押金金额不能为空", trigger: "blur" }
        ]
      },
      // 退还表单校验
      refundRules: {
        refundAmount: [
          { required: true, message: "退还金额不能为空", trigger: "blur" }
        ],
        refundDate: [
          { required: true, message: "退还日期不能为空", trigger: "change" }
        ],
        refundReason: [
          { required: true, message: "退还原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCommunityList();
  },
  methods: {
    /** 查询押金管理列表 */
    getList() {
      this.loading = true;
      listDepositManagement(this.queryParams).then(response => {
        this.depositList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询小区列表 */
    getCommunityList() {
      listCommunityMangement({}).then(response => {
        this.communityList = response.rows;
      });
    },
    /** 查询业主列表 */
    getOwnerList(communityId) {
      if (communityId) {
        listOwnerMangement({ communityId: communityId }).then(response => {
          this.ownerList = response.rows;
        });
      } else {
        this.ownerList = [];
      }
    },
    /** 小区变化事件 */
    onCommunityChange(communityId) {
      this.form.ownerId = null;
      this.getOwnerList(communityId);
    },
    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '已收取',
        1: '已退还',
        2: '部分退还'
      };
      return statusMap[status] || '未知';
    },
    /** 获取状态类型 */
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'info'
      };
      return typeMap[status] || '';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消退还
    cancelRefund() {
      this.refundOpen = false;
      this.resetRefund();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        communityId: null,
        ownerId: null,
        depositType: 1,
        depositName: null,
        depositAmount: null,
        collectDate: null,
        paymentMethod: null,
        receiptNumber: null,
        renovationStartDate: null,
        renovationEndDate: null,
        renovationManager: null,
        renovationManagerPhone: null,
        remark: null
      };
      this.resetForm("form");
    },
    // 退还表单重置
    resetRefund() {
      this.refundForm = {
        id: null,
        depositName: null,
        depositAmount: null,
        alreadyRefunded: null,
        availableRefund: null,
        refundAmount: null,
        refundDate: null,
        refundReason: null
      };
      this.resetForm("refundForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 自动设置收取日期为当天
      this.form.collectDate = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.open = true;
      this.title = "添加押金管理";
    },
    /** 点击行查看详情 */
    handleRowClick(row, column, event) {
      // 如果点击的是操作列，不触发行点击事件
      if (column && column.className && column.className.includes('fixed-width')) {
        return;
      }
      this.handleViewDetail(row);
    },
    /** 查看详情按钮操作 */
    handleViewDetail(row) {
      this.detailData = { ...row };
      this.detailOpen = true;
    },
    /** 从详情页面跳转到退还 */
    handleRefundFromDetail() {
      this.detailOpen = false;
      this.handleRefund(this.detailData);
    },
    /** 从详情页面跳转到编辑 */
    handleUpdateFromDetail() {
      this.detailOpen = false;
      this.handleUpdate(this.detailData);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDepositManagement(id).then(response => {
        this.form = response.data;
        this.getOwnerList(this.form.communityId);
        this.open = true;
        this.title = "修改押金管理";
      });
    },
    /** 退还按钮操作 */
    handleRefund(row) {
      this.resetRefund();
      this.refundForm.id = row.id;
      this.refundForm.depositName = row.depositName;
      this.refundForm.depositAmount = row.depositAmount;
      this.refundForm.alreadyRefunded = row.refundAmount || 0;
      this.refundForm.availableRefund = row.depositAmount - (row.refundAmount || 0);
      this.refundForm.refundDate = this.parseTime(new Date(), '{y}-{m}-{d}');
      this.refundOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDepositManagement(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDepositManagement(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交退还 */
    submitRefund() {
      this.$refs["refundForm"].validate(valid => {
        if (valid) {
          refundDeposit(this.refundForm).then(response => {
            this.$modal.msgSuccess("退还成功");
            this.refundOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除押金管理编号为"' + ids + '"的数据项？').then(function() {
        return delDepositManagement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/depositManagement/export', {
        ...this.queryParams
      }, `depositManagement_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
/* 表格行悬停效果 */
.el-table tbody tr:hover {
  background-color: #f5f7fa;
  cursor: pointer;
}

/* 详情对话框样式 */
.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions-item__label {
  font-weight: bold;
  color: #606266;
}

/* 时间轴样式 */
.el-timeline-item__content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.el-timeline-item__content p {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

/* 卡片样式 */
.el-card {
  border: 1px solid #ebeef5;
}

/* 分割线样式 */
.el-divider {
  margin: 20px 0 15px 0;
}

.el-divider__text {
  font-weight: bold;
  color: #409eff;
}

/* 标签样式 */
.el-tag {
  margin-right: 8px;
}

/* 金额显示样式 */
.amount-display {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
}
</style>
