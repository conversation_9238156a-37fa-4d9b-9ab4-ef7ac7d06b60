<template>
  <div class="app-container">
    <h2>打印功能测试</h2>
    
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card header="账单催缴单测试">
          <el-button @click="testBillNotice" type="primary">测试账单催缴单</el-button>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card header="停车催缴单测试">
          <el-button @click="testParkingNotice" type="success">测试停车催缴单</el-button>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card header="收费单测试">
          <el-button @click="testPaymentReceipt" type="warning">测试收费单</el-button>
        </el-card>
      </el-col>
    </el-row>

    <!-- 打印组件 -->
    <print-component ref="printComponent">
      <bill-notice 
        v-if="currentPrintType === 'bill'"
        :billData="testBillData">
      </bill-notice>
      
      <parking-notice 
        v-if="currentPrintType === 'parking'"
        :parkingData="testParkingData">
      </parking-notice>
      
      <payment-receipt 
        v-if="currentPrintType === 'payment'"
        :paymentData="testPaymentData">
      </payment-receipt>
    </print-component>
  </div>
</template>

<script>
import PrintComponent from "@/components/Print/index.vue"
import BillNotice from "@/components/Print/BillNotice.vue"
import ParkingNotice from "@/components/Print/ParkingNotice.vue"
import PaymentReceipt from "@/components/Print/PaymentReceipt.vue"

export default {
  name: "PrintTest",
  components: {
    PrintComponent,
    BillNotice,
    ParkingNotice,
    PaymentReceipt
  },
  data() {
    return {
      currentPrintType: '',
      testBillData: {
        bill: {
          communityName: '测试小区',
          billNumber: 'BILL202506190001',
          ownerName: '张三',
          buildingNumber: '1',
          houseNumber: '101',
          billPeriodStart: '2025-01-01',
          billPeriodEnd: '2025-12-31',
          propertyFee: 1200.00,
          parkingFee: 300.00,
          sanitationFee: 120.00,
          elevatorFee: 0,
          lateFee: 0,
          totalAmount: 1620.00
        }
      },
      testParkingData: {
        communityName: '测试小区',
        spaceNumber: 'A001',
        plateNumber: '京A12345',
        ownerName: '李四',
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        monthlyFee: 300.00,
        isThreeCertificates: 1
      },
      testPaymentData: {
        communityName: '测试小区',
        receiptNumber: 'REC202506190001',
        ownerName: '王五',
        buildingNumber: '2',
        houseNumber: '201',
        paymentDate: '2025-06-19',
        paymentMethod: '现金',
        propertyFeeAmount: 1200.00,
        parkingFeeAmount: 300.00,
        sanitationFeeAmount: 120.00,
        elevatorFeeAmount: 0,
        lateFeeAmount: 0,
        paymentAmount: 1620.00,
        remark: '测试收费单'
      }
    }
  },
  methods: {
    testBillNotice() {
      this.currentPrintType = 'bill'
      this.$nextTick(() => {
        this.$refs.printComponent.print()
      })
    },
    testParkingNotice() {
      this.currentPrintType = 'parking'
      this.$nextTick(() => {
        this.$refs.printComponent.print()
      })
    },
    testPaymentReceipt() {
      this.currentPrintType = 'payment'
      this.$nextTick(() => {
        this.$refs.printComponent.print()
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
