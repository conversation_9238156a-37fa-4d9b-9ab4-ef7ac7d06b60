/**
 * el-select 下拉加载更多指令
 * 使用方法：v-el-select-loadmore="loadmore"
 */

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

export default {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')

    if (!SELECTWRAP_DOM) return

    // 创建防抖的加载函数
    const debouncedLoad = debounce(() => {
      binding.value()
    }, 100) // 100ms防抖

    let isLoading = false

    SELECTWRAP_DOM.addEventListener('scroll', function() {
      /**
       * scrollHeight 获取元素内容高度(只读)
       * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
       * clientHeight 读取元素的可见高度(只读)
       * 如果元素滚动到底, 下面等式返回true, 否则返回false:
       * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
       */

      // 增加一些容错，提前一点触发加载
      const threshold = 5 // 5px的提前量
      const condition = this.scrollHeight - this.scrollTop <= this.clientHeight + threshold

      if (condition && !isLoading) {
        isLoading = true
        debouncedLoad()

        // 重置loading状态
        setTimeout(() => {
          isLoading = false
        }, 300)
      }
    })
  }
}
