import request from '@/utils/request'

// 查询人脸识别开通记录列表
export function listFaceAccess(query) {
  return request({
    url: '/system/faceAccess/list',
    method: 'get',
    params: query
  })
}

// 查询人脸识别开通记录详细
export function getFaceAccess(id) {
  return request({
    url: '/system/faceAccess/' + id,
    method: 'get'
  })
}

// 新增人脸识别开通记录
export function addFaceAccess(data) {
  return request({
    url: '/system/faceAccess',
    method: 'post',
    data: data
  })
}

// 修改人脸识别开通记录
export function updateFaceAccess(data) {
  return request({
    url: '/system/faceAccess',
    method: 'put',
    data: data
  })
}

// 删除人脸识别开通记录
export function delFaceAccess(id) {
  return request({
    url: '/system/faceAccess/' + id,
    method: 'delete'
  })
}
