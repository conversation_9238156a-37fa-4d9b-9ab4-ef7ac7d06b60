import request from '@/utils/request'

// 查询退费记录列表
export function listRefundRecord(query) {
  return request({
    url: '/system/refundRecord/list',
    method: 'get',
    params: query
  })
}

// 查询退费记录详细
export function getRefundRecord(id) {
  return request({
    url: '/system/refundRecord/' + id,
    method: 'get'
  })
}

// 新增退费记录
export function addRefundRecord(data) {
  return request({
    url: '/system/refundRecord',
    method: 'post',
    data: data
  })
}

// 修改退费记录
export function updateRefundRecord(data) {
  return request({
    url: '/system/refundRecord',
    method: 'put',
    data: data
  })
}

// 删除退费记录
export function delRefundRecord(id) {
  return request({
    url: '/system/refundRecord/' + id,
    method: 'delete'
  })
}

// 处理退费申请
export function processRefund(data) {
  return request({
    url: '/system/refundRecord/processRefund',
    method: 'post',
    data: data
  })
}

// 计算可退费金额
export function calculateRefundAmount(data) {
  return request({
    url: '/system/refundRecord/calculateRefundAmount',
    method: 'post',
    data: data
  })
}

// 获取收费记录的退费信息
export function getPaymentRefundInfo(paymentId) {
  return request({
    url: '/system/refundRecord/paymentRefundInfo/' + paymentId,
    method: 'get'
  })
}

// 审核退费记录
export function auditRefund(data) {
  return request({
    url: '/system/refundRecord/audit',
    method: 'post',
    data: data
  })
}

// 统计退费金额
export function getRefundStatistics(data) {
  return request({
    url: '/system/refundRecord/statistics',
    method: 'post',
    data: data
  })
}

// 根据原收费记录ID查询退费记录
export function getRefundsByOriginalPayment(originalPaymentId) {
  return request({
    url: '/system/refundRecord/byOriginalPayment/' + originalPaymentId,
    method: 'get'
  })
}
