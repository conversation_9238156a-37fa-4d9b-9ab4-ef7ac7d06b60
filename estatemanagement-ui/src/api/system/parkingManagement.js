import request from '@/utils/request'

// 查询车位管理列表
export function listParkingManagement(query) {
  return request({
    url: '/system/parkingManagement/list',
    method: 'get',
    params: query
  })
}

// 查询车位管理详细
export function getParkingManagement(id) {
  return request({
    url: '/system/parkingManagement/' + id,
    method: 'get'
  })
}

// 新增车位管理
export function addParkingManagement(data) {
  return request({
    url: '/system/parkingManagement',
    method: 'post',
    data: data
  })
}

// 修改车位管理
export function updateParkingManagement(data) {
  return request({
    url: '/system/parkingManagement',
    method: 'put',
    data: data
  })
}

// 删除车位管理
export function delParkingManagement(id) {
  return request({
    url: '/system/parkingManagement/' + id,
    method: 'delete'
  })
}



// 释放车位
export function releaseParking(id) {
  return request({
    url: '/system/parkingManagement/release/' + id,
    method: 'post'
  })
}

// 查询可用车位
export function getAvailableParking(query) {
  return request({
    url: '/system/parkingManagement/available',
    method: 'get',
    params: query
  })
}

// 批量创建车位
export function batchCreateParking(data) {
  return request({
    url: '/system/parkingManagement/batchCreate',
    method: 'post',
    data: data
  })
}


