import request from '@/utils/request'

// 查询业主管理列表
export function listOwnerMangement(query) {
  return request({
    url: '/system/ownerMangement/list',
    method: 'get',
    params: query
  })
}

// 查询业主管理详细
export function getOwnerMangement(id) {
  return request({
    url: '/system/ownerMangement/' + id,
    method: 'get'
  })
}

// 新增业主管理
export function addOwnerMangement(data) {
  return request({
    url: '/system/ownerMangement',
    method: 'post',
    data: data
  })
}

// 修改业主管理
export function updateOwnerMangement(data) {
  return request({
    url: '/system/ownerMangement',
    method: 'put',
    data: data
  })
}

// 删除业主管理
export function delOwnerMangement(id) {
  return request({
    url: '/system/ownerMangement/' + id,
    method: 'delete'
  })
}

// 获取业主自动补全列表
export function getOwnerAutocomplete(query) {
  return request({
    url: '/system/ownerMangement/autocomplete',
    method: 'get',
    params: query
  })
}

// 获取指定小区的可用车位列表
export function getAvailableParkingSpaces(communityId) {
  return request({
    url: '/system/ownerMangement/availableParkingSpaces',
    method: 'get',
    params: { communityId }
  })
}

// 远程搜索业主
export function searchOwnerMangement(communityId, query) {
  return request({
    url: '/system/ownerMangement/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 20,
      communityId: communityId,
      ownerName: query
    }
  })
}
