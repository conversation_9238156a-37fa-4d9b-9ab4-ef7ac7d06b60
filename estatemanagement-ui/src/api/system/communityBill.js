import request from '@/utils/request'

// 查询小区账单列表
export function listCommunityBill(query) {
  return request({
    url: '/system/communityBill/list',
    method: 'get',
    params: query
  })
}

// 查询小区账单详细
export function getCommunityBill(id) {
  return request({
    url: '/system/communityBill/' + id,
    method: 'get'
  })
}

// 查询小区账单详情（包含明细）
export function getCommunityBillWithDetails(id) {
  return request({
    url: '/system/communityBill/details/' + id,
    method: 'get'
  })
}

// 新增小区账单
export function addCommunityBill(data) {
  return request({
    url: '/system/communityBill',
    method: 'post',
    data: data
  })
}

// 修改小区账单
export function updateCommunityBill(data) {
  return request({
    url: '/system/communityBill',
    method: 'put',
    data: data
  })
}

// 删除小区账单
export function delCommunityBill(id) {
  return request({
    url: '/system/communityBill/' + id,
    method: 'delete'
  })
}

// 自动生成账单
export function generateBills(data) {
  return request({
    url: '/system/communityBill/generate',
    method: 'post',
    data: data
  })
}

// 查询生成进度
export function getGenerationProgress(taskId) {
  return request({
    url: '/system/communityBill/progress/' + taskId,
    method: 'get'
  })
}

// 根据小区ID获取未缴费账单列表（用于批量打印催缴单）
export function getUnpaidBillsByCommunity(communityId) {
  return request({
    url: '/system/communityBill/unpaid/community/' + communityId,
    method: 'get'
  })
}
