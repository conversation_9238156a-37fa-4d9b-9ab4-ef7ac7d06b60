import request from '@/utils/request'

// 查询押金管理列表
export function listDepositManagement(query) {
  return request({
    url: '/system/depositManagement/list',
    method: 'get',
    params: query
  })
}

// 查询押金管理详细
export function getDepositManagement(id) {
  return request({
    url: '/system/depositManagement/' + id,
    method: 'get'
  })
}

// 新增押金管理
export function addDepositManagement(data) {
  return request({
    url: '/system/depositManagement',
    method: 'post',
    data: data
  })
}

// 修改押金管理
export function updateDepositManagement(data) {
  return request({
    url: '/system/depositManagement',
    method: 'put',
    data: data
  })
}

// 删除押金管理
export function delDepositManagement(id) {
  return request({
    url: '/system/depositManagement/' + id,
    method: 'delete'
  })
}

// 退还押金
export function refundDeposit(data) {
  return request({
    url: '/system/depositManagement/refund',
    method: 'post',
    data: data
  })
}
