import request from '@/utils/request'

// 查询小区账单明细列表
export function listCommunityBillDetail(query) {
  return request({
    url: '/system/communityBillDetail/list',
    method: 'get',
    params: query
  })
}

// 查询小区账单明细详细
export function getCommunityBillDetail(id) {
  return request({
    url: '/system/communityBillDetail/' + id,
    method: 'get'
  })
}

// 新增小区账单明细
export function addCommunityBillDetail(data) {
  return request({
    url: '/system/communityBillDetail',
    method: 'post',
    data: data
  })
}

// 修改小区账单明细
export function updateCommunityBillDetail(data) {
  return request({
    url: '/system/communityBillDetail',
    method: 'put',
    data: data
  })
}

// 删除小区账单明细
export function delCommunityBillDetail(id) {
  return request({
    url: '/system/communityBillDetail/' + id,
    method: 'delete'
  })
}
