import request from '@/utils/request'

// 查询电梯费列表
export function listElevatorFeeBill(query) {
  return request({
    url: '/system/elevatorFeeBill/list',
    method: 'get',
    params: query
  })
}

// 查询电梯费详细
export function getElevatorFeeBill(id) {
  return request({
    url: '/system/elevatorFeeBill/' + id,
    method: 'get'
  })
}

// 新增电梯费
export function addElevatorFeeBill(data) {
  return request({
    url: '/system/elevatorFeeBill',
    method: 'post',
    data: data
  })
}

// 修改电梯费
export function updateElevatorFeeBill(data) {
  return request({
    url: '/system/elevatorFeeBill',
    method: 'put',
    data: data
  })
}

// 删除电梯费
export function delElevatorFeeBill(id) {
  return request({
    url: '/system/elevatorFeeBill/' + id,
    method: 'delete'
  })
}
