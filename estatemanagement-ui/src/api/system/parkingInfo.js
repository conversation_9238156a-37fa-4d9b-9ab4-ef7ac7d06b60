import request from '@/utils/request'

// 查询停车费信息列表
export function listParkingInfo(query) {
  return request({
    url: '/system/parkingInfo/list',
    method: 'get',
    params: query
  })
}

// 查询停车费信息详细
export function getParkingInfo(id) {
  return request({
    url: '/system/parkingInfo/' + id,
    method: 'get'
  })
}

// 新增停车费信息
export function addParkingInfo(data) {
  return request({
    url: '/system/parkingInfo',
    method: 'post',
    data: data
  })
}

// 修改停车费信息
export function updateParkingInfo(data) {
  return request({
    url: '/system/parkingInfo',
    method: 'put',
    data: data
  })
}

// 删除停车费信息
export function delParkingInfo(id) {
  return request({
    url: '/system/parkingInfo/' + id,
    method: 'delete'
  })
}

// 根据小区ID获取未缴费停车记录列表（用于批量打印催缴单）
export function getUnpaidParkingByCommunity(communityId) {
  return request({
    url: '/system/parkingInfo/unpaid/community/' + communityId,
    method: 'get'
  })
}
