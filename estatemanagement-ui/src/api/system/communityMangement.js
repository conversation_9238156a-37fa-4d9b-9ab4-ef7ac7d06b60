import request from '@/utils/request'

// 查询小区管理列表
export function listCommunityMangement(query) {
  return request({
    url: '/system/communityMangement/list',
    method: 'get',
    params: query
  })
}

// 查询小区管理详细
export function getCommunityMangement(id) {
  return request({
    url: '/system/communityMangement/' + id,
    method: 'get'
  })
}

// 新增小区管理
export function addCommunityMangement(data) {
  return request({
    url: '/system/communityMangement',
    method: 'post',
    data: data
  })
}

// 修改小区管理
export function updateCommunityMangement(data) {
  return request({
    url: '/system/communityMangement',
    method: 'put',
    data: data
  })
}

// 删除小区管理
export function delCommunityMangement(id) {
  return request({
    url: '/system/communityMangement/' + id,
    method: 'delete'
  })
}

// 远程搜索小区
export function searchCommunityMangement(query) {
  return request({
    url: '/system/communityMangement/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 20,
      communityName: query
    }
  })
}
