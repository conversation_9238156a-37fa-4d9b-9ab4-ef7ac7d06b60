import request from '@/utils/request'

// 查询收费记录列表
export function listPaymentRecord(query) {
  return request({
    url: '/system/paymentRecord/list',
    method: 'get',
    params: query
  })
}

// 查询指定账单的收费记录及明细
export function getBillPaymentRecords(billId) {
  return request({
    url: '/system/paymentRecord/bill/' + billId,
    method: 'get'
  })
}

// 查询未完全支付的账单，用于收费管理
export function getUnpaidBillsForPayment(communityId, ownerId) {
  return request({
    url: '/system/paymentRecord/unpaidBills',
    method: 'get',
    params: { communityId, ownerId }
  })
}

// 执行收费操作
export function processPayment(data) {
  return request({
    url: '/system/paymentRecord/payment',
    method: 'post',
    data: data
  })
}

// 查询收费记录详细
export function getPaymentRecord(id) {
  return request({
    url: '/system/paymentRecord/' + id,
    method: 'get'
  })
}

// 新增收费记录
export function addPaymentRecord(data) {
  return request({
    url: '/system/paymentRecord',
    method: 'post',
    data: data
  })
}

// 修改收费记录
export function updatePaymentRecord(data) {
  return request({
    url: '/system/paymentRecord',
    method: 'put',
    data: data
  })
}

// 删除收费记录
export function delPaymentRecord(id) {
  return request({
    url: '/system/paymentRecord/' + id,
    method: 'delete'
  })
}

// 计算预交费用金额
export function calculateAdvanceFee(communityId, ownerId, feeType, months) {
  return request({
    url: '/system/paymentRecord/calculateAdvanceFee',
    method: 'get',
    params: { communityId, ownerId, feeType, months }
  })
}

// 计算部分支付费用金额
export function calculatePartialFee(data) {
  return request({
    url: '/system/paymentRecord/calculatePartialFee',
    method: 'post',
    data: data
  })
}

// 计算部分支付的详细信息（包括缴费周期）
export function calculatePartialFeeDetails(data) {
  return request({
    url: '/system/paymentRecord/calculatePartialFeeDetails',
    method: 'post',
    data: data
  })
}

// 获取收费记录明细
export function getPaymentDetails(paymentId) {
  return request({
    url: '/system/paymentRecord/details/' + paymentId,
    method: 'get'
  })
}

// 查询收费记录详情（包含明细）用于打印
export function getPaymentRecordWithDetails(paymentId) {
  return request({
    url: '/system/paymentRecord/withDetails/' + paymentId,
    method: 'get'
  })
}

// 手动生成账单
export function generateBills(data) {
  return request({
    url: '/system/paymentRecord/generateBills',
    method: 'post',
    data: data
  })
}

// 查询账单生成配置列表
export function getBillConfigList(query) {
  return request({
    url: '/system/paymentRecord/billConfig/list',
    method: 'get',
    params: query
  })
}

// 根据小区ID查询账单生成配置
export function getBillConfigByCommunityId(communityId) {
  return request({
    url: '/system/paymentRecord/billConfig/community/' + communityId,
    method: 'get'
  })
}

// 新增账单生成配置
export function addBillConfig(data) {
  return request({
    url: '/system/paymentRecord/billConfig',
    method: 'post',
    data: data
  })
}

// 修改账单生成配置
export function updateBillConfig(data) {
  return request({
    url: '/system/paymentRecord/billConfig',
    method: 'put',
    data: data
  })
}

// 启用或停用账单自动生成
export function toggleAutoGeneration(data) {
  return request({
    url: '/system/paymentRecord/billConfig/toggle',
    method: 'post',
    data: data
  })
}

// 计算混合支付金额
export function calculateMixedPaymentAmount(data) {
  return request({
    url: '/system/paymentRecord/calculateMixedAmount',
    method: 'post',
    data: data
  })
}

// 审核收费记录
export function auditPaymentRecord(data) {
  return request({
    url: '/system/paymentRecord/audit',
    method: 'post',
    data: data
  })
}

// 批量审核收费记录
export function batchAuditPaymentRecords(data) {
  return request({
    url: '/system/paymentRecord/batchAudit',
    method: 'post',
    data: data
  })
}

// 查询待审核的收费记录列表
export function listPendingAuditPaymentRecords(query) {
  return request({
    url: '/system/paymentRecord/pendingAudit',
    method: 'get',
    params: query
  })
}

// 获取收费记录审核统计信息
export function getPaymentAuditStatistics(communityId) {
  return request({
    url: '/system/paymentRecord/auditStatistics',
    method: 'get',
    params: { communityId }
  })
}

// 获取审核状态选项
export function getAuditStatusOptions() {
  return request({
    url: '/system/paymentRecord/auditStatusOptions',
    method: 'get'
  })
}
