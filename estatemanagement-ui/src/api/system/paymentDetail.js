import request from '@/utils/request'

// 查询收费明细列表
export function listPaymentDetail(query) {
  return request({
    url: '/system/paymentDetail/list',
    method: 'get',
    params: query
  })
}

// 查询收费明细详细
export function getPaymentDetail(id) {
  return request({
    url: '/system/paymentDetail/' + id,
    method: 'get'
  })
}

// 新增收费明细
export function addPaymentDetail(data) {
  return request({
    url: '/system/paymentDetail',
    method: 'post',
    data: data
  })
}

// 修改收费明细
export function updatePaymentDetail(data) {
  return request({
    url: '/system/paymentDetail',
    method: 'put',
    data: data
  })
}

// 删除收费明细
export function delPaymentDetail(id) {
  return request({
    url: '/system/paymentDetail/' + id,
    method: 'delete'
  })
}
