import request from '@/utils/request'

// 查询小区汇总账单列表
export function listCommunitySummaryBill(query) {
  return request({
    url: '/system/communitySummaryBill/list',
    method: 'get',
    params: query
  })
}

// 查询小区汇总账单详细
export function getCommunitySummaryBill(id) {
  return request({
    url: '/system/communitySummaryBill/' + id,
    method: 'get'
  })
}

// 新增小区汇总账单
export function addCommunitySummaryBill(data) {
  return request({
    url: '/system/communitySummaryBill',
    method: 'post',
    data: data
  })
}

// 修改小区汇总账单
export function updateCommunitySummaryBill(data) {
  return request({
    url: '/system/communitySummaryBill',
    method: 'put',
    data: data
  })
}

// 删除小区汇总账单
export function delCommunitySummaryBill(id) {
  return request({
    url: '/system/communitySummaryBill/' + id,
    method: 'delete'
  })
}

// 财务确认收钱
export function confirmReceive(data) {
  return request({
    url: '/system/communitySummaryBill/confirmReceive',
    method: 'post',
    data: data
  })
}

// 生成汇总账单
export function generateSummaryBills(data) {
  return request({
    url: '/system/communitySummaryBill/generate',
    method: 'post',
    data: data
  })
}

// 查询生成进度
export function getGenerationProgress(taskId) {
  return request({
    url: '/system/communitySummaryBill/progress/' + taskId,
    method: 'get'
  })
}

// 查询汇总账单生成配置列表
export function listSummaryBillGenerationConfig(query) {
  return request({
    url: '/system/summaryBillGenerationConfig/list',
    method: 'get',
    params: query
  })
}

// 根据小区ID查询汇总账单生成配置
export function getSummaryBillGenerationConfigByCommunityId(communityId) {
  return request({
    url: '/system/summaryBillGenerationConfig/community/' + communityId,
    method: 'get'
  })
}

// 新增汇总账单生成配置
export function addSummaryBillGenerationConfig(data) {
  return request({
    url: '/system/summaryBillGenerationConfig',
    method: 'post',
    data: data
  })
}

// 修改汇总账单生成配置
export function updateSummaryBillGenerationConfig(data) {
  return request({
    url: '/system/summaryBillGenerationConfig',
    method: 'put',
    data: data
  })
}
