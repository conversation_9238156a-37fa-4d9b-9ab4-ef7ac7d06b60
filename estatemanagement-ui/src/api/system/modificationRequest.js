import request from '@/utils/request'

// 查询修改申请工单列表
export function listModificationRequest(query) {
  return request({
    url: '/system/modificationRequest/list',
    method: 'get',
    params: query
  })
}

// 查询修改申请工单详细
export function getModificationRequest(id) {
  return request({
    url: '/system/modificationRequest/' + id,
    method: 'get'
  })
}

// 新增修改申请工单
export function addModificationRequest(data) {
  return request({
    url: '/system/modificationRequest',
    method: 'post',
    data: data
  })
}

// 修改修改申请工单
export function updateModificationRequest(data) {
  return request({
    url: '/system/modificationRequest',
    method: 'put',
    data: data
  })
}

// 删除修改申请工单
export function delModificationRequest(id) {
  return request({
    url: '/system/modificationRequest/' + id,
    method: 'delete'
  })
}
