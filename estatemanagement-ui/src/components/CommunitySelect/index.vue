<template>
  <el-select 
    :value="value" 
    :placeholder="placeholder" 
    :clearable="clearable"
    :disabled="disabled"
    :size="size"
    filterable
    remote
    reserve-keyword
    :remote-method="remoteCommunitySearch"
    :loading="communityLoading"
    @input="handleInput"
    @change="handleChange"
    @clear="handleClear"
    @visible-change="handleSelectVisible">
    
    <!-- 小区选项 -->
    <el-option
      v-for="community in communityList"
      :key="community.id"
      :label="community.communityName"
      :value="community.id">
    </el-option>
    
    <!-- 加载中提示 -->
    <el-option
      v-if="communityLoading && communityList.length === 0"
      :value="'loading'"
      disabled
      style="text-align: center; padding: 8px 0;">
      <i class="el-icon-loading" style="margin-right: 5px;"></i>
      <span style="color: #999; font-size: 12px;">搜索中...</span>
    </el-option>
    
    <!-- 无数据提示 -->
    <el-option
      v-if="communityList.length === 0 && !communityLoading && searchKeyword"
      :value="'no-data'"
      disabled
      style="text-align: center; padding: 8px 0; color: #999; font-size: 12px;">
      未找到相关小区
    </el-option>
  </el-select>
</template>

<script>
import { listCommunityMangement, searchCommunityMangement } from "@/api/system/communityMangement";

export default {
  name: "CommunitySelect",
  props: {
    // v-model 绑定的值
    value: {
      type: [String, Number],
      default: null
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择小区'
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 尺寸
    size: {
      type: String,
      default: ''
    },
    // 初始加载数量
    initialSize: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      // 小区列表
      communityList: [],
      // 加载状态
      communityLoading: false,
      // 搜索关键字
      searchKeyword: ''
    }
  },
  watch: {
    // 监听value变化，确保选中的小区在列表中
    value: {
      handler(newVal) {
        if (newVal && this.communityList.length > 0) {
          const selectedCommunity = this.communityList.find(community => community.id === newVal);
          if (!selectedCommunity) {
            // 如果选中的小区不在当前列表中，尝试加载
            this.loadSelectedCommunity(newVal);
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // 初始加载小区列表
    this.loadInitialCommunityList();
  },
  methods: {
    /** 初始加载小区列表 */
    loadInitialCommunityList() {
      const searchParams = {
        pageNum: 1,
        pageSize: this.initialSize
      };
      
      listCommunityMangement(searchParams).then(response => {
        this.communityList = response.rows || [];
      });
    },
    
    /** 远程搜索小区 */
    remoteCommunitySearch(query) {
      this.searchKeyword = query;
      
      if (query !== '') {
        this.communityLoading = true;
        searchCommunityMangement(query).then(response => {
          this.communityList = response.rows || response.data || [];
          this.communityLoading = false;
        }).catch(() => {
          this.communityLoading = false;
        });
      } else {
        // 搜索关键字为空时，重新加载初始列表
        this.loadInitialCommunityList();
      }
    },
    
    /** 加载选中的小区（确保选中项在列表中） */
    loadSelectedCommunity(communityId) {
      if (!communityId) return;
      
      listCommunityMangement({
        pageNum: 1,
        pageSize: 1,
        id: communityId
      }).then(response => {
        if (response.rows && response.rows.length > 0) {
          const selectedCommunity = response.rows[0];
          const exists = this.communityList.find(community => community.id === selectedCommunity.id);
          if (!exists) {
            this.communityList.unshift(selectedCommunity);
          }
        }
      });
    },
    
    /** 处理输入事件 */
    handleInput(value) {
      this.$emit('input', value);
    },
    
    /** 处理变化事件 */
    handleChange(value) {
      const selectedCommunity = this.communityList.find(community => community.id === value);
      this.$emit('change', value, selectedCommunity);
    },
    
    /** 处理清空事件 */
    handleClear() {
      this.communityList = [];
      this.searchKeyword = '';
      this.loadInitialCommunityList();
      this.$emit('input', null);
      this.$emit('change', null, null);
      this.$emit('clear');
    },
    
    /** 处理下拉框显示状态变化 */
    handleSelectVisible(visible) {
      if (visible && this.communityList.length === 0) {
        this.loadInitialCommunityList();
      }
    }
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
