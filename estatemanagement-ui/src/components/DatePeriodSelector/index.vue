<template>
  <div class="date-period-selector">
    <el-form-item :label="startDateLabel" :prop="startDateProp">
      <el-date-picker 
        clearable
        :value="startDate"
        @input="handleStartDateChange"
        type="date"
        value-format="yyyy-MM-dd"
        :placeholder="startDatePlaceholder">
      </el-date-picker>
    </el-form-item>
    
    <el-form-item :label="periodLabel" v-if="startDate && showPeriodSelector">
      <el-radio-group :value="selectedPeriod" @input="handlePeriodChange">
        <el-radio v-for="option in periodOptions" :key="option.value" :label="option.value">
          {{ option.label }}
        </el-radio>
      </el-radio-group>
      <div style="font-size: 12px; color: #999; margin-top: 4px;">
        {{ periodTip }}
      </div>
    </el-form-item>
    
    <el-form-item :label="endDateLabel" :prop="endDateProp" v-if="showEndDate">
      <el-date-picker 
        clearable
        :value="endDate"
        @input="handleEndDateChange"
        type="date"
        value-format="yyyy-MM-dd"
        :placeholder="endDatePlaceholder">
      </el-date-picker>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'DatePeriodSelector',
  props: {
    // 开始日期
    startDate: {
      type: String,
      default: null
    },
    // 结束日期
    endDate: {
      type: String,
      default: null
    },
    // 选择的期间（月数）
    selectedPeriod: {
      type: Number,
      default: 1
    },
    // 是否显示期间选择器
    showPeriodSelector: {
      type: Boolean,
      default: true
    },
    // 是否显示结束日期
    showEndDate: {
      type: Boolean,
      default: true
    },
    // 期间选项
    periodOptions: {
      type: Array,
      default: () => [
        { value: 1, label: '1个月' },
        { value: 3, label: '3个月' },
        { value: 6, label: '6个月' },
        { value: 12, label: '12个月' }
      ]
    },
    // 标签文本
    startDateLabel: {
      type: String,
      default: '开始日期'
    },
    endDateLabel: {
      type: String,
      default: '结束日期'
    },
    periodLabel: {
      type: String,
      default: '租期选择'
    },
    // 属性名
    startDateProp: {
      type: String,
      default: 'startDate'
    },
    endDateProp: {
      type: String,
      default: 'endDate'
    },
    // 占位符
    startDatePlaceholder: {
      type: String,
      default: '请选择开始日期'
    },
    endDatePlaceholder: {
      type: String,
      default: '请选择结束日期'
    },
    // 提示文本
    periodTip: {
      type: String,
      default: '选择租期后将自动计算结束日期（结束日期 = 开始日期 + 租期 - 1天）'
    }
  },
  methods: {
    /** 开始日期变化处理 */
    handleStartDateChange(date) {
      this.$emit('update:startDate', date)
      this.$emit('start-date-change', date)
      
      if (date) {
        // 如果显示期间选择器，则根据期间计算结束日期
        // 如果不显示期间选择器（新增模式），则结束日期等于开始日期
        if (this.showPeriodSelector) {
          this.calculateEndDate(date, this.selectedPeriod)
        } else {
          this.$emit('update:endDate', date)
          this.$emit('end-date-change', date)
          this.$emit('date-calculated', {
            startDate: date,
            endDate: date,
            period: 0
          })
        }
      } else {
        this.$emit('update:endDate', null)
        this.$emit('end-date-change', null)
      }
    },
    
    /** 结束日期变化处理 */
    handleEndDateChange(date) {
      this.$emit('update:endDate', date)
      this.$emit('end-date-change', date)
    },
    
    /** 期间变化处理 */
    handlePeriodChange(period) {
      this.$emit('update:selectedPeriod', period)
      this.$emit('period-change', period)
      
      if (this.startDate) {
        this.calculateEndDate(this.startDate, period)
      }
    },
    
    /** 计算结束日期 */
    calculateEndDate(startDate, period) {
      if (!startDate || !period) return
      
      const start = new Date(startDate)
      const end = new Date(start)
      
      // 添加指定的月数
      end.setMonth(end.getMonth() + period)
      // 减去一天
      end.setDate(end.getDate() - 1)
      
      // 格式化为 yyyy-MM-dd
      const year = end.getFullYear()
      const month = String(end.getMonth() + 1).padStart(2, '0')
      const day = String(end.getDate()).padStart(2, '0')
      
      const endDateStr = `${year}-${month}-${day}`
      
      this.$emit('update:endDate', endDateStr)
      this.$emit('end-date-change', endDateStr)
      this.$emit('date-calculated', {
        startDate: startDate,
        endDate: endDateStr,
        period: period
      })
    }
  }
}
</script>

<style scoped>
.date-period-selector {
  /* 组件样式 */
}
</style> 