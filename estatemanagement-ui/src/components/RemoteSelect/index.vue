<template>
  <el-select
    v-model="currentValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :filterable="filterable"
    :remote="remote"
    :reserve-keyword="reserveKeyword"
    :remote-method="handleRemoteMethod"
    :loading="loading"
    :disabled="disabled"
    v-el-select-loadmore="loadMore"
    @change="handleChange"
    @clear="handleClear"
    @visible-change="handleVisibleChange">
    <el-option
      v-for="item in options"
      :key="item[valueKey]"
      :label="item[labelKey]"
      :value="item[valueKey]">
    </el-option>
    <el-option
      v-if="loading && options.length > 0"
      :value="'loading'"
      disabled
      style="text-align: center;">
      <i class="el-icon-loading"></i> 加载中...
    </el-option>
    <el-option
      v-if="!hasMore && options.length > 0 && !loading"
      :value="'no-more'"
      disabled
      style="text-align: center; color: #999;">
      没有更多数据了
    </el-option>
  </el-select>
</template>

<script>
import elSelectLoadmore from '@/directive/el-select-loadmore'

export default {
  name: 'RemoteSelect',
  directives: {
    elSelectLoadmore
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    remote: {
      type: Boolean,
      default: true
    },
    reserveKeyword: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // API相关
    api: {
      type: Function,
      required: true
    },
    // 额外的查询参数
    queryParams: {
      type: Object,
      default: () => ({})
    },
    // 数据字段映射
    valueKey: {
      type: String,
      default: 'id'
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    // 分页配置
    pageSize: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      currentValue: this.value,
      options: [],
      loading: false,
      currentPage: 1,
      hasMore: true,
      currentKeyword: ''
    }
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal
    }
  },
  methods: {
    // 远程搜索方法
    handleRemoteMethod(query) {
      this.currentKeyword = query
      this.currentPage = 1
      this.hasMore = true
      this.loadData(true)
    },
    
    // 加载数据
    async loadData(reset = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          ...this.queryParams
        }
        
        // 如果有搜索关键字，添加到参数中
        if (this.currentKeyword) {
          params.keyword = this.currentKeyword
        }
        
        const response = await this.api(params)
        const data = response.rows || response.data || []
        
        if (reset) {
          this.options = data
        } else {
          this.options = [...this.options, ...data]
        }
        
        // 判断是否还有更多数据
        this.hasMore = data.length === this.pageSize
        
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return
      
      this.currentPage++
      this.loadData()
    },
    
    // 值改变事件
    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },
    
    // 清空事件
    handleClear() {
      this.options = []
      this.currentPage = 1
      this.hasMore = true
      this.currentKeyword = ''
      this.$emit('clear')
    },
    
    // 下拉框显示/隐藏事件
    handleVisibleChange(visible) {
      if (visible && this.options.length === 0) {
        this.loadData(true)
      }
    }
  }
}
</script>
