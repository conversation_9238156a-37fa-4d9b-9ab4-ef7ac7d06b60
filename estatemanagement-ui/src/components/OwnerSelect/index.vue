<template>
  <el-select 
    :value="value" 
    :placeholder="placeholder" 
    :clearable="clearable"
    :disabled="disabled"
    :size="size"
    filterable
    remote
    reserve-keyword
    :remote-method="remoteOwnerSearch"
    :loading="ownerLoading"
    v-el-select-loadmore="loadMoreOwners"
    @input="handleInput"
    @change="handleChange"
    @clear="handleClear"
    @visible-change="handleSelectVisible">
    
    <!-- 业主选项 -->
    <el-option
      v-for="owner in ownerList"
      :key="owner.id"
      :label="getOwnerLabel(owner)"
      :value="owner.id">
    </el-option>
    
    <!-- 加载中提示 -->
    <el-option
      v-if="ownerLoadingMore"
      :value="'loading'"
      disabled
      style="text-align: center; padding: 8px 0;">
      <i class="el-icon-loading" style="margin-right: 5px;"></i>
      <span style="color: #999; font-size: 12px;">加载中...</span>
    </el-option>
    
    <!-- 无更多数据提示 -->
    <el-option
      v-if="!ownerHasMore && ownerList.length > 0 && !ownerLoadingMore"
      :value="'no-more'"
      disabled
      style="text-align: center; padding: 8px 0; color: #999; font-size: 12px;">
      没有更多数据了
    </el-option>
    
    <!-- 无数据提示 -->
    <el-option
      v-if="ownerList.length === 0 && !ownerLoading && communityId"
      :value="'no-data'"
      disabled
      style="text-align: center; padding: 8px 0; color: #999; font-size: 12px;">
      暂无业主数据
    </el-option>
    
    <!-- 未选择小区提示 -->
    <el-option
      v-if="!communityId && !ownerLoading"
      :value="'no-community'"
      disabled
      style="text-align: center; padding: 8px 0; color: #999; font-size: 12px;">
      请先选择小区
    </el-option>
  </el-select>
</template>

<script>
import { listOwnerMangement, searchOwnerMangement } from "@/api/system/ownerMangement";
import elSelectLoadmore from '@/directive/el-select-loadmore';

export default {
  name: "OwnerSelect",
  directives: {
    'el-select-loadmore': elSelectLoadmore
  },
  props: {
    // v-model 绑定的值
    value: {
      type: [String, Number],
      default: null
    },
    // 小区ID，必须传入
    communityId: {
      type: [String, Number],
      default: null
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择业主'
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 尺寸
    size: {
      type: String,
      default: ''
    },
    // 每页数量
    pageSize: {
      type: Number,
      default: 20
    },
    // 标签显示格式
    labelFormat: {
      type: String,
      default: 'name-address', // 'name-address' | 'name-only' | 'custom'
      validator: value => ['name-address', 'name-only', 'custom'].includes(value)
    },
    // 自定义标签格式化函数
    customLabelFormatter: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      // 业主列表
      ownerList: [],
      // 加载状态
      ownerLoading: false,
      ownerLoadingMore: false,
      // 分页相关
      ownerCurrentPage: 1,
      ownerHasMore: true,
      ownerSearchKeyword: ''
    }
  },
  watch: {
    // 监听小区变化
    communityId: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.resetData();
          if (newVal) {
            this.loadOwnerList(true);
          }
        }
      },
      immediate: true
    },
    // 监听value变化，确保选中的业主在列表中
    value: {
      handler(newVal) {
        if (newVal && this.communityId && this.ownerList.length > 0) {
          const selectedOwner = this.ownerList.find(owner => owner.id === newVal);
          if (!selectedOwner) {
            // 如果选中的业主不在当前列表中，尝试加载
            this.loadSelectedOwner(newVal);
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 重置数据 */
    resetData() {
      this.ownerList = [];
      this.ownerCurrentPage = 1;
      this.ownerHasMore = true;
      this.ownerSearchKeyword = '';
      this.ownerLoading = false;
      this.ownerLoadingMore = false;
    },
    
    /** 加载业主列表 */
    loadOwnerList(reset = true) {
      if (!this.communityId) {
        this.ownerList = [];
        return;
      }
      
      if (reset) {
        this.ownerCurrentPage = 1;
        this.ownerHasMore = true;
        this.ownerSearchKeyword = '';
      }
      
      const searchParams = {
        pageNum: this.ownerCurrentPage,
        pageSize: this.pageSize,
        communityId: this.communityId
      };
      
      if (this.ownerSearchKeyword) {
        searchParams.ownerName = this.ownerSearchKeyword;
      }
      
      this.ownerLoading = reset;
      
      listOwnerMangement(searchParams).then(response => {
        this.$nextTick(() => {
          if (reset) {
            this.ownerList = response.rows || [];
          } else {
            this.ownerList = [...this.ownerList, ...(response.rows || [])];
          }
          
          this.ownerHasMore = response.rows && response.rows.length === this.pageSize;
          this.ownerLoading = false;
        });
      }).catch(() => {
        this.ownerLoading = false;
      });
    },
    
    /** 远程搜索业主 */
    remoteOwnerSearch(query) {
      if (!this.communityId) {
        this.$message.warning('请先选择小区');
        return;
      }
      
      this.ownerSearchKeyword = query;
      this.ownerCurrentPage = 1;
      this.ownerHasMore = true;
      
      if (query !== '') {
        this.ownerLoading = true;
        searchOwnerMangement(this.communityId, query).then(response => {
          this.ownerList = response.rows || [];
          this.ownerHasMore = response.rows && response.rows.length === this.pageSize;
          this.ownerLoading = false;
        }).catch(() => {
          this.ownerLoading = false;
        });
      } else {
        this.loadOwnerList(true);
      }
    },
    
    /** 加载更多业主数据 */
    loadMoreOwners() {
      if (this.ownerLoadingMore || !this.ownerHasMore || !this.communityId) {
        return;
      }
      
      this.ownerCurrentPage++;
      this.ownerLoadingMore = true;
      
      const searchParams = {
        pageNum: this.ownerCurrentPage,
        pageSize: this.pageSize,
        communityId: this.communityId
      };
      
      if (this.ownerSearchKeyword) {
        searchParams.ownerName = this.ownerSearchKeyword;
      }
      
      const apiMethod = this.ownerSearchKeyword ? 
        searchOwnerMangement(this.communityId, this.ownerSearchKeyword) : 
        listOwnerMangement(searchParams);
        
      apiMethod.then(response => {
        const newData = response.rows || [];
        if (newData.length > 0) {
          this.$nextTick(() => {
            this.ownerList = [...this.ownerList, ...newData];
            this.ownerHasMore = newData.length === this.pageSize;
            this.ownerLoadingMore = false;
          });
        } else {
          this.ownerHasMore = false;
          this.ownerLoadingMore = false;
        }
      }).catch(() => {
        this.ownerCurrentPage--;
        this.ownerLoadingMore = false;
      });
    },
    
    /** 加载选中的业主（确保选中项在列表中） */
    loadSelectedOwner(ownerId) {
      if (!ownerId || !this.communityId) return;
      
      listOwnerMangement({
        pageNum: 1,
        pageSize: 1,
        communityId: this.communityId,
        id: ownerId
      }).then(response => {
        if (response.rows && response.rows.length > 0) {
          const selectedOwner = response.rows[0];
          const exists = this.ownerList.find(owner => owner.id === selectedOwner.id);
          if (!exists) {
            this.ownerList.unshift(selectedOwner);
          }
        }
      });
    },
    
    /** 获取业主标签显示文本 */
    getOwnerLabel(owner) {
      if (this.customLabelFormatter && typeof this.customLabelFormatter === 'function') {
        return this.customLabelFormatter(owner);
      }
      
      switch (this.labelFormat) {
        case 'name-only':
          return owner.ownerName || '未知业主';
        case 'name-address':
        default:
          return `${owner.ownerName || '未知业主'} (${owner.buildingNumber || ''}栋${owner.houseNumber || ''})`;
      }
    },
    
    /** 处理输入事件 */
    handleInput(value) {
      this.$emit('input', value);
    },
    
    /** 处理变化事件 */
    handleChange(value) {
      const selectedOwner = this.ownerList.find(owner => owner.id === value);
      this.$emit('change', value, selectedOwner);
    },
    
    /** 处理清空事件 */
    handleClear() {
      this.resetData();
      this.$emit('input', null);
      this.$emit('change', null, null);
      this.$emit('clear');
    },
    
    /** 处理下拉框显示状态变化 */
    handleSelectVisible(visible) {
      if (visible && this.communityId && this.ownerList.length === 0) {
        this.loadOwnerList(true);
      }
    }
  }
}
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
