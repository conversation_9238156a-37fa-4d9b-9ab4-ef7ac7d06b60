<template>
  <div>
    <!-- 单个停车催缴单 -->
    <div v-if="parkingData" class="parking-notice">
      <div class="title">停车费催缴通知单</div>

      <!-- 基本信息 -->
      <table class="info-table">
        <tr>
          <td><strong>小区名称：</strong>{{ parkingData.communityName || '' }}</td>
          <td><strong>业主姓名：</strong>{{ parkingData.ownerName || '' }}</td>
        </tr>
        <tr>
          <td><strong>房号：</strong>{{ parkingData.buildingNumber || '' }}-{{ parkingData.houseNumber || '' }}</td>
          <td></td>
        </tr>
      </table>

      <!-- 费用信息 -->
      <div class="subtitle">费用信息</div>
      <table>
        <thead>
          <tr>
            <th class="text-center">车牌号</th>
            <th class="text-center">车位号</th>
            <th class="text-center">到期时间</th>
            <th class="text-center">月租金(元)</th>
            <th class="text-center">三证合一</th>
          </tr>
        </thead>
        <tbody>
          <!-- 如果是分组数据（有parkingRecords数组） -->
          <template v-if="parkingData.parkingRecords && parkingData.parkingRecords.length > 0">
            <tr v-for="record in parkingData.parkingRecords" :key="record.id">
              <td class="text-center">{{ record.plateNumber || '' }}</td>
              <td class="text-center">{{ record.spaceNumber || '' }}</td>
              <td class="text-center">{{ formatDate(record.endDate) }}</td>
              <td class="text-right">{{ record.monthlyFee || 0 }}</td>
              <td class="text-center">{{ record.isThreeCertificates === 1 ? '是' : '否' }}</td>
            </tr>
          </template>
          <!-- 如果是单条记录 -->
          <template v-else>
            <tr>
              <td class="text-center">{{ parkingData.plateNumber || '' }}</td>
              <td class="text-center">{{ parkingData.spaceNumber || '' }}</td>
              <td class="text-center">{{ formatDate(parkingData.endDate) }}</td>
              <td class="text-right">{{ parkingData.monthlyFee || 0 }}</td>
              <td class="text-center">{{ parkingData.isThreeCertificates === 1 ? '是' : '否' }}</td>
            </tr>
          </template>
        </tbody>
      </table>

      <!-- 催缴说明 -->
      <div class="subtitle">催缴说明：</div>
      <p>请您在收到本通知单后尽快到物业管理处缴纳停车费用。如有疑问，请及时与物业管理处联系。</p>

      <!-- 签名区域 -->
      <div class="signature-area">
        <div class="contact-info">
          <span>物业电话：024-31502355</span>
        </div>
        <div class="date-seal-container">
          <span>日期：{{ getCurrentDate() }}</span>
          <div class="seal-container">
            <span class="seal-text">物业管理处</span>
            <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
          </div>
        </div>
      </div>
    </div>

    <!-- 批量停车催缴单 -->
    <div v-if="parkingList && parkingList.length > 0">
      <div v-for="(parking, index) in parkingList" :key="parking.ownerId || parking.id" :class="{ 'print-break': index > 0 }">
        <div class="title">停车费催缴通知单</div>

        <!-- 基本信息 -->
        <table class="info-table">
          <tr>
            <td><strong>小区名称：</strong>{{ parking.communityName || '' }}</td>
            <td><strong>业主姓名：</strong>{{ parking.ownerName || '' }}</td>
          </tr>
          <tr>
            <td><strong>房号：</strong>{{ parking.buildingNumber || '' }}-{{ parking.houseNumber || '' }}</td>
            <td></td>
          </tr>
        </table>

        <!-- 费用信息 -->
        <div class="subtitle">费用信息</div>
        <table>
          <thead>
            <tr>
              <th class="text-center">车牌号</th>
              <th class="text-center">车位号</th>
              <th class="text-center">到期时间</th>
              <th class="text-center">月租金(元)</th>
              <th class="text-center">三证合一</th>
            </tr>
          </thead>
          <tbody>
            <!-- 如果是分组数据（有parkingRecords数组） -->
            <template v-if="parking.parkingRecords && parking.parkingRecords.length > 0">
              <tr v-for="record in parking.parkingRecords" :key="record.id">
                <td class="text-center">{{ record.plateNumber || '' }}</td>
                <td class="text-center">{{ record.spaceNumber || '' }}</td>
                <td class="text-center">{{ formatDate(record.endDate) }}</td>
                <td class="text-right">{{ record.monthlyFee || 0 }}</td>
                <td class="text-center">{{ record.isThreeCertificates === 1 ? '是' : '否' }}</td>
              </tr>
            </template>
            <!-- 如果是单条记录 -->
            <template v-else>
              <tr>
                <td class="text-center">{{ parking.plateNumber || '' }}</td>
                <td class="text-center">{{ parking.spaceNumber || '' }}</td>
                <td class="text-center">{{ formatDate(parking.endDate) }}</td>
                <td class="text-right">{{ parking.monthlyFee || 0 }}</td>
                <td class="text-center">{{ parking.isThreeCertificates === 1 ? '是' : '否' }}</td>
              </tr>
            </template>
          </tbody>
        </table>

        <!-- 催缴说明 -->
        <div class="subtitle">催缴说明：</div>
        <p>请您在收到本通知单后尽快到物业管理处缴纳停车费用。如有疑问，请及时与物业管理处联系。</p>

        <!-- 签名区域 -->
        <div class="signature-area">
          <div class="contact-info">
            <span>物业电话：024-31502355</span>
          </div>
          <div class="date-seal-container">
            <span>日期：{{ getCurrentDate() }}</span>
            <div class="seal-container">
              <span class="seal-text">物业管理处</span>
              <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ParkingNotice',
  props: {
    parkingData: {
      type: Object,
      default: null
    },
    parkingList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}年${(d.getMonth() + 1).toString().padStart(2, '0')}月${d.getDate().toString().padStart(2, '0')}日`
    },
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      const hours = d.getHours().toString().padStart(2, '0')
      const minutes = d.getMinutes().toString().padStart(2, '0')
      const seconds = d.getSeconds().toString().padStart(2, '0')
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
    },
    getCurrentDate() {
      const now = new Date()
      return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
    },
    getFeeTypeName(isThreeCertificates) {
      return isThreeCertificates === 1 ? '三证合一停车费' : '非三证合一停车费'
    },
    getParkingField(parkingData, fieldName) {
      if (!parkingData) return ''
      return parkingData[fieldName] || ''
    }
  }
}
</script>

<style scoped>
.parking-notice {
  padding: 15px;
  margin: 0 auto;
  max-width: 95%;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  background: white;
  page-break-after: always;
  box-sizing: border-box;
}

.parking-notice:last-child {
  page-break-after: auto;
}

.date-seal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.seal-container {
  display: inline-block;
  position: relative;
  margin-top: 5px;
}

.seal-text {
  font-weight: bold;
  position: relative;
  z-index: 2;
  color: #333;
  display: inline-block;
}

.seal-image {
  width: 45mm;
  height: 45mm;
  opacity: 0.8;
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 30px;
  min-height: 60px;
}

.contact-info {
  align-self: flex-end;
  font-size: 12px;
  color: #666;
}

@media print {
  .seal-image {
    width: 40mm;
    height: 40mm;
    top: -35px;
  }
}
</style>
