<template>
  <div>
    <!-- 打印内容区域 -->
    <div ref="printContent" class="print-content" v-show="false">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PrintComponent',
  methods: {
    // 打印方法
    print() {
      const printContent = this.$refs.printContent.innerHTML

      // 创建隐藏的iframe进行打印
      const iframe = document.createElement('iframe')
      iframe.style.position = 'absolute'
      iframe.style.top = '-9999px'
      iframe.style.left = '-9999px'
      iframe.style.width = '0px'
      iframe.style.height = '0px'
      iframe.style.border = 'none'
      document.body.appendChild(iframe)

      const iframeDoc = iframe.contentDocument || iframe.contentWindow.document

      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>打印</title>
          <style>
            @media print {
              @page {
                margin: 25mm 20mm;
                size: A4;
              }
              body {
                font-family: "Microsoft YaHei", Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
                margin: 0 auto;
                padding: 0;
                max-width: 90%;
                transform: scale(0.95);
                transform-origin: top center;
              }
              .no-print {
                display: none !important;
              }
              .print-break {
                page-break-before: always;
              }
              table {
                width: 95%;
                max-width: 95%;
                border-collapse: collapse;
                margin: 8px auto;
                table-layout: fixed;
              }
              table, th, td {
                border: 1px solid #333;
              }
              th, td {
                padding: 3px 5px;
                text-align: left;
                font-size: 11px;
                word-wrap: break-word;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              th {
                background-color: #f5f5f5;
                font-weight: bold;
              }
              .text-center {
                text-align: center;
              }
              .text-right {
                text-align: right;
              }
              .font-bold {
                font-weight: bold;
              }
              .title {
                font-size: 18px;
                font-weight: bold;
                text-align: center;
                margin: 15px 0;
              }
              .subtitle {
                font-size: 14px;
                font-weight: bold;
                margin: 12px 0 8px 0;
              }
              .info-table {
                border: none;
                margin: 10px auto;
                width: 95%;
                max-width: 95%;
              }
              .info-table td {
                border: none;
                padding: 3px 5px;
                font-size: 11px;
                word-wrap: break-word;
                overflow: hidden;
                text-overflow: ellipsis;
              }
              .signature-area {
                margin-top: 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                max-width: 100%;
              }
              .seal {
                font-weight: bold;
                color: #333;
              }
              .date-seal-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                min-height: 50px;
              }
              .seal-container {
                display: inline-block;
                position: relative;
                margin-top: 8px;
              }
              .seal-text {
                font-weight: bold;
                position: relative;
                z-index: 2;
                color: #333;
                font-size: 12px;
                display: inline-block;
              }
              .seal-image {
                width: 40mm;
                height: 40mm;
                opacity: 0.8;
                position: absolute;
                top: -35px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1;
              }
              p {
                margin: 6px 0;
                font-size: 12px;
              }
            }
            @media screen {
              body {
                font-family: "Microsoft YaHei", Arial, sans-serif;
                font-size: 14px;
                line-height: 1.6;
                color: #333;
                margin: 20px;
                padding: 0;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
              }
              table, th, td {
                border: 1px solid #ddd;
              }
              th, td {
                padding: 8px;
                text-align: left;
              }
              th {
                background-color: #f5f5f5;
                font-weight: bold;
              }
              .text-center {
                text-align: center;
              }
              .text-right {
                text-align: right;
              }
              .font-bold {
                font-weight: bold;
              }
              .title {
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                margin: 20px 0;
              }
              .subtitle {
                font-size: 16px;
                font-weight: bold;
                margin: 15px 0 10px 0;
              }
              .info-table {
                border: none;
                margin: 15px 0;
              }
              .info-table td {
                border: none;
                padding: 5px 10px;
              }
              .signature-area {
                margin-top: 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
              .seal {
                font-weight: bold;
                color: #333;
              }
            }
          </style>
        </head>
        <body>
          ${printContent}
        </body>
        </html>
      `)

      iframeDoc.close()

      // 等待内容加载完成后隐式打印
      setTimeout(() => {
        iframe.contentWindow.focus()
        iframe.contentWindow.print()

        // 打印完成后移除iframe
        setTimeout(() => {
          document.body.removeChild(iframe)
        }, 1000)
      }, 500)
    }
  }
}
</script>

<style scoped>
.print-content {
  display: none;
}
</style>
