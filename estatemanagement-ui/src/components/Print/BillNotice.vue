<template>
  <div>
    <!-- 单个催缴单 -->
    <div v-if="billData" class="bill-notice">
      <div class="title">物业费催缴通知单</div>

      <!-- 基本信息 -->
      <table class="info-table">
        <tr>
          <td><strong>小区名称：</strong>{{ getBillField(billData, 'communityName') }}</td>
          <td><strong>账单编号：</strong>{{ getBillField(billData, 'billNumber') }}</td>
        </tr>
        <tr>
          <td><strong>业主姓名：</strong>{{ getBillField(billData, 'ownerName') }}</td>
          <td><strong>房号：</strong>{{ getBillField(billData, 'buildingNumber') + '-' + getBillField(billData, 'houseNumber') }}</td>
        </tr>
      </table>

      <!-- 费用明细 -->
      <div class="subtitle">费用明细</div>
      <table>
        <thead>
          <tr>
            <th class="text-center">费用项目</th>
            <th class="text-center">账单周期</th>
            <th class="text-center">金额(元)</th>
            <th class="text-center">备注</th>
          </tr>
        </thead>
        <tbody>
          <!-- 使用明细数据显示费用项目和对应的账单周期 -->
          <template v-if="billData && billData.details">
            <tr v-for="detail in billData.details" :key="detail.id" v-if="detail.amount && detail.amount > 0">
              <td>
                {{ detail.feeName }}
                <span v-if="detail.feeType == 2 && detail.plateNumber" style="color: #666; font-size: 11px;">
                  （{{ detail.plateNumber }}）
                </span>
              </td>
              <td class="text-center">{{ formatDate(detail.billPeriodStart) }} 至 {{ formatDate(detail.billPeriodEnd) }}</td>
              <td class="text-right">{{ detail.amount }}</td>
              <td>{{ detail.remark || '' }}</td>
            </tr>
          </template>
          <!-- 如果没有明细数据，使用原来的显示方式 -->
          <template v-else>
            <tr v-if="getBillField(billData, 'propertyFee') && getBillField(billData, 'propertyFee') > 0">
              <td>物业费</td>
              <td class="text-center">{{ formatDate(getBillField(billData, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(billData, 'billPeriodEnd')) }}</td>
              <td class="text-right">{{ getBillField(billData, 'propertyFee') }}</td>
              <td></td>
            </tr>
            <tr v-if="getBillField(billData, 'parkingFee') && getBillField(billData, 'parkingFee') > 0">
              <td>停车费</td>
              <td class="text-center">{{ formatDate(getBillField(billData, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(billData, 'billPeriodEnd')) }}</td>
              <td class="text-right">{{ getBillField(billData, 'parkingFee') }}</td>
              <td></td>
            </tr>
            <tr v-if="getBillField(billData, 'sanitationFee') && getBillField(billData, 'sanitationFee') > 0">
              <td>卫生费</td>
              <td class="text-center">{{ formatDate(getBillField(billData, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(billData, 'billPeriodEnd')) }}</td>
              <td class="text-right">{{ getBillField(billData, 'sanitationFee') }}</td>
              <td></td>
            </tr>
            <tr v-if="getBillField(billData, 'elevatorFee') && getBillField(billData, 'elevatorFee') > 0">
              <td>电梯费</td>
              <td class="text-center">{{ formatDate(getBillField(billData, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(billData, 'billPeriodEnd')) }}</td>
              <td class="text-right">{{ getBillField(billData, 'elevatorFee') }}</td>
              <td></td>
            </tr>
            <tr v-if="getBillField(billData, 'lateFee') && getBillField(billData, 'lateFee') > 0">
              <td>滞纳金</td>
              <td class="text-center">{{ formatDate(getBillField(billData, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(billData, 'billPeriodEnd')) }}</td>
              <td class="text-right">{{ getBillField(billData, 'lateFee') }}</td>
              <td></td>
            </tr>
          </template>
          <tr class="font-bold">
            <td>合计</td>
            <td></td>
            <td class="text-right">{{ getBillField(billData, 'totalAmount') }}</td>
            <td></td>
          </tr>
        </tbody>
      </table>

      <!-- 催缴说明 -->
      <div class="subtitle">催缴说明：</div>
      <p>请您在收到本通知单后及时到物业管理处缴纳相关费用。如有疑问，请及时与物业管理处联系。</p>

      <!-- 签名区域 -->
      <div class="signature-area">
        <div></div>
        <div class="date-seal-container">
          <span>日期：{{ getCurrentDate() }}</span>
          <div class="seal-container">
            <span class="seal-text">物业管理处</span>
            <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
          </div>
        </div>
      </div>
    </div>

    <!-- 批量催缴单 -->
    <div v-if="billList && billList.length > 0">
      <div v-for="(bill, index) in billList" :key="bill.id" :class="{ 'print-break': index > 0 }">
        <div class="title">物业费催缴通知单</div>

        <!-- 基本信息 -->
        <table class="info-table">
          <tr>
            <td><strong>小区名称：</strong>{{ getBillField(bill, 'communityName') }}</td>
            <td><strong>账单编号：</strong>{{ getBillField(bill, 'billNumber') }}</td>
          </tr>
          <tr>
            <td><strong>业主姓名：</strong>{{ getBillField(bill, 'ownerName') }}</td>
            <td><strong>房号：</strong>{{ getBillField(bill, 'buildingNumber') + '-' + getBillField(bill, 'houseNumber') }}</td>
          </tr>
          <tr>
            <td colspan="2"><strong>账单周期：</strong>{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
          </tr>
        </table>

        <!-- 费用明细 -->
        <div class="subtitle">费用明细</div>
        <table>
          <thead>
            <tr>
              <th class="text-center">费用项目</th>
              <th class="text-center">账单周期</th>
              <th class="text-center">金额(元)</th>
              <th class="text-center">备注</th>
            </tr>
          </thead>
          <tbody>
            <!-- 使用明细数据显示费用项目和对应的账单周期 -->
            <template v-if="bill && bill.details">
              <tr v-for="detail in bill.details" :key="detail.id" v-if="detail.amount && detail.amount > 0">
                <td>
                  {{ detail.feeName }}
                  <span v-if="detail.feeType == 2 && detail.plateNumber" style="color: #666; font-size: 11px;">
                    （{{ detail.plateNumber }}）
                  </span>
                </td>
                <td class="text-center">{{ formatDate(detail.billPeriodStart) }} 至 {{ formatDate(detail.billPeriodEnd) }}</td>
                <td class="text-right">{{ detail.amount }}</td>
                <td>{{ detail.remark || '' }}</td>
              </tr>
            </template>
            <!-- 如果没有明细数据，使用原来的显示方式 -->
            <template v-else>
              <tr v-if="getBillField(bill, 'propertyFee') && getBillField(bill, 'propertyFee') > 0">
                <td>物业费</td>
                <td class="text-center">{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
                <td class="text-right">{{ getBillField(bill, 'propertyFee') }}</td>
                <td></td>
              </tr>
              <tr v-if="getBillField(bill, 'parkingFee') && getBillField(bill, 'parkingFee') > 0">
                <td>停车费</td>
                <td class="text-center">{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
                <td class="text-right">{{ getBillField(bill, 'parkingFee') }}</td>
                <td></td>
              </tr>
              <tr v-if="getBillField(bill, 'sanitationFee') && getBillField(bill, 'sanitationFee') > 0">
                <td>卫生费</td>
                <td class="text-center">{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
                <td class="text-right">{{ getBillField(bill, 'sanitationFee') }}</td>
                <td></td>
              </tr>
              <tr v-if="getBillField(bill, 'elevatorFee') && getBillField(bill, 'elevatorFee') > 0">
                <td>电梯费</td>
                <td class="text-center">{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
                <td class="text-right">{{ getBillField(bill, 'elevatorFee') }}</td>
                <td></td>
              </tr>
              <tr v-if="getBillField(bill, 'lateFee') && getBillField(bill, 'lateFee') > 0">
                <td>滞纳金</td>
                <td class="text-center">{{ formatDate(getBillField(bill, 'billPeriodStart')) }} 至 {{ formatDate(getBillField(bill, 'billPeriodEnd')) }}</td>
                <td class="text-right">{{ getBillField(bill, 'lateFee') }}</td>
                <td></td>
              </tr>
            </template>
            <tr class="font-bold">
              <td>合计</td>
              <td></td>
              <td class="text-right">{{ getBillField(bill, 'totalAmount') }}</td>
              <td></td>
            </tr>
          </tbody>
        </table>

        <!-- 催缴说明 -->
        <div class="subtitle">催缴说明：</div>
        <p>请您在收到本通知单后及时到物业管理处缴纳相关费用。如有疑问，请及时与物业管理处联系。</p>

        <!-- 签名区域 -->
        <div class="signature-area">
          <div></div>
          <div class="date-seal-container">
            <span>日期：{{ getCurrentDate() }}</span>
            <div class="seal-container">
              <span class="seal-text">物业管理处</span>
              <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BillNotice',
  props: {
    billData: {
      type: Object,
      default: null
    },
    billList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}年${(d.getMonth() + 1).toString().padStart(2, '0')}月${d.getDate().toString().padStart(2, '0')}日`
    },
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      const hours = d.getHours().toString().padStart(2, '0')
      const minutes = d.getMinutes().toString().padStart(2, '0')
      const seconds = d.getSeconds().toString().padStart(2, '0')
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
    },
    getCurrentDate() {
      const now = new Date()
      return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
    },
    getBillField(billData, fieldName) {
      if (!billData) return ''
      // 如果billData有bill属性，说明是从API返回的包装对象
      if (billData.bill) {
        return billData.bill[fieldName] || ''
      }
      // 否则直接访问字段
      return billData[fieldName] || ''
    }
  }
}
</script>

<style scoped>
.bill-notice {
  padding: 15px;
  margin: 0 auto;
  max-width: 95%;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  background: white;
  page-break-after: always;
  box-sizing: border-box;
}

.bill-notice:last-child {
  page-break-after: auto;
}

.date-seal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.seal-container {
  display: inline-block;
  position: relative;
  margin-top: 5px;
}

.seal-text {
  font-weight: bold;
  position: relative;
  z-index: 2;
  color: #333;
  display: inline-block;
}

.seal-image {
  width: 45mm;
  height: 45mm;
  opacity: 0.8;
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

@media print {
  .seal-image {
    width: 40mm;
    height: 40mm;
    top: -35px;
  }
}
</style>
