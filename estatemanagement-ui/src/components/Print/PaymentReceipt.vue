<template>
  <div>
    <!-- 单个收费单 -->
    <div v-if="paymentData" class="payment-receipt">
      <div class="title">俊博物业 收款收据</div>
      
      <!-- 基本信息 -->
      <table class="info-table">
        <tr>
          <td><strong>小区名称：</strong>{{ paymentData.communityName || '' }}</td>
          <td><strong>收据编号：</strong>{{ paymentData.receiptNumber || '' }}</td>
        </tr>
        <tr>
          <td><strong>业主姓名：</strong>{{ paymentData.ownerName || '' }}</td>
          <td><strong>房号：</strong>{{ formatHouseNumber(paymentData) }}</td>
        </tr>
        <tr>
          <td><strong>缴费日期：</strong>{{ formatDate(paymentData.paymentDate) }}</td>
          <td><strong>支付方式：</strong>{{ paymentData.paymentMethod || '' }}</td>
        </tr>
        <tr v-if="paymentData.billNumber">
          <td colspan="2"><strong>关联账单：</strong>{{ paymentData.billNumber }}</td>
        </tr>
      </table>

      <!-- 收费明细 -->
      <div class="subtitle">收费明细</div>
      <table>
        <thead>
          <tr>
            <th class="text-center">收费项目</th>
            <th class="text-center">缴费周期</th>
            <th class="text-center">金额(元)</th>
            <th class="text-center">备注</th>
          </tr>
        </thead>
        <tbody>
          <!-- 使用明细数据显示收费项目和对应的缴费周期 -->
          <template v-if="paymentData && paymentData.details && paymentData.details.length > 0">
            <tr v-for="detail in paymentData.details" :key="detail.id" v-if="detail.payment_amount && detail.payment_amount > 0">
              <td>
                {{ detail.fee_type_name }}
                <span v-if="detail.fee_type == 2 && detail.plate_number" style="color: #666; font-size: 11px;">
                  （{{ detail.plate_number }}）
                </span>
              </td>
              <td class="text-center">{{ formatDate(detail.period_start) }} 至 {{ formatDate(detail.period_end) }}</td>
              <td class="text-right">{{ formatAmount(detail.payment_amount) }}</td>
              <td>{{ detail.remark || '' }}</td>
            </tr>
          </template>
          <!-- 如果没有明细数据，使用原来的显示方式 -->
          <template v-else>
            <tr v-if="paymentData.propertyFeeAmount && paymentData.propertyFeeAmount > 0">
              <td>物业费</td>
              <td class="text-center">{{ getPaymentPeriodText(paymentData, 1) }}</td>
              <td class="text-right">{{ formatAmount(paymentData.propertyFeeAmount) }}</td>
              <td></td>
            </tr>
            <tr v-if="paymentData.parkingFeeAmount && paymentData.parkingFeeAmount > 0">
              <td>停车费</td>
              <td class="text-center">{{ getPaymentPeriodText(paymentData, 2) }}</td>
              <td class="text-right">{{ formatAmount(paymentData.parkingFeeAmount) }}</td>
              <td></td>
            </tr>
            <tr v-if="paymentData.sanitationFeeAmount && paymentData.sanitationFeeAmount > 0">
              <td>卫生费</td>
              <td class="text-center">{{ getPaymentPeriodText(paymentData, 3) }}</td>
              <td class="text-right">{{ formatAmount(paymentData.sanitationFeeAmount) }}</td>
              <td></td>
            </tr>
            <tr v-if="paymentData.elevatorFeeAmount && paymentData.elevatorFeeAmount > 0">
              <td>电梯费</td>
              <td class="text-center">{{ getPaymentPeriodText(paymentData, 4) }}</td>
              <td class="text-right">{{ formatAmount(paymentData.elevatorFeeAmount) }}</td>
              <td></td>
            </tr>
            <tr v-if="paymentData.lateFeeAmount && paymentData.lateFeeAmount > 0">
              <td>滞纳金</td>
              <td class="text-center"></td>
              <td class="text-right">{{ formatAmount(paymentData.lateFeeAmount) }}</td>
              <td></td>
            </tr>
          </template>
          <tr class="font-bold">
            <td>实收金额</td>
            <td></td>
            <td class="text-right">{{ formatAmount(paymentData.paymentAmount) }}</td>
            <td></td>
          </tr>
          <tr class="font-bold">
            <td>合计人民币（大写）</td>
            <td colspan="3">{{ numberToChinese(paymentData.paymentAmount) }}</td>
          </tr>
        </tbody>
      </table>

      <!-- 缴费信息 -->
      <div v-if="hasPaymentInfo(paymentData)" class="subtitle">缴费信息：</div>
      <div v-if="hasPaymentInfo(paymentData)" class="payment-info">
        <p v-if="paymentData.isAdvance === 1">
          <strong>预交费用：</strong>{{ getAdvanceText(paymentData) }}
        </p>
        <p v-if="paymentData.isPartial === 1">
          <strong>部分付款：</strong>本次为部分缴费
        </p>
        <p v-if="paymentData.paymentMonths && paymentData.paymentMonths > 0">
          <strong>缴费月数：</strong>{{ paymentData.paymentMonths }}个月
        </p>
        <p v-if="paymentData.paymentDays && paymentData.paymentDays > 0">
          <strong>缴费天数：</strong>{{ paymentData.paymentDays }}天
        </p>
      </div>

      <!-- 备注 -->
      <div v-if="paymentData.remark" class="subtitle">备注：</div>
      <p v-if="paymentData.remark">{{ paymentData.remark }}</p>

      <!-- 签名区域 -->
      <div class="signature-area">
        <div class="left-info">
          <p><strong>收费时间：</strong>{{ formatDate(paymentData.paymentDate) }}</p>
          <p><strong>收费人：</strong>{{ paymentData.operatorName || '系统' }}</p>
          <p><strong>联系电话：</strong>俊博物业 024-31502355</p>
        </div>
        <div class="date-seal-container">
          <span>日期：{{ getCurrentDate() }}</span>
          <div class="seal-container">
            <span class="seal-text">物业管理处</span>
            <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
          </div>
        </div>
      </div>
    </div>

    <!-- 批量收费单 -->
    <div v-if="paymentList && paymentList.length > 0">
      <div v-for="(payment, index) in paymentList" :key="payment.id" :class="{ 'print-break': index > 0 }">
        <div class="title">俊博物业 收款收据</div>
        
        <!-- 基本信息 -->
        <table class="info-table">
          <tr>
            <td><strong>小区名称：</strong>{{ payment.communityName || '' }}</td>
            <td><strong>收据编号：</strong>{{ payment.receiptNumber || '' }}</td>
          </tr>
          <tr>
            <td><strong>业主姓名：</strong>{{ payment.ownerName || '' }}</td>
            <td><strong>房号：</strong>{{ formatHouseNumber(payment) }}</td>
          </tr>
          <tr>
            <td><strong>缴费日期：</strong>{{ formatDate(payment.paymentDate) }}</td>
            <td><strong>支付方式：</strong>{{ payment.paymentMethod || '' }}</td>
          </tr>
          <tr v-if="payment.billNumber">
            <td colspan="2"><strong>关联账单：</strong>{{ payment.billNumber }}</td>
          </tr>
        </table>

        <!-- 收费明细 -->
        <div class="subtitle">收费明细</div>
        <table>
          <thead>
            <tr>
              <th class="text-center">收费项目</th>
              <th class="text-center">缴费周期</th>
              <th class="text-center">金额(元)</th>
              <th class="text-center">备注</th>
            </tr>
          </thead>
          <tbody>
            <!-- 使用明细数据显示收费项目和对应的缴费周期 -->
            <template v-if="payment && payment.details && payment.details.length > 0">
              <tr v-for="detail in payment.details" :key="detail.id" v-if="detail.payment_amount && detail.payment_amount > 0">
                <td>
                  {{ detail.fee_type_name }}
                  <span v-if="detail.fee_type == 2 && detail.plate_number" style="color: #666; font-size: 11px;">
                    （{{ detail.plate_number }}）
                  </span>
                </td>
                <td class="text-center">{{ formatDate(detail.period_start) }} 至 {{ formatDate(detail.period_end) }}</td>
                <td class="text-right">{{ formatAmount(detail.payment_amount) }}</td>
                <td>{{ detail.remark || '' }}</td>
              </tr>
            </template>
            <!-- 如果没有明细数据，使用原来的显示方式 -->
            <template v-else>
              <tr v-if="payment.propertyFeeAmount && payment.propertyFeeAmount > 0">
                <td>物业费</td>
                <td class="text-center">{{ getPaymentPeriodText(payment, 1) }}</td>
                <td class="text-right">{{ formatAmount(payment.propertyFeeAmount) }}</td>
                <td></td>
              </tr>
              <tr v-if="payment.parkingFeeAmount && payment.parkingFeeAmount > 0">
                <td>停车费</td>
                <td class="text-center">{{ getPaymentPeriodText(payment, 2) }}</td>
                <td class="text-right">{{ formatAmount(payment.parkingFeeAmount) }}</td>
                <td></td>
              </tr>
              <tr v-if="payment.sanitationFeeAmount && payment.sanitationFeeAmount > 0">
                <td>卫生费</td>
                <td class="text-center">{{ getPaymentPeriodText(payment, 3) }}</td>
                <td class="text-right">{{ formatAmount(payment.sanitationFeeAmount) }}</td>
                <td></td>
              </tr>
              <tr v-if="payment.elevatorFeeAmount && payment.elevatorFeeAmount > 0">
                <td>电梯费</td>
                <td class="text-center">{{ getPaymentPeriodText(payment, 4) }}</td>
                <td class="text-right">{{ formatAmount(payment.elevatorFeeAmount) }}</td>
                <td></td>
              </tr>
              <tr v-if="payment.lateFeeAmount && payment.lateFeeAmount > 0">
                <td>滞纳金</td>
                <td class="text-center"></td>
                <td class="text-right">{{ formatAmount(payment.lateFeeAmount) }}</td>
                <td></td>
              </tr>
            </template>
            <tr class="font-bold">
              <td>实收金额</td>
              <td></td>
              <td class="text-right">{{ formatAmount(payment.paymentAmount) }}</td>
              <td></td>
            </tr>
            <tr class="font-bold">
              <td>合计人民币（大写）</td>
              <td colspan="3">{{ numberToChinese(payment.paymentAmount) }}</td>
            </tr>
          </tbody>
        </table>

        <!-- 缴费信息 -->
        <div v-if="hasPaymentInfo(payment)" class="subtitle">缴费信息：</div>
        <div v-if="hasPaymentInfo(payment)" class="payment-info">
          <p v-if="payment.isAdvance === 1">
            <strong>预交费用：</strong>{{ getAdvanceText(payment) }}
          </p>
          <p v-if="payment.isPartial === 1">
            <strong>部分付款：</strong>本次为部分缴费
          </p>
          <p v-if="payment.paymentMonths && payment.paymentMonths > 0">
            <strong>缴费月数：</strong>{{ payment.paymentMonths }}个月
          </p>
          <p v-if="payment.paymentDays && payment.paymentDays > 0">
            <strong>缴费天数：</strong>{{ payment.paymentDays }}天
          </p>
        </div>

        <!-- 备注 -->
        <div v-if="payment.remark" class="subtitle">备注：</div>
        <p v-if="payment.remark">{{ payment.remark }}</p>

        <!-- 签名区域 -->
        <div class="signature-area">
          <div class="left-info">
            <p><strong>收费时间：</strong>{{ formatDate(payment.paymentDate) }}</p>
            <p><strong>收费人：</strong>{{ payment.operatorName || '系统' }}</p>
            <p><strong>联系电话：</strong>俊博物业 024-31502355</p>
          </div>
          <div class="date-seal-container">
            <span>日期：{{ getCurrentDate() }}</span>
            <div class="seal-container">
              <span class="seal-text">物业管理处</span>
              <img src="@/assets/images/公章.png" alt="公章" class="seal-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { numberToChinese } from '@/utils'

export default {
  name: 'PaymentReceipt',
  props: {
    paymentData: {
      type: Object,
      default: null
    },
    paymentList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`
    },
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      const hours = d.getHours().toString().padStart(2, '0')
      const minutes = d.getMinutes().toString().padStart(2, '0')
      const seconds = d.getSeconds().toString().padStart(2, '0')
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
    },
    getCurrentDate() {
      const now = new Date()
      return `${now.getFullYear()}年${(now.getMonth() + 1).toString().padStart(2, '0')}月${now.getDate().toString().padStart(2, '0')}日`
    },
    formatAmount(amount) {
      if (!amount) return '0.00'
      return parseFloat(amount).toFixed(2)
    },
    formatHouseNumber(data) {
      if (!data) return ''
      const building = data.buildingNumber || ''
      const house = data.houseNumber || ''
      if (!building && !house) return '-'
      return building && house ? `${building}-${house}` : (building || house)
    },
    getPaymentPeriodText(paymentData, feeType) {
      if (!paymentData) return ''

      // 如果有缴费月数，显示月数信息
      if (paymentData.paymentMonths && paymentData.paymentMonths > 0) {
        return `${paymentData.paymentMonths}个月`
      }

      // 如果有缴费天数，显示天数信息
      if (paymentData.paymentDays && paymentData.paymentDays > 0) {
        return `${paymentData.paymentDays}天`
      }

      // 如果是预交费用
      if (paymentData.isAdvance === 1) {
        return '预交费用'
      }

      // 如果是部分付款
      if (paymentData.isPartial === 1) {
        return '部分付款'
      }

      return ''
    },
    hasPaymentInfo(paymentData) {
      if (!paymentData) return false
      return paymentData.isAdvance === 1 ||
             paymentData.isPartial === 1 ||
             (paymentData.paymentMonths && paymentData.paymentMonths > 0) ||
             (paymentData.paymentDays && paymentData.paymentDays > 0)
    },
    getAdvanceText(paymentData) {
      if (!paymentData) return ''
      let text = '提前缴费'
      if (paymentData.paymentMonths && paymentData.paymentMonths > 0) {
        text += `，预交${paymentData.paymentMonths}个月`
      }
      return text
    },
    numberToChinese(amount) {
      return numberToChinese(amount)
    }
  }
}
</script>

<style scoped>
.payment-receipt {
  padding: 15px;
  margin: 0 auto;
  max-width: 95%;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
  background: white;
  page-break-after: always;
  box-sizing: border-box;
}

.payment-receipt:last-child {
  page-break-after: auto;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 20px;
}

.left-info {
  flex: 1;
  font-size: 12px;
}

.left-info p {
  margin: 3px 0;
  line-height: 1.4;
}

.date-seal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.seal-text {
  margin-bottom: 5px;
  font-weight: bold;
}

.seal-container {
  display: inline-block;
  position: relative;
  margin-top: 5px;
}

.seal-text {
  font-weight: bold;
  position: relative;
  z-index: 2;
  color: #333;
  display: inline-block;
}

.seal-image {
  width: 45mm;
  height: 45mm;
  opacity: 0.8;
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

@media print {
  .seal-image {
    width: 40mm;
    height: 40mm;
    top: -35px;
  }
}

.payment-info {
  margin: 10px 0;
  padding: 8px;
  background-color: #f9f9f9;
  border-left: 3px solid #409eff;
}

.payment-info p {
  margin: 4px 0;
  font-size: 12px;
}

@media print {
  .payment-info {
    background-color: transparent;
    border-left: 2px solid #333;
    padding: 4px;
  }
}
</style>
