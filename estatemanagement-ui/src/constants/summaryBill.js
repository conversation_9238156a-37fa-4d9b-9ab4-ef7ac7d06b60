/**
 * 汇总账单相关常量
 */

// 支付状态
export const PAYMENT_STATUS = {
  UNPAID: 0,        // 未支付
  PARTIAL_PAID: 1,  // 部分支付
  PAID: 2,          // 已支付
  MERGED: 3         // 已合并（历史欠费）
}

// 上交状态
export const SUBMIT_STATUS = {
  NOT_SUBMITTED: 0, // 未上交
  SUBMITTED: 1      // 已上交
}

// 财务确认状态
export const FINANCE_CONFIRM_STATUS = {
  NOT_CONFIRMED: 0, // 未确认
  CONFIRMED: 1      // 已确认
}

// 收钱确认状态
export const RECEIVE_STATUS = {
  NOT_RECEIVED: 0,  // 未确认收钱
  RECEIVED: 1       // 已确认收钱
}

// 自动生成状态
export const AUTO_GENERATE_STATUS = {
  DISABLED: 0,      // 不自动生成
  ENABLED: 1        // 自动生成
}

// 配置状态
export const CONFIG_STATUS = {
  DISABLED: 0,      // 停用
  ENABLED: 1        // 启用
}

// 任务状态
export const TASK_STATUS = {
  RUNNING: 'RUNNING',     // 运行中
  COMPLETED: 'COMPLETED', // 已完成
  FAILED: 'FAILED'        // 失败
}

// 状态文本映射
export const STATUS_TEXT = {
  SUBMIT_STATUS: {
    [SUBMIT_STATUS.NOT_SUBMITTED]: '未上交',
    [SUBMIT_STATUS.SUBMITTED]: '已上交'
  },
  FINANCE_CONFIRM_STATUS: {
    [FINANCE_CONFIRM_STATUS.NOT_CONFIRMED]: '未确认',
    [FINANCE_CONFIRM_STATUS.CONFIRMED]: '已确认'
  },
  RECEIVE_STATUS: {
    [RECEIVE_STATUS.NOT_RECEIVED]: '未确认',
    [RECEIVE_STATUS.RECEIVED]: '已确认'
  },
  PAYMENT_STATUS: {
    [PAYMENT_STATUS.UNPAID]: '未支付',
    [PAYMENT_STATUS.PARTIAL_PAID]: '部分支付',
    [PAYMENT_STATUS.PAID]: '已支付',
    [PAYMENT_STATUS.MERGED]: '已合并'
  },
  AUTO_GENERATE_STATUS: {
    [AUTO_GENERATE_STATUS.DISABLED]: '已停用',
    [AUTO_GENERATE_STATUS.ENABLED]: '已启用'
  },
  CONFIG_STATUS: {
    [CONFIG_STATUS.DISABLED]: '已停用',
    [CONFIG_STATUS.ENABLED]: '已启用'
  }
}

// 状态标签类型映射
export const STATUS_TAG_TYPE = {
  SUBMIT_STATUS: {
    [SUBMIT_STATUS.NOT_SUBMITTED]: 'warning',
    [SUBMIT_STATUS.SUBMITTED]: 'success'
  },
  FINANCE_CONFIRM_STATUS: {
    [FINANCE_CONFIRM_STATUS.NOT_CONFIRMED]: 'warning',
    [FINANCE_CONFIRM_STATUS.CONFIRMED]: 'success'
  },
  RECEIVE_STATUS: {
    [RECEIVE_STATUS.NOT_RECEIVED]: 'danger',
    [RECEIVE_STATUS.RECEIVED]: 'success'
  },
  PAYMENT_STATUS: {
    [PAYMENT_STATUS.UNPAID]: 'danger',
    [PAYMENT_STATUS.PARTIAL_PAID]: 'warning',
    [PAYMENT_STATUS.PAID]: 'success',
    [PAYMENT_STATUS.MERGED]: 'info'
  }
}

// 日期格式
export const DATE_FORMAT = {
  DATE_SHORT: 'yyyyMMdd',
  DATETIME_SHORT: 'yyyyMMddHHmmss',
  DATE_STANDARD: 'yyyy-MM-dd',
  DATETIME_STANDARD: 'yyyy-MM-dd HH:mm:ss'
}

// 汇总账单编号前缀
export const SUMMARY_NUMBER_PREFIX = 'SUM'

// 任务ID前缀
export const TASK_ID_PREFIX = 'SUMMARY_'

// 全部小区标识
export const ALL_COMMUNITIES = 'ALL'
