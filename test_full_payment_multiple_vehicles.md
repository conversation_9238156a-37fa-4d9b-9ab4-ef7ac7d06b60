# 全部支付多车辆测试文档

## 修改内容总结

### 问题描述
在全部支付（full）时，如果业主有多辆车，系统只生成一条停车费的收费明细记录，没有为每辆车分别生成记录，导致车牌号信息丢失。

### 解决方案
修改了 `PaymentRecordServiceImpl.java` 中的以下方法：

1. **generateFullPaymentDetails** - 修改停车费处理逻辑，调用新的专门方法
2. **generateParkingFeeDetailsForFullPayment** - 新增方法，专门处理多车辆停车费明细生成

### 核心逻辑

#### 1. 查询业主所有有效车辆
```java
ParkingInfo queryParking = new ParkingInfo();
queryParking.setOwnerId(paymentRecord.getOwnerId());
queryParking.setStatus(1); // 有效状态
List<ParkingInfo> parkingList = parkingInfoMapper.selectParkingInfoList(queryParking);
```

#### 2. 过滤有效车牌
```java
List<ParkingInfo> validParkingList = parkingList.stream()
    .filter(parking -> parking.getPlateNumber() != null && !parking.getPlateNumber().trim().isEmpty())
    .collect(java.util.stream.Collectors.toList());
```

#### 3. 平均分配停车费
```java
BigDecimal feePerVehicle = totalParkingFee.divide(new BigDecimal(validParkingList.size()), 2, RoundingMode.HALF_UP);
```

#### 4. 为每辆车生成收费明细
- 每辆车生成单独的 PaymentDetail 记录
- 记录对应的车牌号信息
- 最后一辆车使用剩余金额，避免精度问题

### 测试场景

#### 场景1：业主有2辆车
- 车牌号：粤A12345, 粤B67890
- 停车费总额：200元
- 预期结果：生成2条收费明细，每条100元，分别记录对应车牌号

#### 场景2：业主有3辆车
- 车牌号：粤A11111, 粤B22222, 粤C33333
- 停车费总额：300元
- 预期结果：生成3条收费明细，每条100元，分别记录对应车牌号

#### 场景3：业主没有车辆信息
- 预期结果：生成1条收费明细，不记录车牌号

#### 场景4：停车费金额不能整除
- 车牌号：粤A12345, 粤B67890
- 停车费总额：100.01元
- 预期结果：生成2条收费明细，第一辆车50.01元，第二辆车50.00元

### 验证方法

1. **数据库验证**
   ```sql
   SELECT pd.*, pd.plate_number 
   FROM payment_detail pd 
   WHERE pd.payment_id = ? AND pd.fee_type = 2
   ORDER BY pd.id;
   ```

2. **前端验证**
   - 查看收费明细页面，确认每辆车都有单独记录
   - 确认车牌号字段正确显示

3. **打印收据验证**
   - 收据上应显示每辆车的详细信息
   - 总金额应等于各车辆金额之和

### 注意事项

1. **向后兼容性**：如果没有找到车辆信息，会降级到原有逻辑
2. **精度处理**：使用剩余金额分配给最后一辆车，避免精度问题
3. **异常处理**：包含完整的异常处理和降级逻辑
4. **性能考虑**：只查询有效状态的车辆信息

### 相关文件
- `estatemanagement-system/src/main/java/com/estatemanagement/system/service/impl/PaymentRecordServiceImpl.java`
- `estatemanagement-system/src/main/java/com/estatemanagement/system/domain/PaymentDetail.java`
- `estatemanagement-system/src/main/resources/mapper/system/PaymentDetailMapper.xml`
